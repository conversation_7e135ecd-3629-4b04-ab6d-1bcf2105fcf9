"""
Entry Mode Serializers
"""
from rest_framework import serializers
from ..models import EntryMode


class EntryModeSerializer(serializers.ModelSerializer):
    """Serializer for EntryMode model"""
    
    usage_count = serializers.ReadOnlyField()
    
    class Meta:
        model = EntryMode
        fields = [
            'id',
            'name',
            'description',
            'code',
            'is_default',
            'requires_approval',
            'allows_negative_stock',
            'is_active',
            'usage_count',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'code']

    def validate_name(self, value):
        """Validate name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
        return value

    def validate(self, data):
        """Custom validation"""
        # Check if trying to set multiple defaults
        if data.get('is_default', False):
            existing_default = EntryMode.objects.filter(
                is_default=True, 
                is_active=True
            ).exclude(pk=self.instance.pk if self.instance else None).first()
            
            if existing_default:
                raise serializers.ValidationError({
                    'is_default': f'Another entry mode "{existing_default.name}" is already set as default'
                })
        
        return data


class EntryModeListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing entry modes"""
    
    usage_count = serializers.ReadOnlyField()
    
    class Meta:
        model = EntryMode
        fields = [
            'id',
            'name',
            'code',
            'is_default',
            'requires_approval',
            'allows_negative_stock',
            'is_active',
            'usage_count'
        ]


class EntryModeDropdownSerializer(serializers.ModelSerializer):
    """Minimal serializer for dropdown/select options"""
    
    label = serializers.CharField(source='name')
    value = serializers.CharField(source='id')
    
    class Meta:
        model = EntryMode
        fields = ['value', 'label', 'code', 'requires_approval']


class EntryModeCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating entry modes with code auto-generation"""
    
    class Meta:
        model = EntryMode
        fields = [
            'name',
            'description',
            'is_default',
            'requires_approval',
            'allows_negative_stock',
            'is_active'
        ]

    def validate_name(self, value):
        """Validate name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
        return value
