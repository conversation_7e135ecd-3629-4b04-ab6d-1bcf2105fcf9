"""
Approval Status Model for Inventory Setup
"""
import uuid
from django.db import models
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _


class ApprovalStatus(models.Model):
    """
    Model for approval status tracking (Pending, Approved, Rejected, etc.)
    """
    
    # Status type choices
    STATUS_TYPES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('cancelled', 'Cancelled'),
        ('on_hold', 'On Hold'),
    ]
    
    # Predefined color choices
    COLOR_CHOICES = [
        ('#4CAF50', 'Green - Approved/Success'),
        ('#F44336', 'Red - Rejected/Failed'),
        ('#FF9800', 'Orange - Pending/Warning'),
        ('#2196F3', 'Blue - In Review'),
        ('#9C27B0', 'Purple - On Hold'),
        ('#607D8B', 'Gray - Cancelled/Inactive'),
        ('#795548', 'Brown - Archived'),
        ('#E91E63', 'Pink - Special Status'),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text=_("Unique identifier for the approval status")
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        null=True,
        blank=True,
        validators=[RegexValidator(
            regex=r'^[A-Z0-9_-]+$',
            message=_("Code must contain only uppercase letters, numbers, underscores, and hyphens")
        )],
        verbose_name=_("Status Code"),
        help_text=_("Unique code for the status (e.g., 'PENDING', 'APPROVED', 'REJECTED')")
    )
    
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_("Status Name"),
        help_text=_("Human-readable name for the approval status")
    )
    
    description = models.TextField(
        blank=True,
        verbose_name=_("Description"),
        help_text=_("Detailed description of what this approval status means")
    )
    
    status_type = models.CharField(
        max_length=20,
        choices=STATUS_TYPES,
        default='pending',
        verbose_name=_("Status Type"),
        help_text=_("Category of this approval status")
    )
    
    color_code = models.CharField(
        max_length=7,
        choices=COLOR_CHOICES,
        default='#FF9800',
        validators=[RegexValidator(
            regex=r'^#[0-9A-Fa-f]{6}$',
            message=_("Color code must be a valid hex color (e.g., #FF0000)")
        )],
        verbose_name=_("Color Code"),
        help_text=_("Hex color code for displaying this status")
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("Whether this status is available for use")
    )

    is_default = models.BooleanField(
        default=False,
        verbose_name=_("Default Status"),
        help_text=_("Whether this is the default approval status")
    )

    is_final = models.BooleanField(
        default=False,
        verbose_name=_("Final Status"),
        help_text=_("Whether this status represents a final decision")
    )

    allows_modification = models.BooleanField(
        default=True,
        verbose_name=_("Allows Modification"),
        help_text=_("Whether items with this status can be modified")
    )

    requires_comment = models.BooleanField(
        default=False,
        verbose_name=_("Requires Comment"),
        help_text=_("Whether setting this status requires a comment")
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Created At"),
        help_text=_("Date and time when the record was created")
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_("Updated At"),
        help_text=_("Date and time when the record was last updated")
    )

    class Meta:
        verbose_name = _("Approval Status")
        verbose_name_plural = _("Approval Statuses")
        db_table = 'inventory_approval_status'
        ordering = ['name']
        indexes = [
            models.Index(fields=['code'], name='inv_appr_status_code_idx'),
            models.Index(fields=['is_active'], name='inv_appr_status_active_idx'),
            models.Index(fields=['status_type'], name='inv_appr_status_type_idx'),
        ]

    def __str__(self):
        return f"{self.name}" + (f" ({self.code})" if self.code else "")

    def save(self, *args, **kwargs):
        # Auto-generate code if not provided
        if not self.code and self.name:
            from django.utils.text import slugify
            self.code = slugify(self.name).upper().replace('-', '_')
        
        # Ensure only one default status
        if self.is_default:
            ApprovalStatus.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)

    @property
    def usage_count(self):
        """Count of items using this approval status"""
        # This will be implemented when item model is created
        return 0

    @property
    def display_name(self):
        """Display name with code if available"""
        return f"{self.name}" + (f" ({self.code})" if self.code else "")

    def clean(self):
        """Validate the model"""
        from django.core.exceptions import ValidationError
        
        if self.code:
            self.code = self.code.upper()
        
        # Validate color code format
        if self.color_code and not self.color_code.startswith('#'):
            raise ValidationError({'color_code': _("Color code must start with #")})

    @classmethod
    def get_default_status(cls):
        """Get the default approval status"""
        return cls.objects.filter(is_default=True, is_active=True).first()

    @classmethod
    def create_default_statuses(cls):
        """Create default approval statuses if they don't exist"""
        default_statuses = [
            {
                'code': 'PENDING',
                'name': 'Pending Approval',
                'description': 'Request is pending approval',
                'status_type': 'pending',
                'color_code': '#FF9800',
                'is_default': True,
                'allows_modification': True
            },
            {
                'code': 'UNDER_REVIEW',
                'name': 'Under Review',
                'description': 'Request is currently being reviewed',
                'status_type': 'pending',
                'color_code': '#2196F3',
                'allows_modification': False
            },
            {
                'code': 'APPROVED',
                'name': 'Approved',
                'description': 'Request has been approved',
                'status_type': 'approved',
                'color_code': '#4CAF50',
                'is_final': True,
                'allows_modification': False
            },
            {
                'code': 'REJECTED',
                'name': 'Rejected',
                'description': 'Request has been rejected',
                'status_type': 'rejected',
                'color_code': '#F44336',
                'is_final': True,
                'allows_modification': False,
                'requires_comment': True
            },
            {
                'code': 'ON_HOLD',
                'name': 'On Hold',
                'description': 'Request is temporarily on hold',
                'status_type': 'on_hold',
                'color_code': '#9C27B0',
                'allows_modification': False,
                'requires_comment': True
            },
            {
                'code': 'CANCELLED',
                'name': 'Cancelled',
                'description': 'Request has been cancelled',
                'status_type': 'cancelled',
                'color_code': '#607D8B',
                'is_final': True,
                'allows_modification': False
            }
        ]
        
        for status_data in default_statuses:
            cls.objects.get_or_create(
                code=status_data['code'],
                defaults=status_data
            )


