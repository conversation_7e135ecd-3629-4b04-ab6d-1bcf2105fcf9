"""
Sub Classification Views
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from ..models import SubClassification
from ..serializers import (
    SubClassificationSerializer,
    SubClassificationListSerializer,
    SubClassificationDropdownSerializer,
    SubClassificationCreateSerializer
)


class SubClassificationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Sub Classifications
    
    Provides CRUD operations for sub classifications with filtering,
    searching, and dropdown endpoints.
    """
    queryset = SubClassification.objects.select_related('main_class').all()
    serializer_class = SubClassificationSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'main_class']
    search_fields = ['code', 'name', 'description', 'main_class__name', 'main_class__code']
    ordering_fields = ['code', 'name', 'main_class__code', 'created_at', 'updated_at']
    ordering = ['main_class__code', 'code']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return SubClassificationListSerializer
        elif self.action == 'dropdown':
            return SubClassificationDropdownSerializer
        elif self.action == 'create':
            return SubClassificationCreateSerializer
        return SubClassificationSerializer

    def get_queryset(self):
        """Filter queryset based on action and parameters"""
        queryset = SubClassification.objects.select_related('main_class').all()
        
        if self.action == 'dropdown':
            # Only return active items for dropdown
            queryset = queryset.filter(is_active=True, main_class__is_active=True)
            
            # Filter by main_class if provided
            main_class_id = self.request.query_params.get('main_class')
            if main_class_id:
                queryset = queryset.filter(main_class_id=main_class_id)
        
        return queryset

    @swagger_auto_schema(
        method='get',
        operation_description="Get sub classifications for dropdown/select options",
        manual_parameters=[
            openapi.Parameter(
                'main_class',
                openapi.IN_QUERY,
                description="Filter by main classification ID",
                type=openapi.TYPE_STRING
            )
        ],
        responses={
            200: openapi.Response(
                description="List of sub classifications for dropdown",
                examples={
                    "application/json": [
                        {
                            "value": "uuid-here",
                            "label": "ELEC-001 - Computers",
                            "main_class_id": "main-uuid",
                            "full_code": "ELEC-001"
                        }
                    ]
                }
            )
        }
    )
    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get sub classifications formatted for dropdown/select options"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='get',
        operation_description="Get sub classifications by main classification",
        responses={
            200: openapi.Response(
                description="List of sub classifications for the main classification"
            )
        }
    )
    @action(detail=False, methods=['get'], url_path='by-main-class/(?P<main_class_id>[^/.]+)')
    def by_main_class(self, request, main_class_id=None):
        """Get sub classifications by main classification ID"""
        queryset = self.get_queryset().filter(main_class_id=main_class_id)
        serializer = SubClassificationListSerializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='post',
        operation_description="Bulk activate/deactivate sub classifications",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'ids': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description='List of sub classification IDs'
                ),
                'is_active': openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    description='Active status to set'
                )
            },
            required=['ids', 'is_active']
        )
    )
    @action(detail=False, methods=['post'])
    def bulk_update_status(self, request):
        """Bulk update active status of sub classifications"""
        ids = request.data.get('ids', [])
        is_active = request.data.get('is_active')
        
        if not ids:
            return Response(
                {'error': 'No IDs provided'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if is_active is None:
            return Response(
                {'error': 'is_active field is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        updated_count = SubClassification.objects.filter(
            id__in=ids
        ).update(is_active=is_active)
        
        return Response({
            'message': f'Updated {updated_count} sub classifications',
            'updated_count': updated_count
        })

    def destroy(self, request, *args, **kwargs):
        """Soft delete sub classification"""
        instance = self.get_object()
        instance.soft_delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
