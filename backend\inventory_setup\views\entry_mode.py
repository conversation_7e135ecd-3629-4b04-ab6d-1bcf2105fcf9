"""
Entry Mode Views
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from ..models import EntryMode
from ..serializers import (
    EntryModeSerializer,
    EntryModeListSerializer,
    EntryModeDropdownSerializer,
    EntryModeCreateSerializer
)


class EntryModeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Entry Modes
    
    Provides CRUD operations for entry modes with filtering,
    searching, and dropdown endpoints.
    """
    queryset = EntryMode.objects.all()
    serializer_class = EntryModeSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'is_default', 'requires_approval', 'allows_negative_stock']
    search_fields = ['name', 'description', 'code']
    ordering_fields = ['name', 'code', 'created_at', 'updated_at']
    ordering = ['name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return EntryModeListSerializer
        elif self.action == 'dropdown':
            return EntryModeDropdownSerializer
        elif self.action == 'create':
            return EntryModeCreateSerializer
        return EntryModeSerializer

    def get_queryset(self):
        """Filter queryset based on action"""
        queryset = EntryMode.objects.all()
        
        if self.action == 'dropdown':
            # Only return active items for dropdown
            queryset = queryset.filter(is_active=True)
        
        return queryset

    @swagger_auto_schema(
        method='get',
        operation_description="Get entry modes for dropdown/select options",
        responses={
            200: openapi.Response(
                description="List of entry modes for dropdown",
                examples={
                    "application/json": [
                        {
                            "value": "uuid-here",
                            "label": "Purchase",
                            "code": "PURCHASE",
                            "requires_approval": False
                        }
                    ]
                }
            )
        }
    )
    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get entry modes formatted for dropdown/select options"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='get',
        operation_description="Get the default entry mode",
        responses={
            200: openapi.Response(
                description="Default entry mode",
                schema=EntryModeSerializer
            ),
            404: openapi.Response(description="No default entry mode found")
        }
    )
    @action(detail=False, methods=['get'])
    def default(self, request):
        """Get the default entry mode"""
        default_mode = EntryMode.get_default()
        if default_mode:
            serializer = EntryModeSerializer(default_mode)
            return Response(serializer.data)
        return Response(
            {'error': 'No default entry mode found'}, 
            status=status.HTTP_404_NOT_FOUND
        )

    @swagger_auto_schema(
        method='post',
        operation_description="Set an entry mode as default",
        responses={
            200: openapi.Response(description="Entry mode set as default"),
            404: openapi.Response(description="Entry mode not found")
        }
    )
    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """Set this entry mode as the default"""
        instance = self.get_object()
        
        # Remove default from all other entry modes
        EntryMode.objects.filter(is_default=True).update(is_default=False)
        
        # Set this one as default
        instance.is_default = True
        instance.save(update_fields=['is_default', 'updated_at'])
        
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='post',
        operation_description="Bulk activate/deactivate entry modes",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'ids': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description='List of entry mode IDs'
                ),
                'is_active': openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    description='Active status to set'
                )
            },
            required=['ids', 'is_active']
        )
    )
    @action(detail=False, methods=['post'])
    def bulk_update_status(self, request):
        """Bulk update active status of entry modes"""
        ids = request.data.get('ids', [])
        is_active = request.data.get('is_active')
        
        if not ids:
            return Response(
                {'error': 'No IDs provided'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if is_active is None:
            return Response(
                {'error': 'is_active field is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        updated_count = EntryMode.objects.filter(
            id__in=ids
        ).update(is_active=is_active)
        
        return Response({
            'message': f'Updated {updated_count} entry modes',
            'updated_count': updated_count
        })

    def destroy(self, request, *args, **kwargs):
        """Soft delete entry mode"""
        instance = self.get_object()
        
        # Check if this is the default entry mode
        if instance.is_default:
            return Response(
                {'error': 'Cannot delete the default entry mode'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        instance.soft_delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
