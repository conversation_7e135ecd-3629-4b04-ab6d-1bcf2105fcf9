import { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  CircularProgress,
  Breadcrumbs,
  Link,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  Add as AddIcon,
  Category as CategoryIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Home as HomeIcon,
  Storage as StorageIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { Link as RouterLink } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import api from '../../utils/axios';

const StoreTypesList = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  
  const [storeTypes, setStoreTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [formDialog, setFormDialog] = useState({ open: false, storeType: null });
  const [deleteDialog, setDeleteDialog] = useState({ open: false, storeType: null });
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: true
  });

  useEffect(() => {
    loadStoreTypes();
  }, []);

  const loadStoreTypes = async () => {
    setLoading(true);
    try {
      const response = await api.get('/organization/store-types/');
      setStoreTypes(response.data.results || response.data);
    } catch (error) {
      console.error('Error loading store types:', error);
      enqueueSnackbar('Failed to load store types', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenForm = (storeType = null) => {
    if (storeType) {
      setFormData({
        name: storeType.name,
        description: storeType.description || '',
        is_active: storeType.is_active
      });
    } else {
      setFormData({
        name: '',
        description: '',
        is_active: true
      });
    }
    setFormDialog({ open: true, storeType });
  };

  const handleCloseForm = () => {
    setFormDialog({ open: false, storeType: null });
    setFormData({ name: '', description: '', is_active: true });
  };

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      enqueueSnackbar('Store type name is required', { variant: 'error' });
      return;
    }

    setSaving(true);
    try {
      const submitData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        is_active: formData.is_active
      };

      if (formDialog.storeType) {
        await api.put(`/organization/store-types/${formDialog.storeType.id}/`, submitData);
        enqueueSnackbar('Store type updated successfully', { variant: 'success' });
      } else {
        await api.post('/organization/store-types/', submitData);
        enqueueSnackbar('Store type created successfully', { variant: 'success' });
      }
      
      handleCloseForm();
      loadStoreTypes();
    } catch (error) {
      console.error('Error saving store type:', error);
      enqueueSnackbar('Failed to save store type', { variant: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    try {
      await api.delete(`/organization/store-types/${deleteDialog.storeType.id}/`);
      enqueueSnackbar('Store type deleted successfully', { variant: 'success' });
      setDeleteDialog({ open: false, storeType: null });
      loadStoreTypes();
    } catch (error) {
      console.error('Error deleting store type:', error);
      enqueueSnackbar('Failed to delete store type', { variant: 'error' });
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 3 }}>
            Loading store types...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Link
          component={RouterLink}
          to="/storage-menu"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <StorageIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Storage Management
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <CategoryIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Store Types
        </Typography>
      </Breadcrumbs>

      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box display="flex" alignItems="center">
          <CategoryIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
          <Typography variant="h4" component="h1" fontWeight="bold">
            Store Types
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadStoreTypes}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenForm()}
          >
            Add Store Type
          </Button>
        </Box>
      </Box>

      {/* Store Types Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Stores Count</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {storeTypes.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    No store types found. Create your first store type to get started.
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              storeTypes.map((storeType) => (
                <TableRow key={storeType.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {storeType.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {storeType.description || 'No description'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {storeType.stores_count || 0} stores
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={storeType.is_active ? 'Active' : 'Inactive'}
                      color={storeType.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(storeType.created_at).toLocaleDateString()}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Edit Store Type">
                        <IconButton
                          size="small"
                          onClick={() => handleOpenForm(storeType)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Store Type">
                        <IconButton
                          size="small"
                          onClick={() => setDeleteDialog({ open: true, storeType })}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Form Dialog */}
      <Dialog open={formDialog.open} onClose={handleCloseForm} maxWidth="sm" fullWidth>
        <DialogTitle>
          {formDialog.storeType ? 'Edit Store Type' : 'Create Store Type'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              margin="normal"
              multiline
              rows={3}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                />
              }
              label="Active"
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseForm} disabled={saving}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} variant="contained" disabled={saving}>
            {saving ? 'Saving...' : (formDialog.storeType ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, storeType: null })}>
        <DialogTitle>Delete Store Type</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the store type "{deleteDialog.storeType?.name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, storeType: null })}>
            Cancel
          </Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default StoreTypesList;
