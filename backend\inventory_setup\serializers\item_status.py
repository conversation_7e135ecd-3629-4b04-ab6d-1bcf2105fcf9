"""
Item Status Serializers
"""
from rest_framework import serializers
from ..models import ItemStatus


class ItemStatusSerializer(serializers.ModelSerializer):
    """Serializer for ItemStatus model"""
    
    usage_count = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = ItemStatus
        fields = [
            'id',
            'code',
            'name',
            'description',
            'status_type',
            'color_code',
            'is_active',
            'is_default',
            'allows_checkout',
            'requires_approval',
            'display_name',
            'usage_count',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_code(self, value):
        """Validate code"""
        if value:
            value = value.upper().strip()
            if len(value) < 2:
                raise serializers.ValidationError("Code must be at least 2 characters long")
        return value

    def validate_name(self, value):
        """Validate name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
            if len(value) > 100:
                raise serializers.ValidationError("Name cannot exceed 100 characters")
        return value

    def validate_color_code(self, value):
        """Validate color code format"""
        if value:
            import re
            if not re.match(r'^#[0-9A-Fa-f]{6}$', value):
                raise serializers.ValidationError("Color code must be a valid hex color (e.g., #FF0000)")
        return value

    def validate(self, data):
        """Cross-field validation"""
        # Check for duplicate names (excluding current instance)
        name = data.get('name')
        if name:
            queryset = ItemStatus.objects.filter(name__iexact=name)
            if self.instance:
                queryset = queryset.exclude(pk=self.instance.pk)
            if queryset.exists():
                raise serializers.ValidationError({'name': 'An item status with this name already exists'})

        # Check for duplicate codes (excluding current instance)
        code = data.get('code')
        if code:
            queryset = ItemStatus.objects.filter(code__iexact=code)
            if self.instance:
                queryset = queryset.exclude(pk=self.instance.pk)
            if queryset.exists():
                raise serializers.ValidationError({'code': 'An item status with this code already exists'})

        return data


class ItemStatusListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing item statuses"""
    
    usage_count = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = ItemStatus
        fields = [
            'id',
            'code',
            'name',
            'status_type',
            'color_code',
            'is_active',
            'is_default',
            'allows_checkout',
            'display_name',
            'usage_count'
        ]


class ItemStatusDropdownSerializer(serializers.ModelSerializer):
    """Minimal serializer for dropdown/select options"""
    
    label = serializers.SerializerMethodField()
    value = serializers.CharField(source='id')
    
    class Meta:
        model = ItemStatus
        fields = ['value', 'label', 'color_code']
    
    def get_label(self, obj):
        return f"{obj.name} ({obj.code})"


class ItemStatusCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating item statuses"""
    
    class Meta:
        model = ItemStatus
        fields = [
            'code',
            'name',
            'description',
            'status_type',
            'color_code',
            'is_active',
            'is_default',
            'allows_checkout',
            'requires_approval'
        ]

    def validate_code(self, value):
        """Validate and format code"""
        if value:
            value = value.upper().strip()
            if len(value) < 2:
                raise serializers.ValidationError("Code must be at least 2 characters long")
        return value
