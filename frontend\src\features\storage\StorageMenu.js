import { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  Paper,
  Chip,
  useTheme,
  alpha,
  Breadcrumbs,
  Link,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Warehouse as WarehouseIcon,
  Store as StoreIcon,
  Shelves as ShelfIcon,
  Category as StoreTypeIcon,
  Insights as InsightsIcon,
  Home as HomeIcon,
  Dashboard as DashboardIcon,
  Storage as StorageIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';

const StorageMenu = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [counts, setCounts] = useState({
    storeTypes: 0,
    stores: 0,
    shelves: 0
  });

  useEffect(() => {
    loadStorageCounts();
  }, []);

  const loadStorageCounts = async () => {
    setLoading(true);
    try {
      // TODO: Implement API calls to get actual counts
      // For now, using placeholder counts
      setCounts({
        storeTypes: 5,
        stores: 12,
        shelves: 48
      });
    } catch (err) {
      console.error('Error loading storage counts:', err);
      setError('Failed to load storage data');
    } finally {
      setLoading(false);
    }
  };

  // Storage management cards data
  const storageCards = [
    {
      id: 'store-types',
      title: 'Store Types',
      description: 'Define and manage different types of storage facilities',
      icon: <StoreTypeIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.primary.main,
      path: '/storage/store-types',
      count: counts.storeTypes,
      adminOnly: false
    },
    {
      id: 'stores',
      title: 'Stores',
      description: 'Manage physical storage locations and warehouses',
      icon: <StoreIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.secondary.main,
      path: '/storage/stores',
      count: counts.stores,
      adminOnly: false
    },
    {
      id: 'shelves',
      title: 'Shelves',
      description: 'Organize and manage storage shelves within stores',
      icon: <ShelfIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.info.main,
      path: '/storage/shelves',
      count: counts.shelves,
      adminOnly: false
    },
    {
      id: 'storage-layout',
      title: 'Storage Layout',
      description: 'Visualize and manage storage facility layouts',
      icon: <WarehouseIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.success.main,
      path: '/storage/layout',
      count: 'New',
      adminOnly: false
    }
  ];

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 3 }}>
            Loading storage management...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <StorageIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Storage Management
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          Storage Management
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Manage storage facilities, organize locations, and optimize warehouse operations
        </Typography>
      </Box>

      {/* Info Paper */}
      <Paper
        sx={{
          p: 2,
          mb: 4,
          display: 'flex',
          alignItems: 'center',
          bgcolor: alpha(theme.palette.success.main, 0.1),
          border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
          borderRadius: 2
        }}
      >
        <InsightsIcon color="success" sx={{ mr: 2 }} />
        <Box>
          <Typography variant="h6" color="success.main">
            Storage Management Center
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Configure store types, manage storage locations, organize shelves, and optimize warehouse layouts
          </Typography>
        </Box>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Storage Management Cards */}
      <Grid container spacing={3}>
        {storageCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card
              component={RouterLink}
              to={card.path}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0,0,0,0.1)',
                },
                textDecoration: 'none',
                borderTop: `4px solid ${card.color}`,
                borderRadius: 2,
                position: 'relative',
                overflow: 'visible'
              }}
            >
              {card.count && (
                <Chip
                  label={card.count}
                  color="primary"
                  size="small"
                  sx={{
                    position: 'absolute',
                    top: -10,
                    right: 16,
                    fontWeight: 'bold',
                    boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
                  }}
                />
              )}
              <CardContent sx={{ flexGrow: 1, p: 3 }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: alpha(card.color, 0.1),
                    mb: 2,
                    mx: 'auto'
                  }}
                >
                  {card.icon}
                </Box>
                <Typography variant="h6" component="h2" gutterBottom textAlign="center" fontWeight="bold">
                  {card.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" textAlign="center">
                  {card.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Quick Stats */}
      <Paper sx={{ p: 3, mt: 4, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          Storage Overview
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={4}>
            <Box textAlign="center">
              <Typography variant="h4" color="primary.main" fontWeight="bold">
                {counts.storeTypes}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Store Types
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Box textAlign="center">
              <Typography variant="h4" color="secondary.main" fontWeight="bold">
                {counts.stores}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Stores
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Box textAlign="center">
              <Typography variant="h4" color="info.main" fontWeight="bold">
                {counts.shelves}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Shelves
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default StorageMenu;
