/**
 * Serial Vouchers List Component
 * Manages serial voucher categories and vouchers
 */
import { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  CircularProgress,
  Alert,
  Breadcrumbs,
  Link,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import {
  Add as AddIcon,
  Receipt as ReceiptIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Home as HomeIcon,
  Refresh as RefreshIcon,
  Preview as PreviewIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { Link as RouterLink } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import api from '../../utils/axios';

const SerialVouchersList = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  const [vouchers, setVouchers] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialog, setDeleteDialog] = useState({ open: false, voucher: null });
  const [previewDialog, setPreviewDialog] = useState({ open: false, voucher: null });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [vouchersResponse, categoriesResponse] = await Promise.all([
        api.get('/serial-vouchers/'),
        api.get('/serial-voucher-categories/')
      ]);

      setVouchers(vouchersResponse.data.results || vouchersResponse.data);
      setCategories(categoriesResponse.data.results || categoriesResponse.data);
    } catch (error) {
      console.error('Error loading data:', error);
      enqueueSnackbar('Failed to load serial vouchers', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      await api.delete(`/serial-vouchers/${deleteDialog.voucher.id}/`);
      enqueueSnackbar('Serial voucher deleted successfully', { variant: 'success' });
      setDeleteDialog({ open: false, voucher: null });
      loadData();
    } catch (error) {
      console.error('Error deleting voucher:', error);
      enqueueSnackbar('Failed to delete voucher', { variant: 'error' });
    }
  };

  const handlePreviewNext = async (voucher) => {
    try {
      const response = await api.get(`/serial-vouchers/${voucher.id}/preview_next/`);
      setPreviewDialog({
        open: true,
        voucher: { ...voucher, next_serial: response.data.next_serial }
      });
    } catch (error) {
      console.error('Error previewing next serial:', error);
      enqueueSnackbar('Failed to preview next serial number', { variant: 'error' });
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 3 }}>
            Loading serial vouchers...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Link
          component={RouterLink}
          to="/item-management-menu"
          color="inherit"
        >
          Item Management
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <ReceiptIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Serial Vouchers
        </Typography>
      </Breadcrumbs>

      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box display="flex" alignItems="center">
          <ReceiptIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
          <Typography variant="h4" component="h1" fontWeight="bold">
            Serial Vouchers
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadData}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/items/serial-vouchers/new')}
          >
            Add Voucher
          </Button>
        </Box>
      </Box>

      {/* Vouchers Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Category</TableCell>
              <TableCell>Prefix</TableCell>
              <TableCell>Current Number</TableCell>
              <TableCell>Number Length</TableCell>
              <TableCell>Max Number</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Preview</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {vouchers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    No serial vouchers found. Create your first voucher to get started.
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              vouchers.map((voucher) => (
                <TableRow key={voucher.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {voucher.category_name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={voucher.prefix}
                      size="small"
                      variant="outlined"
                      sx={{ fontFamily: 'monospace', fontWeight: 'bold' }}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                      {voucher.current_number}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {voucher.number_length} digits
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                      {voucher.max_number || 'Unlimited'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={voucher.is_active ? 'Active' : 'Inactive'}
                      color={voucher.is_active ? 'success' : 'default'}
                      size="small"
                    />
                    {voucher.is_exhausted && (
                      <Chip
                        label="Exhausted"
                        color="error"
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    )}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace', fontWeight: 'bold' }}>
                      {voucher.preview_next_serial}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Preview Next Serial">
                        <IconButton
                          size="small"
                          onClick={() => handlePreviewNext(voucher)}
                          color="info"
                        >
                          <PreviewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit Voucher">
                        <IconButton
                          size="small"
                          onClick={() => navigate(`/items/serial-vouchers/${voucher.id}/edit`)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Voucher">
                        <IconButton
                          size="small"
                          onClick={() => setDeleteDialog({ open: true, voucher })}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, voucher: null })}>
        <DialogTitle>Delete Serial Voucher</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the voucher "{deleteDialog.voucher?.category_name} - {deleteDialog.voucher?.prefix}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, voucher: null })}>
            Cancel
          </Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Preview Dialog */}
      <Dialog open={previewDialog.open} onClose={() => setPreviewDialog({ open: false, voucher: null })}>
        <DialogTitle>Next Serial Number Preview</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Category: {previewDialog.voucher?.category_name}
          </Typography>
          <Typography variant="body1" gutterBottom>
            Prefix: {previewDialog.voucher?.prefix}
          </Typography>
          <Typography variant="h5" color="primary.main" sx={{ fontFamily: 'monospace', fontWeight: 'bold', mt: 2 }}>
            Next Serial: {previewDialog.voucher?.next_serial}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog({ open: false, voucher: null })}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default SerialVouchersList;
