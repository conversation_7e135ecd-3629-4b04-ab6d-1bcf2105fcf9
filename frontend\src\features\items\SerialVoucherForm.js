import { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Breadcrumbs,
  Link,
  Alert,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Home as HomeIcon,
  Receipt as ReceiptIcon,
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Preview as PreviewIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate, useParams } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import api from '../../utils/axios';

const SerialVoucherForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { enqueueSnackbar } = useSnackbar();
  const isEdit = Boolean(id);

  const [formData, setFormData] = useState({
    category: '',
    prefix: '',
    current_number: 1,
    number_length: 6,
    max_number: '',
    is_active: true
  });

  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState({});

  // Load data on component mount
  useEffect(() => {
    loadCategories();
    if (isEdit) {
      loadVoucher();
    }
  }, [id, isEdit]);

  const loadCategories = async () => {
    try {
      const response = await api.get('/serial-voucher-categories/');
      setCategories(response.data.results || response.data);
    } catch (error) {
      console.error('Error loading categories:', error);
      enqueueSnackbar('Failed to load voucher categories', { variant: 'error' });
    }
  };

  const loadVoucher = async () => {
    setLoading(true);
    try {
      const response = await api.get(`/serial-vouchers/${id}/`);
      setFormData(response.data);
    } catch (error) {
      console.error('Error loading voucher:', error);
      enqueueSnackbar('Failed to load voucher', { variant: 'error' });
      navigate('/items/serial-vouchers');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = useCallback((field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  }, [errors]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }
    if (!formData.prefix || formData.prefix.trim().length < 1) {
      newErrors.prefix = 'Prefix is required';
    }
    if (formData.prefix && !/^[A-Z0-9]+$/.test(formData.prefix.trim())) {
      newErrors.prefix = 'Prefix must contain only uppercase letters and numbers';
    }
    if (formData.current_number < 1) {
      newErrors.current_number = 'Current number must be at least 1';
    }
    if (formData.number_length < 3 || formData.number_length > 10) {
      newErrors.number_length = 'Number length must be between 3 and 10';
    }
    if (formData.max_number && formData.max_number < formData.current_number) {
      newErrors.max_number = 'Maximum number must be greater than current number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!validateForm()) {
      enqueueSnackbar('Please fix the errors in the form', { variant: 'error' });
      return;
    }

    setSaving(true);
    try {
      const submitData = {
        ...formData,
        prefix: formData.prefix.trim().toUpperCase(),
        max_number: formData.max_number || null
      };

      if (isEdit) {
        await api.put(`/serial-vouchers/${id}/`, submitData);
        enqueueSnackbar('Serial voucher updated successfully', { variant: 'success' });
      } else {
        await api.post('/serial-vouchers/', submitData);
        enqueueSnackbar('Serial voucher created successfully', { variant: 'success' });
      }

      navigate('/items/serial-vouchers');
    } catch (error) {
      console.error('Error saving voucher:', error);
      if (error.response?.data) {
        const serverErrors = error.response.data;
        setErrors(serverErrors);
        enqueueSnackbar('Please fix the errors and try again', { variant: 'error' });
      } else {
        enqueueSnackbar('Failed to save voucher', { variant: 'error' });
      }
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate('/items/serial-vouchers');
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 3 }}>
            Loading voucher...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Link
          component={RouterLink}
          to="/item-management-menu"
          color="inherit"
        >
          Item Management
        </Link>
        <Link
          component={RouterLink}
          to="/items/serial-vouchers"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <ReceiptIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Serial Vouchers
        </Link>
        <Typography color="text.primary">
          {isEdit ? 'Edit Voucher' : 'New Voucher'}
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/items/serial-vouchers')}
          sx={{ mb: 2 }}
        >
          Back to Serial Vouchers
        </Button>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          {isEdit ? 'Edit Serial Voucher' : 'Create Serial Voucher'}
        </Typography>
        <Typography variant="h6" color="text.secondary">
          {isEdit ? 'Update serial voucher configuration' : 'Set up a new serial voucher for automated numbering'}
        </Typography>
      </Box>



      {/* Form */}
      <Paper sx={{ p: 4 }}>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required error={!!errors.category}>
                <InputLabel>Category</InputLabel>
                <Select
                  value={formData.category}
                  onChange={handleInputChange('category')}
                  label="Category"
                  disabled={saving}
                >
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.title}
                    </MenuItem>
                  ))}
                </Select>
                {errors.category && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                    {errors.category}
                  </Typography>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Prefix"
                value={formData.prefix}
                onChange={handleInputChange('prefix')}
                placeholder="e.g., INV, BATCH, ASSET"
                error={!!errors.prefix}
                helperText={errors.prefix || "Prefix for generated serial numbers (uppercase letters and numbers only)"}
                disabled={saving}
                inputProps={{ maxLength: 10 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                required
                label="Current Number"
                type="number"
                value={formData.current_number}
                onChange={handleInputChange('current_number')}
                error={!!errors.current_number}
                helperText={errors.current_number || "Next number to be issued"}
                disabled={saving}
                inputProps={{ min: 1 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                required
                label="Number Length"
                type="number"
                value={formData.number_length}
                onChange={handleInputChange('number_length')}
                error={!!errors.number_length}
                helperText={errors.number_length || "Total digits in the number (3-10)"}
                disabled={saving}
                inputProps={{ min: 3, max: 10 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Maximum Number"
                type="number"
                value={formData.max_number}
                onChange={handleInputChange('max_number')}
                error={!!errors.max_number}
                helperText={errors.max_number || "Optional maximum limit"}
                disabled={saving}
                inputProps={{ min: 1 }}
              />
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_active}
                    onChange={handleInputChange('is_active')}
                    disabled={saving}
                  />
                }
                label="Active"
              />
            </Grid>

            {/* Preview */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Paper sx={{ p: 3, bgcolor: 'grey.50', border: '1px solid', borderColor: 'grey.300' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <PreviewIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6" color="primary.main">
                    Serial Number Preview
                  </Typography>
                </Box>
                <Typography variant="subtitle2" gutterBottom>
                  Next Serial Number:
                </Typography>
                <Typography variant="h4" color="primary.main" fontWeight="bold" sx={{ fontFamily: 'monospace' }}>
                  {formData.prefix && formData.current_number && formData.number_length ?
                    `${formData.prefix.toUpperCase()}${String(formData.current_number).padStart(formData.number_length, '0')}` :
                    'Enter prefix and numbers to preview'
                  }
                </Typography>
                {formData.max_number && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Maximum: {formData.prefix.toUpperCase()}{String(formData.max_number).padStart(formData.number_length, '0')}
                  </Typography>
                )}
              </Paper>
            </Grid>

            {/* Action Buttons */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
                <Button
                  variant="outlined"
                  onClick={handleCancel}
                  startIcon={<CancelIcon />}
                  disabled={saving}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={<SaveIcon />}
                  disabled={saving}
                >
                  {saving ? 'Saving...' : (isEdit ? 'Update Voucher' : 'Create Voucher')}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>


    </Container>
  );
};

export default SerialVoucherForm;
