from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from .models import UserProfile
from organization.serializers.organization import OfficeSerializer

User = get_user_model()

class UserProfileSerializer(serializers.ModelSerializer):
    assigned_office_details = OfficeSerializer(source='assigned_office', read_only=True)

    class Meta:
        model = UserProfile
        fields = ('assigned_office', 'assigned_office_details', 'phone_number', 'position')

class UserSerializer(serializers.ModelSerializer):
    groups = serializers.StringRelatedField(many=True, read_only=True)
    profile = UserProfileSerializer(read_only=True)
    roles = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'first_name', 'last_name', 'groups', 'is_staff', 'is_superuser', 'profile', 'roles')
        read_only_fields = ('id', 'groups', 'is_staff', 'is_superuser', 'roles')

    def get_roles(self, obj):
        """Get user roles based on groups"""
        from organization.roles import get_user_roles
        return get_user_roles(obj)

class LoginSerializer(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)