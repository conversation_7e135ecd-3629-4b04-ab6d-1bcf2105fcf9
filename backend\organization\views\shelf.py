from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters import rest_framework as filters
from django.db import models
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from ..models import Shelf, Store
from ..serializers import (
    ShelfSerializer,
    ShelfListSerializer,
    ShelfDropdownSerializer,
)

try:
    from ..permissions import BaseModelPermission, IsAdmin, IsInventoryManager
except ImportError:
    # Fallback permissions if custom permissions are not available
    BaseModelPermission = permissions.IsAuthenticated
    IsAdmin = permissions.IsAdminUser
    IsInventoryManager = permissions.IsAuthenticated


class ShelfFilter(filters.FilterSet):
    """Filter for Shelf"""
    code = filters.CharFilter(lookup_expr='icontains')
    store = filters.ModelChoiceFilter(queryset=Store.objects.all())
    row = filters.NumberFilter()
    column = filters.NumberFilter()
    row_min = filters.NumberFilter(field_name='row', lookup_expr='gte')
    row_max = filters.NumberFilter(field_name='row', lookup_expr='lte')
    column_min = filters.NumberFilter(field_name='column', lookup_expr='gte')
    column_max = filters.NumberFilter(field_name='column', lookup_expr='lte')
    capacity_min = filters.NumberFilter(field_name='capacity', lookup_expr='gte')
    capacity_max = filters.NumberFilter(field_name='capacity', lookup_expr='lte')
    is_active = filters.BooleanFilter()
    
    class Meta:
        model = Shelf
        fields = ['code', 'store', 'row', 'column', 'is_active']


class ShelfViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing shelves
    """
    queryset = Shelf.objects.select_related('store', 'store__organization').all()
    serializer_class = ShelfSerializer
    permission_classes = []  # Temporarily allow unauthenticated access for testing
    filterset_class = ShelfFilter
    search_fields = ['code', 'description']
    ordering_fields = ['code', 'row', 'column', 'capacity', 'created_at', 'updated_at']
    ordering = ['store', 'row', 'column']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ShelfListSerializer
        elif self.action in ['dropdown', 'active_dropdown']:
            return ShelfDropdownSerializer
        return ShelfSerializer

    def get_queryset(self):
        """Filter queryset based on user permissions and action"""
        queryset = Shelf.objects.select_related('store', 'store__organization').all()
        
        # For dropdown actions, only return active items
        if self.action in ['dropdown', 'active_dropdown']:
            queryset = queryset.filter(is_active=True, store__is_active=True)
        
        return queryset

    @swagger_auto_schema(
        method='get',
        operation_description="Get active shelves for dropdown",
        responses={
            200: ShelfDropdownSerializer(many=True),
        }
    )
    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get active shelves for dropdown/select options"""
        queryset = self.get_queryset().filter(is_active=True, store__is_active=True)
        serializer = ShelfDropdownSerializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='get',
        operation_description="Get all active shelves",
        responses={
            200: ShelfListSerializer(many=True),
        }
    )
    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get all active shelves"""
        queryset = self.get_queryset().filter(is_active=True, store__is_active=True)
        serializer = ShelfListSerializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='get',
        operation_description="Get shelves by store",
        manual_parameters=[
            openapi.Parameter(
                'store_id',
                openapi.IN_QUERY,
                description="Store ID",
                type=openapi.TYPE_STRING,
                required=True
            )
        ],
        responses={
            200: ShelfDropdownSerializer(many=True),
        }
    )
    @action(detail=False, methods=['get'])
    def by_store(self, request):
        """Get shelves by store"""
        store_id = request.query_params.get('store_id')
        if not store_id:
            return Response({
                'error': 'store_id parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        queryset = self.get_queryset().filter(store_id=store_id)
        serializer = ShelfDropdownSerializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='get',
        operation_description="Get shelf layout for a store",
        manual_parameters=[
            openapi.Parameter(
                'store_id',
                openapi.IN_QUERY,
                description="Store ID",
                type=openapi.TYPE_STRING,
                required=True
            )
        ],
        responses={
            200: openapi.Response(
                description="Store layout",
                examples={
                    "application/json": {
                        "store_id": "uuid",
                        "store_name": "Main Store",
                        "max_row": 5,
                        "max_column": 10,
                        "shelves": [
                            {
                                "id": 1,
                                "code": "A1",
                                "row": 1,
                                "column": 1,
                                "capacity": 50,
                                "is_active": True
                            }
                        ]
                    }
                }
            ),
        }
    )
    @action(detail=False, methods=['get'])
    def layout(self, request):
        """Get shelf layout for a store"""
        store_id = request.query_params.get('store_id')
        if not store_id:
            return Response({
                'error': 'store_id parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            store = Store.objects.get(id=store_id)
        except Store.DoesNotExist:
            return Response({
                'error': 'Store not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        shelves = self.get_queryset().filter(store_id=store_id)
        
        layout_data = {
            'store_id': str(store.id),
            'store_name': store.name,
            'store_code': store.code,
            'max_row': shelves.aggregate(max_row=models.Max('row'))['max_row'] or 0,
            'max_column': shelves.aggregate(max_column=models.Max('column'))['max_column'] or 0,
            'shelves': ShelfListSerializer(shelves, many=True).data
        }
        
        return Response(layout_data)

    @swagger_auto_schema(
        method='post',
        operation_description="Activate a shelf",
        responses={
            200: openapi.Response(description="Shelf activated successfully"),
            404: openapi.Response(description="Shelf not found"),
        }
    )
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a shelf"""
        shelf = self.get_object()
        shelf.is_active = True
        shelf.save()
        return Response({
            'message': f'Shelf "{shelf.code}" activated successfully'
        })

    @swagger_auto_schema(
        method='post',
        operation_description="Deactivate a shelf",
        responses={
            200: openapi.Response(description="Shelf deactivated successfully"),
            404: openapi.Response(description="Shelf not found"),
        }
    )
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate a shelf"""
        shelf = self.get_object()
        shelf.is_active = False
        shelf.save()
        return Response({
            'message': f'Shelf "{shelf.code}" deactivated successfully'
        })

    @swagger_auto_schema(
        method='get',
        operation_description="Get statistics for a shelf",
        responses={
            200: openapi.Response(
                description="Shelf statistics",
                examples={
                    "application/json": {
                        "capacity": 50,
                        "current_usage": 0,
                        "available_space": 50,
                        "utilization_percentage": 0.0,
                    }
                }
            ),
        }
    )
    @action(detail=True, methods=['get'])
    def stats(self, request, pk=None):
        """Get statistics for a shelf"""
        shelf = self.get_object()
        
        # Note: This would need to be updated when inventory items are added
        stats = {
            'capacity': shelf.capacity,
            'current_usage': 0,  # Would be calculated from inventory items
            'available_space': shelf.capacity,  # Would be capacity - current_usage
            'utilization_percentage': 0.0,  # Would be (current_usage / capacity) * 100
        }
        
        return Response(stats)
