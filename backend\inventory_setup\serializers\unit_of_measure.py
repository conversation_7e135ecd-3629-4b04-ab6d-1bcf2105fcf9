"""
Unit of Measure Serializers
"""
from rest_framework import serializers
from ..models import UnitOfMeasure


class UnitOfMeasureSerializer(serializers.ModelSerializer):
    """Serializer for UnitOfMeasure model"""
    
    usage_count = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = UnitOfMeasure
        fields = [
            'id',
            'name',
            'symbol',
            'description',
            'display_name',
            'is_active',
            'usage_count',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_name(self, value):
        """Validate name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
        return value

    def validate_symbol(self, value):
        """Validate symbol"""
        if value:
            value = value.strip()
            if len(value) < 1:
                raise serializers.ValidationError("Symbol must be at least 1 character long")
        return value


class UnitOfMeasureListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing units of measure"""

    usage_count = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()

    class Meta:
        model = UnitOfMeasure
        fields = [
            'id',
            'name',
            'symbol',
            'description',
            'display_name',
            'is_active',
            'usage_count'
        ]


class UnitOfMeasureDropdownSerializer(serializers.ModelSerializer):
    """Minimal serializer for dropdown/select options"""
    
    label = serializers.SerializerMethodField()
    value = serializers.CharField(source='id')
    
    class Meta:
        model = UnitOfMeasure
        fields = ['value', 'label', 'symbol']
    
    def get_label(self, obj):
        return f"{obj.name} ({obj.symbol})"
