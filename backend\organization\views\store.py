from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters import rest_framework as filters
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from ..models import Store, StoreType, Organization
from ..serializers import (
    StoreSerializer,
    StoreListSerializer,
    StoreDropdownSerializer,
)

try:
    from ..permissions import BaseModelPermission, IsAdmin, IsInventoryManager
except ImportError:
    # Fallback permissions if custom permissions are not available
    BaseModelPermission = permissions.IsAuthenticated
    IsAdmin = permissions.IsAdminUser
    IsInventoryManager = permissions.IsAuthenticated


class StoreFilter(filters.FilterSet):
    """Filter for Store"""
    name = filters.CharFilter(lookup_expr='icontains')
    code = filters.CharFilter(lookup_expr='icontains')
    store_type = filters.ModelChoiceFilter(queryset=StoreType.objects.all())
    organization = filters.ModelChoiceFilter(queryset=Organization.objects.all())
    location = filters.CharFilter(lookup_expr='icontains')
    manager_name = filters.CharFilter(lookup_expr='icontains')
    is_active = filters.BooleanFilter()
    
    class Meta:
        model = Store
        fields = ['name', 'code', 'store_type', 'organization', 'location', 'manager_name', 'is_active']


class StoreViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing stores
    """
    queryset = Store.objects.select_related('store_type', 'organization').all()
    serializer_class = StoreSerializer
    permission_classes = []  # Temporarily allow unauthenticated access for testing
    filterset_class = StoreFilter
    search_fields = ['name', 'code', 'location', 'manager_name']
    ordering_fields = ['name', 'code', 'created_at', 'updated_at']
    ordering = ['name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return StoreListSerializer
        elif self.action in ['dropdown', 'active_dropdown']:
            return StoreDropdownSerializer
        return StoreSerializer

    def get_queryset(self):
        """Filter queryset based on user permissions and action"""
        queryset = Store.objects.select_related('store_type', 'organization').all()
        
        # For dropdown actions, only return active items
        if self.action in ['dropdown', 'active_dropdown']:
            queryset = queryset.filter(is_active=True)
        
        return queryset

    @swagger_auto_schema(
        method='get',
        operation_description="Get active stores for dropdown",
        responses={
            200: StoreDropdownSerializer(many=True),
        }
    )
    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get active stores for dropdown/select options"""
        queryset = self.get_queryset().filter(is_active=True)
        serializer = StoreDropdownSerializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='get',
        operation_description="Get all active stores",
        responses={
            200: StoreListSerializer(many=True),
        }
    )
    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get all active stores"""
        queryset = self.get_queryset().filter(is_active=True)
        serializer = StoreListSerializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='get',
        operation_description="Get stores by organization",
        manual_parameters=[
            openapi.Parameter(
                'organization_id',
                openapi.IN_QUERY,
                description="Organization ID",
                type=openapi.TYPE_STRING,
                required=True
            )
        ],
        responses={
            200: StoreListSerializer(many=True),
        }
    )
    @action(detail=False, methods=['get'])
    def by_organization(self, request):
        """Get stores by organization"""
        organization_id = request.query_params.get('organization_id')
        if not organization_id:
            return Response({
                'error': 'organization_id parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        queryset = self.get_queryset().filter(organization_id=organization_id)
        serializer = StoreListSerializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='post',
        operation_description="Activate a store",
        responses={
            200: openapi.Response(description="Store activated successfully"),
            404: openapi.Response(description="Store not found"),
        }
    )
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a store"""
        store = self.get_object()
        store.is_active = True
        store.save()
        return Response({
            'message': f'Store "{store.name}" activated successfully'
        })

    @swagger_auto_schema(
        method='post',
        operation_description="Deactivate a store",
        responses={
            200: openapi.Response(description="Store deactivated successfully"),
            404: openapi.Response(description="Store not found"),
            400: openapi.Response(description="Cannot deactivate store with active shelves"),
        }
    )
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate a store"""
        store = self.get_object()
        
        # Check if there are active shelves in this store
        active_shelves_count = store.shelves.filter(is_active=True).count()
        if active_shelves_count > 0:
            return Response({
                'error': f'Cannot deactivate store. {active_shelves_count} active shelves exist in this store.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        store.is_active = False
        store.save()
        return Response({
            'message': f'Store "{store.name}" deactivated successfully'
        })

    @swagger_auto_schema(
        method='get',
        operation_description="Get statistics for a store",
        responses={
            200: openapi.Response(
                description="Store statistics",
                examples={
                    "application/json": {
                        "shelves_count": 20,
                        "active_shelves_count": 18,
                        "inactive_shelves_count": 2,
                        "total_capacity": 500,
                    }
                }
            ),
        }
    )
    @action(detail=True, methods=['get'])
    def stats(self, request, pk=None):
        """Get statistics for a store"""
        store = self.get_object()
        shelves = store.shelves.all()
        
        stats = {
            'shelves_count': shelves.count(),
            'active_shelves_count': shelves.filter(is_active=True).count(),
            'inactive_shelves_count': shelves.filter(is_active=False).count(),
            'total_capacity': sum(shelf.capacity for shelf in shelves),
        }
        
        return Response(stats)
