from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters import rest_framework as filters
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from ..models import StoreType
from ..serializers import (
    StoreTypeSerializer,
    StoreTypeListSerializer,
    StoreTypeDropdownSerializer,
)

try:
    from ..permissions import BaseModelPermission, IsAdmin, IsInventoryManager
except ImportError:
    # Fallback permissions if custom permissions are not available
    BaseModelPermission = permissions.IsAuthenticated
    IsAdmin = permissions.IsAdminUser
    IsInventoryManager = permissions.IsAuthenticated


class StoreTypeFilter(filters.FilterSet):
    """Filter for StoreType"""
    name = filters.CharFilter(lookup_expr='icontains')
    is_active = filters.BooleanFilter()
    
    class Meta:
        model = StoreType
        fields = ['name', 'is_active']


class StoreTypeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing store types
    """
    queryset = StoreType.objects.all()
    serializer_class = StoreTypeSerializer
    permission_classes = []  # Temporarily allow unauthenticated access for testing
    filterset_class = StoreTypeFilter
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at', 'updated_at']
    ordering = ['name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return StoreTypeListSerializer
        elif self.action in ['dropdown', 'active_dropdown']:
            return StoreTypeDropdownSerializer
        return StoreTypeSerializer

    def get_queryset(self):
        """Filter queryset based on user permissions and action"""
        queryset = StoreType.objects.all()
        
        # For dropdown actions, only return active items
        if self.action in ['dropdown', 'active_dropdown']:
            queryset = queryset.filter(is_active=True)
        
        return queryset

    @swagger_auto_schema(
        method='get',
        operation_description="Get active store types for dropdown",
        responses={
            200: StoreTypeDropdownSerializer(many=True),
        }
    )
    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get active store types for dropdown/select options"""
        queryset = self.get_queryset().filter(is_active=True)
        serializer = StoreTypeDropdownSerializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='get',
        operation_description="Get all active store types",
        responses={
            200: StoreTypeListSerializer(many=True),
        }
    )
    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get all active store types"""
        queryset = self.get_queryset().filter(is_active=True)
        serializer = StoreTypeListSerializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='post',
        operation_description="Activate a store type",
        responses={
            200: openapi.Response(description="Store type activated successfully"),
            404: openapi.Response(description="Store type not found"),
        }
    )
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a store type"""
        store_type = self.get_object()
        store_type.is_active = True
        store_type.save()
        return Response({
            'message': f'Store type "{store_type.name}" activated successfully'
        })

    @swagger_auto_schema(
        method='post',
        operation_description="Deactivate a store type",
        responses={
            200: openapi.Response(description="Store type deactivated successfully"),
            404: openapi.Response(description="Store type not found"),
            400: openapi.Response(description="Cannot deactivate store type with active stores"),
        }
    )
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate a store type"""
        store_type = self.get_object()
        
        # Check if there are active stores using this type
        active_stores_count = store_type.stores.filter(is_active=True).count()
        if active_stores_count > 0:
            return Response({
                'error': f'Cannot deactivate store type. {active_stores_count} active stores are using this type.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        store_type.is_active = False
        store_type.save()
        return Response({
            'message': f'Store type "{store_type.name}" deactivated successfully'
        })

    @swagger_auto_schema(
        method='get',
        operation_description="Get statistics for a store type",
        responses={
            200: openapi.Response(
                description="Store type statistics",
                examples={
                    "application/json": {
                        "stores_count": 5,
                        "active_stores_count": 4,
                        "inactive_stores_count": 1,
                    }
                }
            ),
        }
    )
    @action(detail=True, methods=['get'])
    def stats(self, request, pk=None):
        """Get statistics for a store type"""
        store_type = self.get_object()
        stores = store_type.stores.all()
        
        stats = {
            'stores_count': stores.count(),
            'active_stores_count': stores.filter(is_active=True).count(),
            'inactive_stores_count': stores.filter(is_active=False).count(),
        }
        
        return Response(stats)
