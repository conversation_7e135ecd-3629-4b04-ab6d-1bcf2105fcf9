{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects v2\\\\asset management\\\\frontend\\\\src\\\\features\\\\items\\\\ItemManagementDashboard.js\",\n  _s = $RefreshSig$();\n/**\n * Item Management Dashboard\n * Professional dashboard-style interface matching Organization and Supplier menus\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link as RouterLink } from 'react-router-dom';\nimport { Container, Typography, Box, Grid, Card, CardContent, Button, Paper, useTheme, alpha, Chip, CircularProgress, Breadcrumbs, Link } from '@mui/material';\nimport { Inventory as InventoryIcon, Receipt as ReceiptIcon, Category as CategoryIcon, QrCode as QrCodeIcon, Insights as InsightsIcon, Home as HomeIcon, Dashboard as DashboardIcon } from '@mui/icons-material';\nimport { useSnackbar } from 'notistack';\nimport api from '../../utils/axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ItemManagementDashboard = () => {\n  _s();\n  const theme = useTheme();\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const [counts, setCounts] = useState({\n    serialVouchers: 0,\n    serialVoucherCategories: 0,\n    loading: true\n  });\n  useEffect(() => {\n    loadCounts();\n  }, []);\n  const loadCounts = async () => {\n    try {\n      var _vouchersResponse$dat, _categoriesResponse$d;\n      const [vouchersResponse, categoriesResponse] = await Promise.all([api.get('/serial-vouchers/'), api.get('/serial-voucher-categories/')]);\n      setCounts({\n        serialVouchers: ((_vouchersResponse$dat = vouchersResponse.data.results) === null || _vouchersResponse$dat === void 0 ? void 0 : _vouchersResponse$dat.length) || vouchersResponse.data.length || 0,\n        serialVoucherCategories: ((_categoriesResponse$d = categoriesResponse.data.results) === null || _categoriesResponse$d === void 0 ? void 0 : _categoriesResponse$d.length) || categoriesResponse.data.length || 0,\n        loading: false\n      });\n    } catch (error) {\n      console.error('Error loading counts:', error);\n      setCounts(prev => ({\n        ...prev,\n        loading: false\n      }));\n    }\n  };\n\n  // Item management cards data\n  const itemCards = [{\n    id: 'serial-vouchers',\n    title: 'Serial Vouchers',\n    description: 'Manage automated serial number generation for items',\n    icon: /*#__PURE__*/_jsxDEV(ReceiptIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.primary.main,\n    path: '/items/serial-vouchers',\n    count: counts.loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 31\n    }, this) : counts.serialVouchers,\n    adminOnly: false\n  }, {\n    id: 'voucher-categories',\n    title: 'Voucher Categories',\n    description: 'Organize serial vouchers by item categories',\n    icon: /*#__PURE__*/_jsxDEV(CategoryIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.success.main,\n    path: '/items/serial-voucher-categories',\n    count: counts.loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 31\n    }, this) : counts.serialVoucherCategories,\n    adminOnly: false\n  }, {\n    id: 'item-catalog',\n    title: 'Item Catalog',\n    description: 'Comprehensive item database and specifications',\n    icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.info.main,\n    path: '/items/catalog',\n    count: 'Soon',\n    adminOnly: false\n  }, {\n    id: 'barcode-management',\n    title: 'Barcode Management',\n    description: 'Generate and manage item barcodes and QR codes',\n    icon: /*#__PURE__*/_jsxDEV(QrCodeIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.warning.main,\n    path: '/items/barcodes',\n    count: 'Soon',\n    adminOnly: false\n  }];\n\n  // Filter cards based on user role (for now showing all)\n  const filteredCards = itemCards.filter(card => !card.adminOnly || true);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      mt: 4,\n      mb: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Breadcrumbs, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        component: RouterLink,\n        to: \"/dashboard\",\n        color: \"inherit\",\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(HomeIcon, {\n          sx: {\n            mr: 0.5\n          },\n          fontSize: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), \"Dashboard\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        color: \"text.primary\",\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(DashboardIcon, {\n          sx: {\n            mr: 0.5\n          },\n          fontSize: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), \"Item Management\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4,\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(InventoryIcon, {\n        color: \"primary\",\n        sx: {\n          fontSize: 40,\n          mr: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        children: \"Item Management Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 4,\n        display: 'flex',\n        alignItems: 'center',\n        bgcolor: alpha(theme.palette.primary.main, 0.1),\n        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,\n        borderRadius: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(InsightsIcon, {\n        color: \"primary\",\n        sx: {\n          mr: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"primary.main\",\n          children: \"Item Management Center\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Comprehensive item lifecycle management system for cataloging, tracking, serial number generation, and inventory control\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: filteredCards.map((card, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          component: RouterLink,\n          to: card.path,\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            transition: 'transform 0.3s, box-shadow 0.3s',\n            '&:hover': {\n              transform: 'translateY(-8px)',\n              boxShadow: '0 12px 20px rgba(0,0,0,0.1)'\n            },\n            textDecoration: 'none',\n            borderTop: `4px solid ${card.color}`,\n            borderRadius: 2,\n            position: 'relative',\n            overflow: 'visible'\n          },\n          children: [card.count && /*#__PURE__*/_jsxDEV(Chip, {\n            label: card.count,\n            color: \"primary\",\n            size: \"small\",\n            sx: {\n              position: 'absolute',\n              top: -10,\n              right: 16,\n              fontWeight: 'bold',\n              boxShadow: '0 2px 5px rgba(0,0,0,0.2)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2,\n              display: 'flex',\n              alignItems: 'center',\n              bgcolor: alpha(card.color, 0.1)\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mr: 2,\n                p: 1.5,\n                borderRadius: '50%',\n                bgcolor: alpha(card.color, 0.2),\n                color: card.color,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: card.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"div\",\n              color: \"text.primary\",\n              children: card.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: card.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2,\n              pt: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              sx: {\n                color: card.color,\n                '&:hover': {\n                  bgcolor: alpha(card.color, 0.1)\n                }\n              },\n              children: card.count === 'Soon' ? 'Coming Soon' : 'Manage'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Quick Statistics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary.main\",\n              fontWeight: \"bold\",\n              children: counts.loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 35\n              }, this) : counts.serialVouchers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Serial Vouchers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"success.main\",\n              fontWeight: \"bold\",\n              children: counts.loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 35\n              }, this) : counts.serialVoucherCategories\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Voucher Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"info.main\",\n              fontWeight: \"bold\",\n              children: \"0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Items Cataloged\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"warning.main\",\n              fontWeight: \"bold\",\n              children: \"0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Barcodes Generated\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_s(ItemManagementDashboard, \"m6T5l5T9jkrWpFQPgZoUDa3Wu9U=\", false, function () {\n  return [useTheme, useSnackbar];\n});\n_c = ItemManagementDashboard;\nexport default ItemManagementDashboard;\nvar _c;\n$RefreshReg$(_c, \"ItemManagementDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "RouterLink", "Container", "Typography", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Paper", "useTheme", "alpha", "Chip", "CircularProgress", "Breadcrumbs", "Inventory", "InventoryIcon", "Receipt", "ReceiptIcon", "Category", "CategoryIcon", "QrCode", "QrCodeIcon", "Insights", "InsightsIcon", "Home", "HomeIcon", "Dashboard", "DashboardIcon", "useSnackbar", "api", "jsxDEV", "_jsxDEV", "ItemManagementDashboard", "_s", "theme", "enqueueSnackbar", "counts", "setCounts", "serialVouchers", "serialVoucherCategories", "loading", "loadCounts", "_vouchersResponse$dat", "_categoriesResponse$d", "vouchersResponse", "categoriesResponse", "Promise", "all", "get", "data", "results", "length", "error", "console", "prev", "itemCards", "id", "title", "description", "icon", "sx", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "palette", "primary", "main", "path", "count", "size", "adminOnly", "success", "info", "warning", "filteredCards", "filter", "card", "max<PERSON><PERSON><PERSON>", "mt", "mb", "children", "component", "to", "display", "alignItems", "mr", "variant", "gutterBottom", "p", "bgcolor", "border", "borderRadius", "container", "spacing", "map", "index", "item", "xs", "sm", "md", "height", "flexDirection", "transition", "transform", "boxShadow", "textDecoration", "borderTop", "position", "overflow", "label", "top", "right", "fontWeight", "justifyContent", "flexGrow", "pt", "textAlign", "_c", "$RefreshReg$"], "sources": ["D:/Projects v2/asset management/frontend/src/features/items/ItemManagementDashboard.js"], "sourcesContent": ["/**\n * Item Management Dashboard\n * Professional dashboard-style interface matching Organization and Supplier menus\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link as RouterLink } from 'react-router-dom';\nimport {\n  Container,\n  Typography,\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Button,\n  Paper,\n  useTheme,\n  alpha,\n  Chip,\n  CircularProgress,\n  Breadcrumbs,\n  Link\n} from '@mui/material';\nimport {\n  Inventory as InventoryIcon,\n  Receipt as ReceiptIcon,\n  Category as CategoryIcon,\n  QrCode as QrCodeIcon,\n  Insights as InsightsIcon,\n  Home as HomeIcon,\n  Dashboard as DashboardIcon\n} from '@mui/icons-material';\nimport { useSnackbar } from 'notistack';\nimport api from '../../utils/axios';\n\nconst ItemManagementDashboard = () => {\n  const theme = useTheme();\n  const { enqueueSnackbar } = useSnackbar();\n  \n  const [counts, setCounts] = useState({\n    serialVouchers: 0,\n    serialVoucherCategories: 0,\n    loading: true\n  });\n\n  useEffect(() => {\n    loadCounts();\n  }, []);\n\n  const loadCounts = async () => {\n    try {\n      const [vouchersResponse, categoriesResponse] = await Promise.all([\n        api.get('/serial-vouchers/'),\n        api.get('/serial-voucher-categories/')\n      ]);\n      \n      setCounts({\n        serialVouchers: vouchersResponse.data.results?.length || vouchersResponse.data.length || 0,\n        serialVoucherCategories: categoriesResponse.data.results?.length || categoriesResponse.data.length || 0,\n        loading: false\n      });\n    } catch (error) {\n      console.error('Error loading counts:', error);\n      setCounts(prev => ({ ...prev, loading: false }));\n    }\n  };\n\n  // Item management cards data\n  const itemCards = [\n    {\n      id: 'serial-vouchers',\n      title: 'Serial Vouchers',\n      description: 'Manage automated serial number generation for items',\n      icon: <ReceiptIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.primary.main,\n      path: '/items/serial-vouchers',\n      count: counts.loading ? <CircularProgress size={16} /> : counts.serialVouchers,\n      adminOnly: false\n    },\n    {\n      id: 'voucher-categories',\n      title: 'Voucher Categories',\n      description: 'Organize serial vouchers by item categories',\n      icon: <CategoryIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.success.main,\n      path: '/items/serial-voucher-categories',\n      count: counts.loading ? <CircularProgress size={16} /> : counts.serialVoucherCategories,\n      adminOnly: false\n    },\n    {\n      id: 'item-catalog',\n      title: 'Item Catalog',\n      description: 'Comprehensive item database and specifications',\n      icon: <InventoryIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.info.main,\n      path: '/items/catalog',\n      count: 'Soon',\n      adminOnly: false\n    },\n    {\n      id: 'barcode-management',\n      title: 'Barcode Management',\n      description: 'Generate and manage item barcodes and QR codes',\n      icon: <QrCodeIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.warning.main,\n      path: '/items/barcodes',\n      count: 'Soon',\n      adminOnly: false\n    }\n  ];\n\n  // Filter cards based on user role (for now showing all)\n  const filteredCards = itemCards.filter(card => !card.adminOnly || true);\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\n      {/* Breadcrumbs */}\n      <Breadcrumbs sx={{ mb: 3 }}>\n        <Link\n          component={RouterLink}\n          to=\"/dashboard\"\n          color=\"inherit\"\n          sx={{ display: 'flex', alignItems: 'center' }}\n        >\n          <HomeIcon sx={{ mr: 0.5 }} fontSize=\"inherit\" />\n          Dashboard\n        </Link>\n        <Typography color=\"text.primary\" sx={{ display: 'flex', alignItems: 'center' }}>\n          <DashboardIcon sx={{ mr: 0.5 }} fontSize=\"inherit\" />\n          Item Management\n        </Typography>\n      </Breadcrumbs>\n\n      {/* Header */}\n      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center' }}>\n        <InventoryIcon color=\"primary\" sx={{ fontSize: 40, mr: 2 }} />\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n          Item Management Dashboard\n        </Typography>\n      </Box>\n\n      {/* Info Paper */}\n      <Paper\n        sx={{\n          p: 2,\n          mb: 4,\n          display: 'flex',\n          alignItems: 'center',\n          bgcolor: alpha(theme.palette.primary.main, 0.1),\n          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,\n          borderRadius: 2\n        }}\n      >\n        <InsightsIcon color=\"primary\" sx={{ mr: 2 }} />\n        <Box>\n          <Typography variant=\"h6\" color=\"primary.main\">\n            Item Management Center\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Comprehensive item lifecycle management system for cataloging, tracking, serial number generation, and inventory control\n          </Typography>\n        </Box>\n      </Paper>\n\n      {/* Item Management Cards */}\n      <Grid container spacing={3}>\n        {filteredCards.map((card, index) => (\n          <Grid item xs={12} sm={6} md={6} key={index}>\n            <Card\n              component={RouterLink}\n              to={card.path}\n              sx={{\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                transition: 'transform 0.3s, box-shadow 0.3s',\n                '&:hover': {\n                  transform: 'translateY(-8px)',\n                  boxShadow: '0 12px 20px rgba(0,0,0,0.1)',\n                },\n                textDecoration: 'none',\n                borderTop: `4px solid ${card.color}`,\n                borderRadius: 2,\n                position: 'relative',\n                overflow: 'visible'\n              }}\n            >\n              {card.count && (\n                <Chip\n                  label={card.count}\n                  color=\"primary\"\n                  size=\"small\"\n                  sx={{\n                    position: 'absolute',\n                    top: -10,\n                    right: 16,\n                    fontWeight: 'bold',\n                    boxShadow: '0 2px 5px rgba(0,0,0,0.2)'\n                  }}\n                />\n              )}\n              <Box\n                sx={{\n                  p: 2,\n                  display: 'flex',\n                  alignItems: 'center',\n                  bgcolor: alpha(card.color, 0.1),\n                }}\n              >\n                <Box\n                  sx={{\n                    mr: 2,\n                    p: 1.5,\n                    borderRadius: '50%',\n                    bgcolor: alpha(card.color, 0.2),\n                    color: card.color,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}\n                >\n                  {card.icon}\n                </Box>\n                <Typography variant=\"h6\" component=\"div\" color=\"text.primary\">\n                  {card.title}\n                </Typography>\n              </Box>\n              <CardContent sx={{ flexGrow: 1 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  {card.description}\n                </Typography>\n              </CardContent>\n              <Box sx={{ p: 2, pt: 0 }}>\n                <Button\n                  size=\"small\"\n                  sx={{\n                    color: card.color,\n                    '&:hover': {\n                      bgcolor: alpha(card.color, 0.1)\n                    }\n                  }}\n                >\n                  {card.count === 'Soon' ? 'Coming Soon' : 'Manage'}\n                </Button>\n              </Box>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* Quick Stats */}\n      <Paper sx={{ p: 3, mt: 4 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Quick Statistics\n        </Typography>\n        <Grid container spacing={3}>\n          <Grid item xs={12} sm={6} md={3}>\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h4\" color=\"primary.main\" fontWeight=\"bold\">\n                {counts.loading ? <CircularProgress size={24} /> : counts.serialVouchers}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Serial Vouchers\n              </Typography>\n            </Box>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h4\" color=\"success.main\" fontWeight=\"bold\">\n                {counts.loading ? <CircularProgress size={24} /> : counts.serialVoucherCategories}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Voucher Categories\n              </Typography>\n            </Box>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h4\" color=\"info.main\" fontWeight=\"bold\">\n                0\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Items Cataloged\n              </Typography>\n            </Box>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h4\" color=\"warning.main\" fontWeight=\"bold\">\n                0\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Barcodes Generated\n              </Typography>\n            </Box>\n          </Grid>\n        </Grid>\n      </Paper>\n    </Container>\n  );\n};\n\nexport default ItemManagementDashboard;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,IAAIC,UAAU,QAAQ,kBAAkB;AACrD,SACEC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,gBAAgB,EAChBC,WAAW,EACXd,IAAI,QACC,eAAe;AACtB,SACEe,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,WAAW;AACvC,OAAOC,GAAG,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,KAAK,GAAGzB,QAAQ,CAAC,CAAC;EACxB,MAAM;IAAE0B;EAAgB,CAAC,GAAGP,WAAW,CAAC,CAAC;EAEzC,MAAM,CAACQ,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC;IACnCyC,cAAc,EAAE,CAAC;IACjBC,uBAAuB,EAAE,CAAC;IAC1BC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF1C,SAAS,CAAC,MAAM;IACd2C,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MAAA,IAAAC,qBAAA,EAAAC,qBAAA;MACF,MAAM,CAACC,gBAAgB,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC/DlB,GAAG,CAACmB,GAAG,CAAC,mBAAmB,CAAC,EAC5BnB,GAAG,CAACmB,GAAG,CAAC,6BAA6B,CAAC,CACvC,CAAC;MAEFX,SAAS,CAAC;QACRC,cAAc,EAAE,EAAAI,qBAAA,GAAAE,gBAAgB,CAACK,IAAI,CAACC,OAAO,cAAAR,qBAAA,uBAA7BA,qBAAA,CAA+BS,MAAM,KAAIP,gBAAgB,CAACK,IAAI,CAACE,MAAM,IAAI,CAAC;QAC1FZ,uBAAuB,EAAE,EAAAI,qBAAA,GAAAE,kBAAkB,CAACI,IAAI,CAACC,OAAO,cAAAP,qBAAA,uBAA/BA,qBAAA,CAAiCQ,MAAM,KAAIN,kBAAkB,CAACI,IAAI,CAACE,MAAM,IAAI,CAAC;QACvGX,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7Cf,SAAS,CAACiB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEd,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMe,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,qDAAqD;IAClEC,IAAI,eAAE5B,OAAA,CAACd,WAAW;MAAC2C,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3CC,KAAK,EAAEhC,KAAK,CAACiC,OAAO,CAACC,OAAO,CAACC,IAAI;IACjCC,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAEnC,MAAM,CAACI,OAAO,gBAAGT,OAAA,CAACnB,gBAAgB;MAAC4D,IAAI,EAAE;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAAG7B,MAAM,CAACE,cAAc;IAC9EmC,SAAS,EAAE;EACb,CAAC,EACD;IACEjB,EAAE,EAAE,oBAAoB;IACxBC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,6CAA6C;IAC1DC,IAAI,eAAE5B,OAAA,CAACZ,YAAY;MAACyC,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5CC,KAAK,EAAEhC,KAAK,CAACiC,OAAO,CAACO,OAAO,CAACL,IAAI;IACjCC,IAAI,EAAE,kCAAkC;IACxCC,KAAK,EAAEnC,MAAM,CAACI,OAAO,gBAAGT,OAAA,CAACnB,gBAAgB;MAAC4D,IAAI,EAAE;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAAG7B,MAAM,CAACG,uBAAuB;IACvFkC,SAAS,EAAE;EACb,CAAC,EACD;IACEjB,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,gDAAgD;IAC7DC,IAAI,eAAE5B,OAAA,CAAChB,aAAa;MAAC6C,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7CC,KAAK,EAAEhC,KAAK,CAACiC,OAAO,CAACQ,IAAI,CAACN,IAAI;IAC9BC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,MAAM;IACbE,SAAS,EAAE;EACb,CAAC,EACD;IACEjB,EAAE,EAAE,oBAAoB;IACxBC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,gDAAgD;IAC7DC,IAAI,eAAE5B,OAAA,CAACV,UAAU;MAACuC,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1CC,KAAK,EAAEhC,KAAK,CAACiC,OAAO,CAACS,OAAO,CAACP,IAAI;IACjCC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,MAAM;IACbE,SAAS,EAAE;EACb,CAAC,CACF;;EAED;EACA,MAAMI,aAAa,GAAGtB,SAAS,CAACuB,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAACN,SAAS,IAAI,IAAI,CAAC;EAEvE,oBACE1C,OAAA,CAAC9B,SAAS;IAAC+E,QAAQ,EAAC,IAAI;IAACpB,EAAE,EAAE;MAAEqB,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAE5CpD,OAAA,CAAClB,WAAW;MAAC+C,EAAE,EAAE;QAAEsB,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzBpD,OAAA,CAAChC,IAAI;QACHqF,SAAS,EAAEpF,UAAW;QACtBqF,EAAE,EAAC,YAAY;QACfnB,KAAK,EAAC,SAAS;QACfN,EAAE,EAAE;UAAE0B,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBAE9CpD,OAAA,CAACN,QAAQ;UAACmC,EAAE,EAAE;YAAE4B,EAAE,EAAE;UAAI,CAAE;UAAC3B,QAAQ,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAElD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPlC,OAAA,CAAC7B,UAAU;QAACgE,KAAK,EAAC,cAAc;QAACN,EAAE,EAAE;UAAE0B,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBAC7EpD,OAAA,CAACJ,aAAa;UAACiC,EAAE,EAAE;YAAE4B,EAAE,EAAE;UAAI,CAAE;UAAC3B,QAAQ,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,mBAEvD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGdlC,OAAA,CAAC5B,GAAG;MAACyD,EAAE,EAAE;QAAEsB,EAAE,EAAE,CAAC;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAJ,QAAA,gBACxDpD,OAAA,CAAChB,aAAa;QAACmD,KAAK,EAAC,SAAS;QAACN,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAE2B,EAAE,EAAE;QAAE;MAAE;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9DlC,OAAA,CAAC7B,UAAU;QAACuF,OAAO,EAAC,IAAI;QAACL,SAAS,EAAC,IAAI;QAACM,YAAY;QAAAP,QAAA,EAAC;MAErD;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNlC,OAAA,CAACvB,KAAK;MACJoD,EAAE,EAAE;QACF+B,CAAC,EAAE,CAAC;QACJT,EAAE,EAAE,CAAC;QACLI,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBK,OAAO,EAAElF,KAAK,CAACwB,KAAK,CAACiC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,GAAG,CAAC;QAC/CwB,MAAM,EAAE,aAAanF,KAAK,CAACwB,KAAK,CAACiC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,GAAG,CAAC,EAAE;QAC7DyB,YAAY,EAAE;MAChB,CAAE;MAAAX,QAAA,gBAEFpD,OAAA,CAACR,YAAY;QAAC2C,KAAK,EAAC,SAAS;QAACN,EAAE,EAAE;UAAE4B,EAAE,EAAE;QAAE;MAAE;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/ClC,OAAA,CAAC5B,GAAG;QAAAgF,QAAA,gBACFpD,OAAA,CAAC7B,UAAU;UAACuF,OAAO,EAAC,IAAI;UAACvB,KAAK,EAAC,cAAc;UAAAiB,QAAA,EAAC;QAE9C;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblC,OAAA,CAAC7B,UAAU;UAACuF,OAAO,EAAC,OAAO;UAACvB,KAAK,EAAC,gBAAgB;UAAAiB,QAAA,EAAC;QAEnD;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRlC,OAAA,CAAC3B,IAAI;MAAC2F,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAb,QAAA,EACxBN,aAAa,CAACoB,GAAG,CAAC,CAAClB,IAAI,EAAEmB,KAAK,kBAC7BnE,OAAA,CAAC3B,IAAI;QAAC+F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAnB,QAAA,eAC9BpD,OAAA,CAAC1B,IAAI;UACH+E,SAAS,EAAEpF,UAAW;UACtBqF,EAAE,EAAEN,IAAI,CAACT,IAAK;UACdV,EAAE,EAAE;YACF2C,MAAM,EAAE,MAAM;YACdjB,OAAO,EAAE,MAAM;YACfkB,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE,iCAAiC;YAC7C,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb,CAAC;YACDC,cAAc,EAAE,MAAM;YACtBC,SAAS,EAAE,aAAa9B,IAAI,CAACb,KAAK,EAAE;YACpC4B,YAAY,EAAE,CAAC;YACfgB,QAAQ,EAAE,UAAU;YACpBC,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,GAEDJ,IAAI,CAACR,KAAK,iBACTxC,OAAA,CAACpB,IAAI;YACHqG,KAAK,EAAEjC,IAAI,CAACR,KAAM;YAClBL,KAAK,EAAC,SAAS;YACfM,IAAI,EAAC,OAAO;YACZZ,EAAE,EAAE;cACFkD,QAAQ,EAAE,UAAU;cACpBG,GAAG,EAAE,CAAC,EAAE;cACRC,KAAK,EAAE,EAAE;cACTC,UAAU,EAAE,MAAM;cAClBR,SAAS,EAAE;YACb;UAAE;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF,eACDlC,OAAA,CAAC5B,GAAG;YACFyD,EAAE,EAAE;cACF+B,CAAC,EAAE,CAAC;cACJL,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBK,OAAO,EAAElF,KAAK,CAACqE,IAAI,CAACb,KAAK,EAAE,GAAG;YAChC,CAAE;YAAAiB,QAAA,gBAEFpD,OAAA,CAAC5B,GAAG;cACFyD,EAAE,EAAE;gBACF4B,EAAE,EAAE,CAAC;gBACLG,CAAC,EAAE,GAAG;gBACNG,YAAY,EAAE,KAAK;gBACnBF,OAAO,EAAElF,KAAK,CAACqE,IAAI,CAACb,KAAK,EAAE,GAAG,CAAC;gBAC/BA,KAAK,EAAEa,IAAI,CAACb,KAAK;gBACjBoB,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB6B,cAAc,EAAE;cAClB,CAAE;cAAAjC,QAAA,EAEDJ,IAAI,CAACpB;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACNlC,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,IAAI;cAACL,SAAS,EAAC,KAAK;cAAClB,KAAK,EAAC,cAAc;cAAAiB,QAAA,EAC1DJ,IAAI,CAACtB;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNlC,OAAA,CAACzB,WAAW;YAACsD,EAAE,EAAE;cAAEyD,QAAQ,EAAE;YAAE,CAAE;YAAAlC,QAAA,eAC/BpD,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,OAAO;cAACvB,KAAK,EAAC,gBAAgB;cAAAiB,QAAA,EAC/CJ,IAAI,CAACrB;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdlC,OAAA,CAAC5B,GAAG;YAACyD,EAAE,EAAE;cAAE+B,CAAC,EAAE,CAAC;cAAE2B,EAAE,EAAE;YAAE,CAAE;YAAAnC,QAAA,eACvBpD,OAAA,CAACxB,MAAM;cACLiE,IAAI,EAAC,OAAO;cACZZ,EAAE,EAAE;gBACFM,KAAK,EAAEa,IAAI,CAACb,KAAK;gBACjB,SAAS,EAAE;kBACT0B,OAAO,EAAElF,KAAK,CAACqE,IAAI,CAACb,KAAK,EAAE,GAAG;gBAChC;cACF,CAAE;cAAAiB,QAAA,EAEDJ,IAAI,CAACR,KAAK,KAAK,MAAM,GAAG,aAAa,GAAG;YAAQ;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC,GA9E6BiC,KAAK;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA+ErC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPlC,OAAA,CAACvB,KAAK;MAACoD,EAAE,EAAE;QAAE+B,CAAC,EAAE,CAAC;QAAEV,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,gBACzBpD,OAAA,CAAC7B,UAAU;QAACuF,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAP,QAAA,EAAC;MAEtC;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACblC,OAAA,CAAC3B,IAAI;QAAC2F,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAb,QAAA,gBACzBpD,OAAA,CAAC3B,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eAC9BpD,OAAA,CAAC5B,GAAG;YAACyD,EAAE,EAAE;cAAE2D,SAAS,EAAE;YAAS,CAAE;YAAApC,QAAA,gBAC/BpD,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,IAAI;cAACvB,KAAK,EAAC,cAAc;cAACiD,UAAU,EAAC,MAAM;cAAAhC,QAAA,EAC5D/C,MAAM,CAACI,OAAO,gBAAGT,OAAA,CAACnB,gBAAgB;gBAAC4D,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GAAG7B,MAAM,CAACE;YAAc;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACblC,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,OAAO;cAACvB,KAAK,EAAC,gBAAgB;cAAAiB,QAAA,EAAC;YAEnD;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPlC,OAAA,CAAC3B,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eAC9BpD,OAAA,CAAC5B,GAAG;YAACyD,EAAE,EAAE;cAAE2D,SAAS,EAAE;YAAS,CAAE;YAAApC,QAAA,gBAC/BpD,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,IAAI;cAACvB,KAAK,EAAC,cAAc;cAACiD,UAAU,EAAC,MAAM;cAAAhC,QAAA,EAC5D/C,MAAM,CAACI,OAAO,gBAAGT,OAAA,CAACnB,gBAAgB;gBAAC4D,IAAI,EAAE;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GAAG7B,MAAM,CAACG;YAAuB;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACblC,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,OAAO;cAACvB,KAAK,EAAC,gBAAgB;cAAAiB,QAAA,EAAC;YAEnD;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPlC,OAAA,CAAC3B,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eAC9BpD,OAAA,CAAC5B,GAAG;YAACyD,EAAE,EAAE;cAAE2D,SAAS,EAAE;YAAS,CAAE;YAAApC,QAAA,gBAC/BpD,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,IAAI;cAACvB,KAAK,EAAC,WAAW;cAACiD,UAAU,EAAC,MAAM;cAAAhC,QAAA,EAAC;YAE7D;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblC,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,OAAO;cAACvB,KAAK,EAAC,gBAAgB;cAAAiB,QAAA,EAAC;YAEnD;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPlC,OAAA,CAAC3B,IAAI;UAAC+F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eAC9BpD,OAAA,CAAC5B,GAAG;YAACyD,EAAE,EAAE;cAAE2D,SAAS,EAAE;YAAS,CAAE;YAAApC,QAAA,gBAC/BpD,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,IAAI;cAACvB,KAAK,EAAC,cAAc;cAACiD,UAAU,EAAC,MAAM;cAAAhC,QAAA,EAAC;YAEhE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblC,OAAA,CAAC7B,UAAU;cAACuF,OAAO,EAAC,OAAO;cAACvB,KAAK,EAAC,gBAAgB;cAAAiB,QAAA,EAAC;YAEnD;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAAChC,EAAA,CAzQID,uBAAuB;EAAA,QACbvB,QAAQ,EACMmB,WAAW;AAAA;AAAA4F,EAAA,GAFnCxF,uBAAuB;AA2Q7B,eAAeA,uBAAuB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}