{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects v2\\\\asset management\\\\frontend\\\\src\\\\features\\\\items\\\\SerialVoucherForm.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { Container, Typography, Box, Paper, Breadcrumbs, Link, Alert, Button, Grid, TextField, FormControl, InputLabel, Select, MenuItem, Switch, FormControlLabel, CircularProgress, Divider } from '@mui/material';\nimport { Home as HomeIcon, Receipt as ReceiptIcon, ArrowBack as ArrowBackIcon, Save as SaveIcon, Cancel as CancelIcon, Preview as PreviewIcon } from '@mui/icons-material';\nimport { Link as RouterLink, useNavigate, useParams } from 'react-router-dom';\nimport { useSnackbar } from 'notistack';\nimport api from '../../utils/axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SerialVoucherForm = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    id\n  } = useParams();\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const isEdit = Boolean(id);\n  const [formData, setFormData] = useState({\n    category: '',\n    prefix: '',\n    current_number: 1,\n    number_length: 6,\n    max_number: '',\n    is_active: true\n  });\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Load data on component mount\n  useEffect(() => {\n    loadCategories();\n    if (isEdit) {\n      loadVoucher();\n    }\n  }, [id, isEdit]);\n  const loadCategories = async () => {\n    try {\n      const response = await api.get('/items/serial-voucher-categories/');\n      setCategories(response.data.results || response.data);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      enqueueSnackbar('Failed to load voucher categories', {\n        variant: 'error'\n      });\n    }\n  };\n  const loadVoucher = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get(`/items/serial-vouchers/${id}/`);\n      setFormData(response.data);\n    } catch (error) {\n      console.error('Error loading voucher:', error);\n      enqueueSnackbar('Failed to load voucher', {\n        variant: 'error'\n      });\n      navigate('/items/serial-vouchers');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = useCallback(field => event => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear field error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  }, [errors]);\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.category) {\n      newErrors.category = 'Category is required';\n    }\n    if (!formData.prefix || formData.prefix.trim().length < 1) {\n      newErrors.prefix = 'Prefix is required';\n    }\n    if (formData.prefix && !/^[A-Z0-9]+$/.test(formData.prefix.trim())) {\n      newErrors.prefix = 'Prefix must contain only uppercase letters and numbers';\n    }\n    if (formData.current_number < 1) {\n      newErrors.current_number = 'Current number must be at least 1';\n    }\n    if (formData.number_length < 3 || formData.number_length > 10) {\n      newErrors.number_length = 'Number length must be between 3 and 10';\n    }\n    if (formData.max_number && formData.max_number < formData.current_number) {\n      newErrors.max_number = 'Maximum number must be greater than current number';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    if (!validateForm()) {\n      enqueueSnackbar('Please fix the errors in the form', {\n        variant: 'error'\n      });\n      return;\n    }\n    setSaving(true);\n    try {\n      const submitData = {\n        ...formData,\n        prefix: formData.prefix.trim().toUpperCase(),\n        max_number: formData.max_number || null\n      };\n      if (isEdit) {\n        await api.put(`/items/serial-vouchers/${id}/`, submitData);\n        enqueueSnackbar('Serial voucher updated successfully', {\n          variant: 'success'\n        });\n      } else {\n        await api.post('/items/serial-vouchers/', submitData);\n        enqueueSnackbar('Serial voucher created successfully', {\n          variant: 'success'\n        });\n      }\n      navigate('/items/serial-vouchers');\n    } catch (error) {\n      var _error$response;\n      console.error('Error saving voucher:', error);\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && _error$response.data) {\n        const serverErrors = error.response.data;\n        setErrors(serverErrors);\n        enqueueSnackbar('Please fix the errors and try again', {\n          variant: 'error'\n        });\n      } else {\n        enqueueSnackbar('Failed to save voucher', {\n          variant: 'error'\n        });\n      }\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleCancel = () => {\n    navigate('/items/serial-vouchers');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      sx: {\n        py: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        minHeight: \"400px\",\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 60\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            ml: 3\n          },\n          children: \"Loading voucher...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Breadcrumbs, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        component: RouterLink,\n        to: \"/dashboard\",\n        color: \"inherit\",\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(HomeIcon, {\n          sx: {\n            mr: 0.5\n          },\n          fontSize: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), \"Dashboard\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        component: RouterLink,\n        to: \"/item-management-menu\",\n        color: \"inherit\",\n        children: \"Item Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        component: RouterLink,\n        to: \"/items/serial-vouchers\",\n        color: \"inherit\",\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(ReceiptIcon, {\n          sx: {\n            mr: 0.5\n          },\n          fontSize: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), \"Serial Vouchers\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        color: \"text.primary\",\n        children: isEdit ? 'Edit Voucher' : 'New Voucher'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 22\n        }, this),\n        onClick: () => navigate('/items/serial-vouchers'),\n        sx: {\n          mb: 2\n        },\n        children: \"Back to Serial Vouchers\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h3\",\n        component: \"h1\",\n        gutterBottom: true,\n        fontWeight: \"bold\",\n        children: isEdit ? 'Edit Serial Voucher' : 'Create Serial Voucher'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: isEdit ? 'Update serial voucher configuration' : 'Set up a new serial voucher for automated numbering'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Basic Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              required: true,\n              error: !!errors.category,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.category,\n                onChange: handleInputChange('category'),\n                label: \"Category\",\n                disabled: saving,\n                children: categories.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: category.id,\n                  children: category.title\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), errors.category && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"error\",\n                sx: {\n                  mt: 0.5\n                },\n                children: errors.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              required: true,\n              label: \"Prefix\",\n              value: formData.prefix,\n              onChange: handleInputChange('prefix'),\n              placeholder: \"e.g., INV, BATCH, ASSET\",\n              error: !!errors.prefix,\n              helperText: errors.prefix || \"Prefix for generated serial numbers (uppercase letters and numbers only)\",\n              disabled: saving,\n              inputProps: {\n                maxLength: 10\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              required: true,\n              label: \"Current Number\",\n              type: \"number\",\n              value: formData.current_number,\n              onChange: handleInputChange('current_number'),\n              error: !!errors.current_number,\n              helperText: errors.current_number || \"Next number to be issued\",\n              disabled: saving,\n              inputProps: {\n                min: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              required: true,\n              label: \"Number Length\",\n              type: \"number\",\n              value: formData.number_length,\n              onChange: handleInputChange('number_length'),\n              error: !!errors.number_length,\n              helperText: errors.number_length || \"Total digits in the number (3-10)\",\n              disabled: saving,\n              inputProps: {\n                min: 3,\n                max: 10\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Maximum Number\",\n              type: \"number\",\n              value: formData.max_number,\n              onChange: handleInputChange('max_number'),\n              error: !!errors.max_number,\n              helperText: errors.max_number || \"Optional maximum limit\",\n              disabled: saving,\n              inputProps: {\n                min: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: formData.is_active,\n                onChange: handleInputChange('is_active'),\n                disabled: saving\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this),\n              label: \"Active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 3,\n                bgcolor: 'grey.50',\n                border: '1px solid',\n                borderColor: 'grey.300'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(PreviewIcon, {\n                  sx: {\n                    mr: 1,\n                    color: 'primary.main'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary.main\",\n                  children: \"Serial Number Preview\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"Next Serial Number:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"primary.main\",\n                fontWeight: \"bold\",\n                sx: {\n                  fontFamily: 'monospace'\n                },\n                children: formData.prefix && formData.current_number && formData.number_length ? `${formData.prefix.toUpperCase()}${String(formData.current_number).padStart(formData.number_length, '0')}` : 'Enter prefix and numbers to preview'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), formData.max_number && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mt: 1\n                },\n                children: [\"Maximum: \", formData.prefix.toUpperCase(), String(formData.max_number).padStart(formData.number_length, '0')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                justifyContent: 'flex-end',\n                mt: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: handleCancel,\n                startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 30\n                }, this),\n                disabled: saving,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"contained\",\n                startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 30\n                }, this),\n                disabled: saving,\n                children: saving ? 'Saving...' : isEdit ? 'Update Voucher' : 'Create Voucher'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 5\n  }, this);\n};\n_s(SerialVoucherForm, \"iou1g6QPKZ54FoZUJNXQ29OopuQ=\", false, function () {\n  return [useNavigate, useParams, useSnackbar];\n});\n_c = SerialVoucherForm;\nexport default SerialVoucherForm;\nvar _c;\n$RefreshReg$(_c, \"SerialVoucherForm\");", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "Container", "Typography", "Box", "Paper", "Breadcrumbs", "Link", "<PERSON><PERSON>", "<PERSON><PERSON>", "Grid", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Switch", "FormControlLabel", "CircularProgress", "Divider", "Home", "HomeIcon", "Receipt", "ReceiptIcon", "ArrowBack", "ArrowBackIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Preview", "PreviewIcon", "RouterLink", "useNavigate", "useParams", "useSnackbar", "api", "jsxDEV", "_jsxDEV", "SerialVoucherForm", "_s", "navigate", "id", "enqueueSnackbar", "isEdit", "Boolean", "formData", "setFormData", "category", "prefix", "current_number", "number_length", "max_number", "is_active", "categories", "setCategories", "loading", "setLoading", "saving", "setSaving", "errors", "setErrors", "loadCategories", "loadVoucher", "response", "get", "data", "results", "error", "console", "variant", "handleInputChange", "field", "event", "value", "target", "type", "checked", "prev", "validateForm", "newErrors", "trim", "length", "test", "Object", "keys", "handleSubmit", "preventDefault", "submitData", "toUpperCase", "put", "post", "_error$response", "serverErrors", "handleCancel", "max<PERSON><PERSON><PERSON>", "sx", "py", "children", "display", "justifyContent", "alignItems", "minHeight", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ml", "mb", "component", "to", "color", "mr", "fontSize", "startIcon", "onClick", "gutterBottom", "fontWeight", "p", "onSubmit", "container", "spacing", "item", "xs", "md", "fullWidth", "required", "onChange", "label", "disabled", "map", "title", "mt", "placeholder", "helperText", "inputProps", "max<PERSON><PERSON><PERSON>", "min", "max", "control", "my", "bgcolor", "border", "borderColor", "fontFamily", "String", "padStart", "gap", "_c", "$RefreshReg$"], "sources": ["D:/Projects v2/asset management/frontend/src/features/items/SerialVoucherForm.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport {\n  Container,\n  Typography,\n  Box,\n  Paper,\n  Breadcrumbs,\n  Link,\n  Alert,\n  Button,\n  Grid,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Switch,\n  FormControlLabel,\n  CircularProgress,\n  Divider\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  Receipt as ReceiptIcon,\n  ArrowBack as ArrowBackIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Preview as PreviewIcon\n} from '@mui/icons-material';\nimport { Link as RouterLink, useNavigate, useParams } from 'react-router-dom';\nimport { useSnackbar } from 'notistack';\nimport api from '../../utils/axios';\n\nconst SerialVoucherForm = () => {\n  const navigate = useNavigate();\n  const { id } = useParams();\n  const { enqueueSnackbar } = useSnackbar();\n  const isEdit = Boolean(id);\n\n  const [formData, setFormData] = useState({\n    category: '',\n    prefix: '',\n    current_number: 1,\n    number_length: 6,\n    max_number: '',\n    is_active: true\n  });\n\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Load data on component mount\n  useEffect(() => {\n    loadCategories();\n    if (isEdit) {\n      loadVoucher();\n    }\n  }, [id, isEdit]);\n\n  const loadCategories = async () => {\n    try {\n      const response = await api.get('/items/serial-voucher-categories/');\n      setCategories(response.data.results || response.data);\n    } catch (error) {\n      console.error('Error loading categories:', error);\n      enqueueSnackbar('Failed to load voucher categories', { variant: 'error' });\n    }\n  };\n\n  const loadVoucher = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get(`/items/serial-vouchers/${id}/`);\n      setFormData(response.data);\n    } catch (error) {\n      console.error('Error loading voucher:', error);\n      enqueueSnackbar('Failed to load voucher', { variant: 'error' });\n      navigate('/items/serial-vouchers');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = useCallback((field) => (event) => {\n    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear field error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  }, [errors]);\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.category) {\n      newErrors.category = 'Category is required';\n    }\n    if (!formData.prefix || formData.prefix.trim().length < 1) {\n      newErrors.prefix = 'Prefix is required';\n    }\n    if (formData.prefix && !/^[A-Z0-9]+$/.test(formData.prefix.trim())) {\n      newErrors.prefix = 'Prefix must contain only uppercase letters and numbers';\n    }\n    if (formData.current_number < 1) {\n      newErrors.current_number = 'Current number must be at least 1';\n    }\n    if (formData.number_length < 3 || formData.number_length > 10) {\n      newErrors.number_length = 'Number length must be between 3 and 10';\n    }\n    if (formData.max_number && formData.max_number < formData.current_number) {\n      newErrors.max_number = 'Maximum number must be greater than current number';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    if (!validateForm()) {\n      enqueueSnackbar('Please fix the errors in the form', { variant: 'error' });\n      return;\n    }\n\n    setSaving(true);\n    try {\n      const submitData = {\n        ...formData,\n        prefix: formData.prefix.trim().toUpperCase(),\n        max_number: formData.max_number || null\n      };\n\n      if (isEdit) {\n        await api.put(`/items/serial-vouchers/${id}/`, submitData);\n        enqueueSnackbar('Serial voucher updated successfully', { variant: 'success' });\n      } else {\n        await api.post('/items/serial-vouchers/', submitData);\n        enqueueSnackbar('Serial voucher created successfully', { variant: 'success' });\n      }\n\n      navigate('/items/serial-vouchers');\n    } catch (error) {\n      console.error('Error saving voucher:', error);\n      if (error.response?.data) {\n        const serverErrors = error.response.data;\n        setErrors(serverErrors);\n        enqueueSnackbar('Please fix the errors and try again', { variant: 'error' });\n      } else {\n        enqueueSnackbar('Failed to save voucher', { variant: 'error' });\n      }\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleCancel = () => {\n    navigate('/items/serial-vouchers');\n  };\n\n  if (loading) {\n    return (\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n          <CircularProgress size={60} />\n          <Typography variant=\"h6\" sx={{ ml: 3 }}>\n            Loading voucher...\n          </Typography>\n        </Box>\n      </Container>\n    );\n  }\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n      {/* Breadcrumbs */}\n      <Breadcrumbs sx={{ mb: 3 }}>\n        <Link\n          component={RouterLink}\n          to=\"/dashboard\"\n          color=\"inherit\"\n          sx={{ display: 'flex', alignItems: 'center' }}\n        >\n          <HomeIcon sx={{ mr: 0.5 }} fontSize=\"inherit\" />\n          Dashboard\n        </Link>\n        <Link\n          component={RouterLink}\n          to=\"/item-management-menu\"\n          color=\"inherit\"\n        >\n          Item Management\n        </Link>\n        <Link\n          component={RouterLink}\n          to=\"/items/serial-vouchers\"\n          color=\"inherit\"\n          sx={{ display: 'flex', alignItems: 'center' }}\n        >\n          <ReceiptIcon sx={{ mr: 0.5 }} fontSize=\"inherit\" />\n          Serial Vouchers\n        </Link>\n        <Typography color=\"text.primary\">\n          {isEdit ? 'Edit Voucher' : 'New Voucher'}\n        </Typography>\n      </Breadcrumbs>\n\n      {/* Header */}\n      <Box sx={{ mb: 4 }}>\n        <Button\n          startIcon={<ArrowBackIcon />}\n          onClick={() => navigate('/items/serial-vouchers')}\n          sx={{ mb: 2 }}\n        >\n          Back to Serial Vouchers\n        </Button>\n        <Typography variant=\"h3\" component=\"h1\" gutterBottom fontWeight=\"bold\">\n          {isEdit ? 'Edit Serial Voucher' : 'Create Serial Voucher'}\n        </Typography>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          {isEdit ? 'Update serial voucher configuration' : 'Set up a new serial voucher for automated numbering'}\n        </Typography>\n      </Box>\n\n\n\n      {/* Form */}\n      <Paper sx={{ p: 4 }}>\n        <form onSubmit={handleSubmit}>\n          <Grid container spacing={3}>\n            {/* Basic Information */}\n            <Grid item xs={12}>\n              <Typography variant=\"h6\" gutterBottom>\n                Basic Information\n              </Typography>\n            </Grid>\n            \n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth required error={!!errors.category}>\n                <InputLabel>Category</InputLabel>\n                <Select\n                  value={formData.category}\n                  onChange={handleInputChange('category')}\n                  label=\"Category\"\n                  disabled={saving}\n                >\n                  {categories.map((category) => (\n                    <MenuItem key={category.id} value={category.id}>\n                      {category.title}\n                    </MenuItem>\n                  ))}\n                </Select>\n                {errors.category && (\n                  <Typography variant=\"caption\" color=\"error\" sx={{ mt: 0.5 }}>\n                    {errors.category}\n                  </Typography>\n                )}\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                required\n                label=\"Prefix\"\n                value={formData.prefix}\n                onChange={handleInputChange('prefix')}\n                placeholder=\"e.g., INV, BATCH, ASSET\"\n                error={!!errors.prefix}\n                helperText={errors.prefix || \"Prefix for generated serial numbers (uppercase letters and numbers only)\"}\n                disabled={saving}\n                inputProps={{ maxLength: 10 }}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                required\n                label=\"Current Number\"\n                type=\"number\"\n                value={formData.current_number}\n                onChange={handleInputChange('current_number')}\n                error={!!errors.current_number}\n                helperText={errors.current_number || \"Next number to be issued\"}\n                disabled={saving}\n                inputProps={{ min: 1 }}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                required\n                label=\"Number Length\"\n                type=\"number\"\n                value={formData.number_length}\n                onChange={handleInputChange('number_length')}\n                error={!!errors.number_length}\n                helperText={errors.number_length || \"Total digits in the number (3-10)\"}\n                disabled={saving}\n                inputProps={{ min: 3, max: 10 }}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Maximum Number\"\n                type=\"number\"\n                value={formData.max_number}\n                onChange={handleInputChange('max_number')}\n                error={!!errors.max_number}\n                helperText={errors.max_number || \"Optional maximum limit\"}\n                disabled={saving}\n                inputProps={{ min: 1 }}\n              />\n            </Grid>\n\n            <Grid item xs={12}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={formData.is_active}\n                    onChange={handleInputChange('is_active')}\n                    disabled={saving}\n                  />\n                }\n                label=\"Active\"\n              />\n            </Grid>\n\n            {/* Preview */}\n            <Grid item xs={12}>\n              <Divider sx={{ my: 2 }} />\n              <Paper sx={{ p: 3, bgcolor: 'grey.50', border: '1px solid', borderColor: 'grey.300' }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                  <PreviewIcon sx={{ mr: 1, color: 'primary.main' }} />\n                  <Typography variant=\"h6\" color=\"primary.main\">\n                    Serial Number Preview\n                  </Typography>\n                </Box>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Next Serial Number:\n                </Typography>\n                <Typography variant=\"h4\" color=\"primary.main\" fontWeight=\"bold\" sx={{ fontFamily: 'monospace' }}>\n                  {formData.prefix && formData.current_number && formData.number_length ?\n                    `${formData.prefix.toUpperCase()}${String(formData.current_number).padStart(formData.number_length, '0')}` :\n                    'Enter prefix and numbers to preview'\n                  }\n                </Typography>\n                {formData.max_number && (\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                    Maximum: {formData.prefix.toUpperCase()}{String(formData.max_number).padStart(formData.number_length, '0')}\n                  </Typography>\n                )}\n              </Paper>\n            </Grid>\n\n            {/* Action Buttons */}\n            <Grid item xs={12}>\n              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>\n                <Button\n                  variant=\"outlined\"\n                  onClick={handleCancel}\n                  startIcon={<CancelIcon />}\n                  disabled={saving}\n                >\n                  Cancel\n                </Button>\n                <Button\n                  type=\"submit\"\n                  variant=\"contained\"\n                  startIcon={<SaveIcon />}\n                  disabled={saving}\n                >\n                  {saving ? 'Saving...' : (isEdit ? 'Update Voucher' : 'Create Voucher')}\n                </Button>\n              </Box>\n            </Grid>\n          </Grid>\n        </form>\n      </Paper>\n\n\n    </Container>\n  );\n};\n\nexport default SerialVoucherForm;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SACEC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,gBAAgB,EAChBC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASxB,IAAI,IAAIyB,UAAU,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AAC7E,SAASC,WAAW,QAAQ,WAAW;AACvC,OAAOC,GAAG,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAG,CAAC,GAAGR,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAES;EAAgB,CAAC,GAAGR,WAAW,CAAC,CAAC;EACzC,MAAMS,MAAM,GAAGC,OAAO,CAACH,EAAE,CAAC;EAE1B,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC;IACvCiD,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2D,MAAM,EAAEC,SAAS,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC6D,MAAM,EAAEC,SAAS,CAAC,GAAG9D,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd8D,cAAc,CAAC,CAAC;IAChB,IAAIlB,MAAM,EAAE;MACVmB,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACrB,EAAE,EAAEE,MAAM,CAAC,CAAC;EAEhB,MAAMkB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAM5B,GAAG,CAAC6B,GAAG,CAAC,mCAAmC,CAAC;MACnEV,aAAa,CAACS,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAIH,QAAQ,CAACE,IAAI,CAAC;IACvD,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDzB,eAAe,CAAC,mCAAmC,EAAE;QAAE2B,OAAO,EAAE;MAAQ,CAAC,CAAC;IAC5E;EACF,CAAC;EAED,MAAMP,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BN,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAM5B,GAAG,CAAC6B,GAAG,CAAC,0BAA0BvB,EAAE,GAAG,CAAC;MAC/DK,WAAW,CAACiB,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CzB,eAAe,CAAC,wBAAwB,EAAE;QAAE2B,OAAO,EAAE;MAAQ,CAAC,CAAC;MAC/D7B,QAAQ,CAAC,wBAAwB,CAAC;IACpC,CAAC,SAAS;MACRgB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,iBAAiB,GAAGtE,WAAW,CAAEuE,KAAK,IAAMC,KAAK,IAAK;IAC1D,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,KAAK,UAAU,GAAGH,KAAK,CAACE,MAAM,CAACE,OAAO,GAAGJ,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1F3B,WAAW,CAAC+B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACN,KAAK,GAAGE;IACX,CAAC,CAAC,CAAC;IACH;IACA,IAAId,MAAM,CAACY,KAAK,CAAC,EAAE;MACjBX,SAAS,CAACiB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACN,KAAK,GAAG;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACZ,MAAM,CAAC,CAAC;EAEZ,MAAMmB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAClC,QAAQ,CAACE,QAAQ,EAAE;MACtBgC,SAAS,CAAChC,QAAQ,GAAG,sBAAsB;IAC7C;IACA,IAAI,CAACF,QAAQ,CAACG,MAAM,IAAIH,QAAQ,CAACG,MAAM,CAACgC,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MACzDF,SAAS,CAAC/B,MAAM,GAAG,oBAAoB;IACzC;IACA,IAAIH,QAAQ,CAACG,MAAM,IAAI,CAAC,aAAa,CAACkC,IAAI,CAACrC,QAAQ,CAACG,MAAM,CAACgC,IAAI,CAAC,CAAC,CAAC,EAAE;MAClED,SAAS,CAAC/B,MAAM,GAAG,wDAAwD;IAC7E;IACA,IAAIH,QAAQ,CAACI,cAAc,GAAG,CAAC,EAAE;MAC/B8B,SAAS,CAAC9B,cAAc,GAAG,mCAAmC;IAChE;IACA,IAAIJ,QAAQ,CAACK,aAAa,GAAG,CAAC,IAAIL,QAAQ,CAACK,aAAa,GAAG,EAAE,EAAE;MAC7D6B,SAAS,CAAC7B,aAAa,GAAG,wCAAwC;IACpE;IACA,IAAIL,QAAQ,CAACM,UAAU,IAAIN,QAAQ,CAACM,UAAU,GAAGN,QAAQ,CAACI,cAAc,EAAE;MACxE8B,SAAS,CAAC5B,UAAU,GAAG,oDAAoD;IAC7E;IAEAS,SAAS,CAACmB,SAAS,CAAC;IACpB,OAAOI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOb,KAAK,IAAK;IACpCA,KAAK,CAACc,cAAc,CAAC,CAAC;IAEtB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;MACnBpC,eAAe,CAAC,mCAAmC,EAAE;QAAE2B,OAAO,EAAE;MAAQ,CAAC,CAAC;MAC1E;IACF;IAEAX,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF,MAAM6B,UAAU,GAAG;QACjB,GAAG1C,QAAQ;QACXG,MAAM,EAAEH,QAAQ,CAACG,MAAM,CAACgC,IAAI,CAAC,CAAC,CAACQ,WAAW,CAAC,CAAC;QAC5CrC,UAAU,EAAEN,QAAQ,CAACM,UAAU,IAAI;MACrC,CAAC;MAED,IAAIR,MAAM,EAAE;QACV,MAAMR,GAAG,CAACsD,GAAG,CAAC,0BAA0BhD,EAAE,GAAG,EAAE8C,UAAU,CAAC;QAC1D7C,eAAe,CAAC,qCAAqC,EAAE;UAAE2B,OAAO,EAAE;QAAU,CAAC,CAAC;MAChF,CAAC,MAAM;QACL,MAAMlC,GAAG,CAACuD,IAAI,CAAC,yBAAyB,EAAEH,UAAU,CAAC;QACrD7C,eAAe,CAAC,qCAAqC,EAAE;UAAE2B,OAAO,EAAE;QAAU,CAAC,CAAC;MAChF;MAEA7B,QAAQ,CAAC,wBAAwB,CAAC;IACpC,CAAC,CAAC,OAAO2B,KAAK,EAAE;MAAA,IAAAwB,eAAA;MACdvB,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,KAAAwB,eAAA,GAAIxB,KAAK,CAACJ,QAAQ,cAAA4B,eAAA,eAAdA,eAAA,CAAgB1B,IAAI,EAAE;QACxB,MAAM2B,YAAY,GAAGzB,KAAK,CAACJ,QAAQ,CAACE,IAAI;QACxCL,SAAS,CAACgC,YAAY,CAAC;QACvBlD,eAAe,CAAC,qCAAqC,EAAE;UAAE2B,OAAO,EAAE;QAAQ,CAAC,CAAC;MAC9E,CAAC,MAAM;QACL3B,eAAe,CAAC,wBAAwB,EAAE;UAAE2B,OAAO,EAAE;QAAQ,CAAC,CAAC;MACjE;IACF,CAAC,SAAS;MACRX,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMmC,YAAY,GAAGA,CAAA,KAAM;IACzBrD,QAAQ,CAAC,wBAAwB,CAAC;EACpC,CAAC;EAED,IAAIe,OAAO,EAAE;IACX,oBACElB,OAAA,CAACpC,SAAS;MAAC6F,QAAQ,EAAC,IAAI;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eACrC5D,OAAA,CAAClC,GAAG;QAAC+F,OAAO,EAAC,MAAM;QAACC,cAAc,EAAC,QAAQ;QAACC,UAAU,EAAC,QAAQ;QAACC,SAAS,EAAC,OAAO;QAAAJ,QAAA,gBAC/E5D,OAAA,CAACpB,gBAAgB;UAACqF,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9BrE,OAAA,CAACnC,UAAU;UAACmE,OAAO,EAAC,IAAI;UAAC0B,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAV,QAAA,EAAC;QAExC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEhB;EAEA,oBACErE,OAAA,CAACpC,SAAS;IAAC6F,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAErC5D,OAAA,CAAChC,WAAW;MAAC0F,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBACzB5D,OAAA,CAAC/B,IAAI;QACHuG,SAAS,EAAE9E,UAAW;QACtB+E,EAAE,EAAC,YAAY;QACfC,KAAK,EAAC,SAAS;QACfhB,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE;QAAS,CAAE;QAAAH,QAAA,gBAE9C5D,OAAA,CAACjB,QAAQ;UAAC2E,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAI,CAAE;UAACC,QAAQ,EAAC;QAAS;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAElD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPrE,OAAA,CAAC/B,IAAI;QACHuG,SAAS,EAAE9E,UAAW;QACtB+E,EAAE,EAAC,uBAAuB;QAC1BC,KAAK,EAAC,SAAS;QAAAd,QAAA,EAChB;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPrE,OAAA,CAAC/B,IAAI;QACHuG,SAAS,EAAE9E,UAAW;QACtB+E,EAAE,EAAC,wBAAwB;QAC3BC,KAAK,EAAC,SAAS;QACfhB,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE;QAAS,CAAE;QAAAH,QAAA,gBAE9C5D,OAAA,CAACf,WAAW;UAACyE,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAI,CAAE;UAACC,QAAQ,EAAC;QAAS;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,mBAErD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPrE,OAAA,CAACnC,UAAU;QAAC6G,KAAK,EAAC,cAAc;QAAAd,QAAA,EAC7BtD,MAAM,GAAG,cAAc,GAAG;MAAa;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGdrE,OAAA,CAAClC,GAAG;MAAC4F,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBACjB5D,OAAA,CAAC7B,MAAM;QACL0G,SAAS,eAAE7E,OAAA,CAACb,aAAa;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BS,OAAO,EAAEA,CAAA,KAAM3E,QAAQ,CAAC,wBAAwB,CAAE;QAClDuD,EAAE,EAAE;UAAEa,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,EACf;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrE,OAAA,CAACnC,UAAU;QAACmE,OAAO,EAAC,IAAI;QAACwC,SAAS,EAAC,IAAI;QAACO,YAAY;QAACC,UAAU,EAAC,MAAM;QAAApB,QAAA,EACnEtD,MAAM,GAAG,qBAAqB,GAAG;MAAuB;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACbrE,OAAA,CAACnC,UAAU;QAACmE,OAAO,EAAC,IAAI;QAAC0C,KAAK,EAAC,gBAAgB;QAAAd,QAAA,EAC5CtD,MAAM,GAAG,qCAAqC,GAAG;MAAqD;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAKNrE,OAAA,CAACjC,KAAK;MAAC2F,EAAE,EAAE;QAAEuB,CAAC,EAAE;MAAE,CAAE;MAAArB,QAAA,eAClB5D,OAAA;QAAMkF,QAAQ,EAAElC,YAAa;QAAAY,QAAA,eAC3B5D,OAAA,CAAC5B,IAAI;UAAC+G,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAxB,QAAA,gBAEzB5D,OAAA,CAAC5B,IAAI;YAACiH,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChB5D,OAAA,CAACnC,UAAU;cAACmE,OAAO,EAAC,IAAI;cAAC+C,YAAY;cAAAnB,QAAA,EAAC;YAEtC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEPrE,OAAA,CAAC5B,IAAI;YAACiH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5D,OAAA,CAAC1B,WAAW;cAACkH,SAAS;cAACC,QAAQ;cAAC3D,KAAK,EAAE,CAAC,CAACR,MAAM,CAACZ,QAAS;cAAAkD,QAAA,gBACvD5D,OAAA,CAACzB,UAAU;gBAAAqF,QAAA,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjCrE,OAAA,CAACxB,MAAM;gBACL4D,KAAK,EAAE5B,QAAQ,CAACE,QAAS;gBACzBgF,QAAQ,EAAEzD,iBAAiB,CAAC,UAAU,CAAE;gBACxC0D,KAAK,EAAC,UAAU;gBAChBC,QAAQ,EAAExE,MAAO;gBAAAwC,QAAA,EAEhB5C,UAAU,CAAC6E,GAAG,CAAEnF,QAAQ,iBACvBV,OAAA,CAACvB,QAAQ;kBAAmB2D,KAAK,EAAE1B,QAAQ,CAACN,EAAG;kBAAAwD,QAAA,EAC5ClD,QAAQ,CAACoF;gBAAK,GADFpF,QAAQ,CAACN,EAAE;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACR/C,MAAM,CAACZ,QAAQ,iBACdV,OAAA,CAACnC,UAAU;gBAACmE,OAAO,EAAC,SAAS;gBAAC0C,KAAK,EAAC,OAAO;gBAAChB,EAAE,EAAE;kBAAEqC,EAAE,EAAE;gBAAI,CAAE;gBAAAnC,QAAA,EACzDtC,MAAM,CAACZ;cAAQ;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPrE,OAAA,CAAC5B,IAAI;YAACiH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5D,OAAA,CAAC3B,SAAS;cACRmH,SAAS;cACTC,QAAQ;cACRE,KAAK,EAAC,QAAQ;cACdvD,KAAK,EAAE5B,QAAQ,CAACG,MAAO;cACvB+E,QAAQ,EAAEzD,iBAAiB,CAAC,QAAQ,CAAE;cACtC+D,WAAW,EAAC,yBAAyB;cACrClE,KAAK,EAAE,CAAC,CAACR,MAAM,CAACX,MAAO;cACvBsF,UAAU,EAAE3E,MAAM,CAACX,MAAM,IAAI,0EAA2E;cACxGiF,QAAQ,EAAExE,MAAO;cACjB8E,UAAU,EAAE;gBAAEC,SAAS,EAAE;cAAG;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrE,OAAA,CAAC5B,IAAI;YAACiH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5D,OAAA,CAAC3B,SAAS;cACRmH,SAAS;cACTC,QAAQ;cACRE,KAAK,EAAC,gBAAgB;cACtBrD,IAAI,EAAC,QAAQ;cACbF,KAAK,EAAE5B,QAAQ,CAACI,cAAe;cAC/B8E,QAAQ,EAAEzD,iBAAiB,CAAC,gBAAgB,CAAE;cAC9CH,KAAK,EAAE,CAAC,CAACR,MAAM,CAACV,cAAe;cAC/BqF,UAAU,EAAE3E,MAAM,CAACV,cAAc,IAAI,0BAA2B;cAChEgF,QAAQ,EAAExE,MAAO;cACjB8E,UAAU,EAAE;gBAAEE,GAAG,EAAE;cAAE;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrE,OAAA,CAAC5B,IAAI;YAACiH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5D,OAAA,CAAC3B,SAAS;cACRmH,SAAS;cACTC,QAAQ;cACRE,KAAK,EAAC,eAAe;cACrBrD,IAAI,EAAC,QAAQ;cACbF,KAAK,EAAE5B,QAAQ,CAACK,aAAc;cAC9B6E,QAAQ,EAAEzD,iBAAiB,CAAC,eAAe,CAAE;cAC7CH,KAAK,EAAE,CAAC,CAACR,MAAM,CAACT,aAAc;cAC9BoF,UAAU,EAAE3E,MAAM,CAACT,aAAa,IAAI,mCAAoC;cACxE+E,QAAQ,EAAExE,MAAO;cACjB8E,UAAU,EAAE;gBAAEE,GAAG,EAAE,CAAC;gBAAEC,GAAG,EAAE;cAAG;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrE,OAAA,CAAC5B,IAAI;YAACiH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5D,OAAA,CAAC3B,SAAS;cACRmH,SAAS;cACTG,KAAK,EAAC,gBAAgB;cACtBrD,IAAI,EAAC,QAAQ;cACbF,KAAK,EAAE5B,QAAQ,CAACM,UAAW;cAC3B4E,QAAQ,EAAEzD,iBAAiB,CAAC,YAAY,CAAE;cAC1CH,KAAK,EAAE,CAAC,CAACR,MAAM,CAACR,UAAW;cAC3BmF,UAAU,EAAE3E,MAAM,CAACR,UAAU,IAAI,wBAAyB;cAC1D8E,QAAQ,EAAExE,MAAO;cACjB8E,UAAU,EAAE;gBAAEE,GAAG,EAAE;cAAE;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPrE,OAAA,CAAC5B,IAAI;YAACiH,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChB5D,OAAA,CAACrB,gBAAgB;cACf2H,OAAO,eACLtG,OAAA,CAACtB,MAAM;gBACL6D,OAAO,EAAE/B,QAAQ,CAACO,SAAU;gBAC5B2E,QAAQ,EAAEzD,iBAAiB,CAAC,WAAW,CAAE;gBACzC2D,QAAQ,EAAExE;cAAO;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CACF;cACDsB,KAAK,EAAC;YAAQ;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGPrE,OAAA,CAAC5B,IAAI;YAACiH,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,gBAChB5D,OAAA,CAACnB,OAAO;cAAC6E,EAAE,EAAE;gBAAE6C,EAAE,EAAE;cAAE;YAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1BrE,OAAA,CAACjC,KAAK;cAAC2F,EAAE,EAAE;gBAAEuB,CAAC,EAAE,CAAC;gBAAEuB,OAAO,EAAE,SAAS;gBAAEC,MAAM,EAAE,WAAW;gBAAEC,WAAW,EAAE;cAAW,CAAE;cAAA9C,QAAA,gBACpF5D,OAAA,CAAClC,GAAG;gBAAC4F,EAAE,EAAE;kBAAEG,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEQ,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,gBACxD5D,OAAA,CAACP,WAAW;kBAACiE,EAAE,EAAE;oBAAEiB,EAAE,EAAE,CAAC;oBAAED,KAAK,EAAE;kBAAe;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrDrE,OAAA,CAACnC,UAAU;kBAACmE,OAAO,EAAC,IAAI;kBAAC0C,KAAK,EAAC,cAAc;kBAAAd,QAAA,EAAC;gBAE9C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNrE,OAAA,CAACnC,UAAU;gBAACmE,OAAO,EAAC,WAAW;gBAAC+C,YAAY;gBAAAnB,QAAA,EAAC;cAE7C;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrE,OAAA,CAACnC,UAAU;gBAACmE,OAAO,EAAC,IAAI;gBAAC0C,KAAK,EAAC,cAAc;gBAACM,UAAU,EAAC,MAAM;gBAACtB,EAAE,EAAE;kBAAEiD,UAAU,EAAE;gBAAY,CAAE;gBAAA/C,QAAA,EAC7FpD,QAAQ,CAACG,MAAM,IAAIH,QAAQ,CAACI,cAAc,IAAIJ,QAAQ,CAACK,aAAa,GACnE,GAAGL,QAAQ,CAACG,MAAM,CAACwC,WAAW,CAAC,CAAC,GAAGyD,MAAM,CAACpG,QAAQ,CAACI,cAAc,CAAC,CAACiG,QAAQ,CAACrG,QAAQ,CAACK,aAAa,EAAE,GAAG,CAAC,EAAE,GAC1G;cAAqC;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE7B,CAAC,EACZ7D,QAAQ,CAACM,UAAU,iBAClBd,OAAA,CAACnC,UAAU;gBAACmE,OAAO,EAAC,OAAO;gBAAC0C,KAAK,EAAC,gBAAgB;gBAAChB,EAAE,EAAE;kBAAEqC,EAAE,EAAE;gBAAE,CAAE;gBAAAnC,QAAA,GAAC,WACvD,EAACpD,QAAQ,CAACG,MAAM,CAACwC,WAAW,CAAC,CAAC,EAAEyD,MAAM,CAACpG,QAAQ,CAACM,UAAU,CAAC,CAAC+F,QAAQ,CAACrG,QAAQ,CAACK,aAAa,EAAE,GAAG,CAAC;cAAA;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGPrE,OAAA,CAAC5B,IAAI;YAACiH,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChB5D,OAAA,CAAClC,GAAG;cAAC4F,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEiD,GAAG,EAAE,CAAC;gBAAEhD,cAAc,EAAE,UAAU;gBAAEiC,EAAE,EAAE;cAAE,CAAE;cAAAnC,QAAA,gBACtE5D,OAAA,CAAC7B,MAAM;gBACL6D,OAAO,EAAC,UAAU;gBAClB8C,OAAO,EAAEtB,YAAa;gBACtBqB,SAAS,eAAE7E,OAAA,CAACT,UAAU;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BuB,QAAQ,EAAExE,MAAO;gBAAAwC,QAAA,EAClB;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrE,OAAA,CAAC7B,MAAM;gBACLmE,IAAI,EAAC,QAAQ;gBACbN,OAAO,EAAC,WAAW;gBACnB6C,SAAS,eAAE7E,OAAA,CAACX,QAAQ;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACxBuB,QAAQ,EAAExE,MAAO;gBAAAwC,QAAA,EAEhBxC,MAAM,GAAG,WAAW,GAAId,MAAM,GAAG,gBAAgB,GAAG;cAAiB;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGC,CAAC;AAEhB,CAAC;AAACnE,EAAA,CA3WID,iBAAiB;EAAA,QACJN,WAAW,EACbC,SAAS,EACIC,WAAW;AAAA;AAAAkH,EAAA,GAHnC9G,iBAAiB;AA6WvB,eAAeA,iBAAiB;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}