import { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  CircularProgress,
  Breadcrumbs,
  Link,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  Grid,
  Avatar
} from '@mui/material';
import {
  Add as AddIcon,
  Category as CategoryIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Home as HomeIcon,
  Business as BusinessIcon,
  Refresh as RefreshIcon,
  ColorLens as ColorIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { Link as RouterLink } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import api from '../../utils/axios';

const SupplierTypesList = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  
  const [supplierTypes, setSupplierTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [formDialog, setFormDialog] = useState({ open: false, supplierType: null });
  const [deleteDialog, setDeleteDialog] = useState({ open: false, supplierType: null });
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    color_code: '#2196F3',
    is_active: true
  });

  useEffect(() => {
    loadSupplierTypes();
  }, []);

  const loadSupplierTypes = async () => {
    setLoading(true);
    try {
      const response = await api.get('/supplier-types/');
      setSupplierTypes(response.data.results || response.data);
    } catch (error) {
      console.error('Error loading supplier types:', error);
      enqueueSnackbar('Failed to load supplier types', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenForm = (supplierType = null) => {
    if (supplierType) {
      setFormData({
        name: supplierType.name,
        code: supplierType.code,
        description: supplierType.description || '',
        color_code: supplierType.color_code,
        is_active: supplierType.is_active
      });
    } else {
      setFormData({
        name: '',
        code: '',
        description: '',
        color_code: '#2196F3',
        is_active: true
      });
    }
    setFormDialog({ open: true, supplierType });
  };

  const handleCloseForm = () => {
    setFormDialog({ open: false, supplierType: null });
    setFormData({
      name: '',
      code: '',
      description: '',
      color_code: '#2196F3',
      is_active: true
    });
  };

  const handleSubmit = async () => {
    if (!formData.name.trim() || !formData.code.trim()) {
      enqueueSnackbar('Name and code are required', { variant: 'error' });
      return;
    }

    setSaving(true);
    try {
      const submitData = {
        name: formData.name.trim(),
        code: formData.code.trim().toUpperCase(),
        description: formData.description.trim(),
        color_code: formData.color_code,
        is_active: formData.is_active
      };

      if (formDialog.supplierType) {
        await api.put(`/supplier-types/${formDialog.supplierType.id}/`, submitData);
        enqueueSnackbar('Supplier type updated successfully', { variant: 'success' });
      } else {
        await api.post('/supplier-types/', submitData);
        enqueueSnackbar('Supplier type created successfully', { variant: 'success' });
      }
      
      handleCloseForm();
      loadSupplierTypes();
    } catch (error) {
      console.error('Error saving supplier type:', error);
      if (error.response?.data) {
        const errorData = error.response.data;
        if (errorData.code) {
          enqueueSnackbar('Supplier type code already exists', { variant: 'error' });
        } else if (errorData.name) {
          enqueueSnackbar('Supplier type name already exists', { variant: 'error' });
        } else {
          enqueueSnackbar('Failed to save supplier type', { variant: 'error' });
        }
      } else {
        enqueueSnackbar('Failed to save supplier type', { variant: 'error' });
      }
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    try {
      await api.delete(`/supplier-types/${deleteDialog.supplierType.id}/`);
      enqueueSnackbar('Supplier type deleted successfully', { variant: 'success' });
      setDeleteDialog({ open: false, supplierType: null });
      loadSupplierTypes();
    } catch (error) {
      console.error('Error deleting supplier type:', error);
      enqueueSnackbar('Failed to delete supplier type', { variant: 'error' });
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 3 }}>
            Loading supplier types...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Link
          component={RouterLink}
          to="/suppliers-menu"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Supplier Management
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <CategoryIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Supplier Types
        </Typography>
      </Breadcrumbs>

      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box display="flex" alignItems="center">
          <CategoryIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
          <Typography variant="h4" component="h1" fontWeight="bold">
            Supplier Types
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadSupplierTypes}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenForm()}
          >
            Add Supplier Type
          </Button>
        </Box>
      </Box>

      {/* Supplier Types Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Code</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Color</TableCell>
              <TableCell>Suppliers Count</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {supplierTypes.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    No supplier types found. Create your first supplier type to get started.
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              supplierTypes.map((supplierType) => (
                <TableRow key={supplierType.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {supplierType.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={supplierType.code} 
                      size="small" 
                      variant="outlined"
                      sx={{ 
                        fontFamily: 'monospace', 
                        fontWeight: 'bold',
                        borderColor: supplierType.color_code,
                        color: supplierType.color_code
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {supplierType.description || 'No description'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar
                        sx={{ 
                          width: 24, 
                          height: 24, 
                          bgcolor: supplierType.color_code 
                        }}
                      >
                        <ColorIcon sx={{ fontSize: 14 }} />
                      </Avatar>
                      <Typography variant="caption" sx={{ fontFamily: 'monospace' }}>
                        {supplierType.color_code}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {supplierType.suppliers_count || 0} suppliers
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={supplierType.is_active ? 'Active' : 'Inactive'}
                      color={supplierType.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(supplierType.created_at).toLocaleDateString()}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Edit Supplier Type">
                        <IconButton
                          size="small"
                          onClick={() => handleOpenForm(supplierType)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Supplier Type">
                        <IconButton
                          size="small"
                          onClick={() => setDeleteDialog({ open: true, supplierType })}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Form Dialog */}
      <Dialog open={formDialog.open} onClose={handleCloseForm} maxWidth="sm" fullWidth>
        <DialogTitle>
          {formDialog.supplierType ? 'Edit Supplier Type' : 'Create Supplier Type'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Code"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                  required
                  helperText="Uppercase alphanumeric with underscores"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  multiline
                  rows={3}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Color Code"
                  type="color"
                  value={formData.color_code}
                  onChange={(e) => setFormData({ ...formData, color_code: e.target.value })}
                  helperText="Color for displaying this type"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_active}
                      onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                    />
                  }
                  label="Active"
                  sx={{ mt: 2 }}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseForm} disabled={saving}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} variant="contained" disabled={saving}>
            {saving ? 'Saving...' : (formDialog.supplierType ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, supplierType: null })}>
        <DialogTitle>Delete Supplier Type</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the supplier type "{deleteDialog.supplierType?.name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, supplierType: null })}>
            Cancel
          </Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default SupplierTypesList;
