"""
Approval Status Views
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend

from ..models import ApprovalStatus
from ..serializers import (
    ApprovalStatusSerializer,
    ApprovalStatusListSerializer,
    ApprovalStatusDropdownSerializer,
    ApprovalStatusCreateSerializer
)


class ApprovalStatusViewSet(viewsets.ModelViewSet):
    """ViewSet for managing Approval Statuses"""
    queryset = ApprovalStatus.objects.all()
    serializer_class = ApprovalStatusSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'status_type', 'is_final', 'allows_modification', 'requires_comment']
    search_fields = ['code', 'name', 'description']
    ordering_fields = ['name', 'code', 'created_at', 'updated_at']
    ordering = ['name']

    def get_serializer_class(self):
        if self.action == 'list':
            return ApprovalStatusListSerializer
        elif self.action == 'dropdown':
            return ApprovalStatusDropdownSerializer
        elif self.action == 'create':
            return ApprovalStatusCreateSerializer
        return ApprovalStatusSerializer

    def get_queryset(self):
        queryset = ApprovalStatus.objects.all()
        if self.action == 'dropdown':
            queryset = queryset.filter(is_active=True)
        return queryset

    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        approval_status = self.get_object()
        ApprovalStatus.objects.filter(is_default=True).update(is_default=False)
        approval_status.is_default = True
        approval_status.save()
        return Response({
            'message': f'"{approval_status.name}" is now the default approval status',
            'default_status': ApprovalStatusSerializer(approval_status).data
        })

    @action(detail=False, methods=['post'])
    def create_defaults(self, request):
        ApprovalStatus.create_default_statuses()
        statuses = ApprovalStatus.objects.all()
        serializer = ApprovalStatusListSerializer(statuses, many=True)
        return Response({
            'message': 'Default approval statuses created successfully',
            'statuses': serializer.data
        })

    @action(detail=False, methods=['post'])
    def bulk_update_status(self, request):
        ids = request.data.get('ids', [])
        is_active = request.data.get('is_active')
        
        if not ids:
            return Response({'error': 'No IDs provided'}, status=status.HTTP_400_BAD_REQUEST)
        if is_active is None:
            return Response({'error': 'is_active field is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        updated_count = ApprovalStatus.objects.filter(id__in=ids).update(is_active=is_active)
        return Response({
            'message': f'Updated {updated_count} approval statuses',
            'updated_count': updated_count
        })

    def retrieve(self, request, *args, **kwargs):
        """Get detailed approval status information"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        """Update approval status with validation"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        """Partial update approval status"""
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()

        # Check if this is the default status
        if instance.is_default:
            return Response(
                {'error': 'Cannot delete the default status'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check usage count (when item model is implemented)
        if instance.usage_count > 0:
            return Response(
                {'error': f'Cannot delete status that is used by {instance.usage_count} items'},
                status=status.HTTP_400_BAD_REQUEST
            )

        instance.soft_delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
