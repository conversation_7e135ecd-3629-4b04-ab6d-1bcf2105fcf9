"""
Item Type Serializers
"""
from rest_framework import serializers
from ..models import ItemType


class ItemTypeSerializer(serializers.ModelSerializer):
    """Serializer for ItemType model"""
    
    usage_count = serializers.ReadOnlyField()
    
    class Meta:
        model = ItemType
        fields = [
            'id',
            'name',
            'description',
            'is_active',
            'usage_count',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_name(self, value):
        """Validate name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
        return value


class ItemTypeListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing item types"""
    
    usage_count = serializers.ReadOnlyField()
    
    class Meta:
        model = ItemType
        fields = [
            'id',
            'name',
            'is_active',
            'usage_count'
        ]


class ItemTypeDropdownSerializer(serializers.ModelSerializer):
    """Minimal serializer for dropdown/select options"""
    
    label = serializers.CharField(source='name')
    value = serializers.CharField(source='id')
    
    class Meta:
        model = ItemType
        fields = ['value', 'label']
