import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Alert,
  CircularProgress,
  InputAdornment,
  Chip,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Paper,
  Fade,
  LinearProgress,
  Avatar,
  Container,
  Divider,
  Collapse
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Inventory as InventoryIcon,
  LocalShipping as ShippingIcon,
  AttachMoney as MoneyIcon,
  NavigateNext as NextIcon,
  NavigateBefore as BackIcon,
  CheckCircle as CheckIcon,
  Assignment as AssignmentIcon,
  Schedule as ScheduleIcon,
  People as PeopleIcon,
  Notes as NotesIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import api from '../../utils/axios';
import {
  API_ENDPOINTS,
  ROUTES,
  MESSAGES,
  FIELD_LABELS,
  PLACEHOLDERS,
  VALIDATION_RULES,
  UI_CONSTANTS,
  COLOR_THEMES,
  BUTTON_TEXT,
  STEP_CONFIGS,
  READ_ONLY_FIELDS
} from '../../config/formConfig';

// Helper function to get step icons for BatchItem
function getBatchStepIcon(stepKey) {
  const iconMap = {
    basic_info: <InventoryIcon />,
    financial_quantities: <MoneyIcon />,
    dates_tracking: <ScheduleIcon />,
    personnel_status: <PeopleIcon />,
    notes_documentation: <NotesIcon />
  };
  return iconMap[stepKey] || <InventoryIcon />;
}

// Step Components - Defined outside to prevent recreation on each render
const BasicInformationStep = ({ formData, dropdowns, handleInputChange }) => (
  <Grid container spacing={3}>
    <Grid item xs={12} md={6}>
      <FormControl fullWidth required>
        <InputLabel>Item Master</InputLabel>
        <Select
          value={formData.item_master}
          onChange={handleInputChange('item_master')}
          label="Item Master"
        >
          {Array.isArray(dropdowns.itemMasters) && dropdowns.itemMasters.map((item) => (
            <MenuItem key={item.id} value={item.id}>
              {item.name} ({item.sku})
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Grid>
    <Grid item xs={12} md={6}>
      <FormControl fullWidth>
        <InputLabel>Supplier</InputLabel>
        <Select
          value={formData.supplier}
          onChange={handleInputChange('supplier')}
          label="Supplier"
        >
          <MenuItem value="">
            <em>None</em>
          </MenuItem>
          {Array.isArray(dropdowns.suppliers) && dropdowns.suppliers.map((supplier) => (
            <MenuItem key={supplier.id} value={supplier.id}>
              {supplier.display_name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Grid>
    <Grid item xs={12} md={6}>
      <TextField
        fullWidth
        label="Purchase Order Number"
        value={formData.purchase_order_number}
        onChange={handleInputChange('purchase_order_number')}
        placeholder="e.g., PO-2024-001"
      />
    </Grid>
    <Grid item xs={12} md={6}>
      <TextField
        fullWidth
        label="Invoice Number"
        value={formData.invoice_number}
        onChange={handleInputChange('invoice_number')}
        placeholder="e.g., INV-2024-001"
      />
    </Grid>
  </Grid>
);

const FinancialQuantitiesStep = ({ formData, handleInputChange }) => (
  <Grid container spacing={3}>
    <Grid item xs={12} md={6}>
      <TextField
        fullWidth
        label="Purchase Price per Unit"
        type="number"
        value={formData.purchase_price}
        onChange={handleInputChange('purchase_price')}
        InputProps={{
          startAdornment: <InputAdornment position="start">$</InputAdornment>,
        }}
        inputProps={{ min: 0, step: 0.01 }}
        placeholder="0.00"
      />
    </Grid>
    <Grid item xs={12} md={6}>
      <TextField
        fullWidth
        label="Quantity Ordered"
        type="number"
        value={formData.quantity_ordered}
        onChange={handleInputChange('quantity_ordered')}
        inputProps={{ min: 0 }}
        placeholder="Optional"
      />
    </Grid>
    <Grid item xs={12} md={4}>
      <TextField
        fullWidth
        label="Quantity Received"
        type="number"
        value={formData.quantity_received}
        onChange={handleInputChange('quantity_received')}
        inputProps={{ min: 0 }}
        required
      />
    </Grid>
    <Grid item xs={12} md={4}>
      <TextField
        fullWidth
        label="Quantity Accepted"
        type="number"
        value={formData.quantity_accepted}
        onChange={handleInputChange('quantity_accepted')}
        inputProps={{ min: 0 }}
      />
    </Grid>
    <Grid item xs={12} md={4}>
      <TextField
        fullWidth
        label="Quantity Rejected"
        type="number"
        value={formData.quantity_rejected}
        onChange={handleInputChange('quantity_rejected')}
        inputProps={{ min: 0 }}
      />
    </Grid>
  </Grid>
);

const DatesTrackingStep = ({ formData, handleInputChange }) => (
  <Grid container spacing={3}>
    <Grid item xs={12} md={6}>
      <TextField
        fullWidth
        label="Order Date"
        type="date"
        value={formData.order_date}
        onChange={handleInputChange('order_date')}
        InputLabelProps={{ shrink: true }}
      />
    </Grid>
    <Grid item xs={12} md={6}>
      <TextField
        fullWidth
        label="Expected Delivery Date"
        type="date"
        value={formData.expected_delivery_date}
        onChange={handleInputChange('expected_delivery_date')}
        InputLabelProps={{ shrink: true }}
      />
    </Grid>
    <Grid item xs={12} md={6}>
      <TextField
        fullWidth
        label="Received Date"
        type="date"
        value={formData.received_date}
        onChange={handleInputChange('received_date')}
        InputLabelProps={{ shrink: true }}
      />
    </Grid>
    <Grid item xs={12} md={6}>
      <TextField
        fullWidth
        label="Inspection Date"
        type="date"
        value={formData.inspection_date}
        onChange={handleInputChange('inspection_date')}
        InputLabelProps={{ shrink: true }}
      />
    </Grid>
    <Grid item xs={12} md={6}>
      <TextField
        fullWidth
        label="Expiration Date"
        type="date"
        value={formData.expiration_date}
        onChange={handleInputChange('expiration_date')}
        InputLabelProps={{ shrink: true }}
      />
    </Grid>
    <Grid item xs={12} md={6}>
      <TextField
        fullWidth
        label="Warranty (Months)"
        type="number"
        value={formData.warranty_months}
        onChange={handleInputChange('warranty_months')}
        inputProps={{ min: 0 }}
        placeholder="Optional"
      />
    </Grid>
    <Grid item xs={12}>
      <TextField
        fullWidth
        label="Lot Number"
        value={formData.lot_number}
        onChange={handleInputChange('lot_number')}
        placeholder="Manufacturer lot or batch number"
      />
    </Grid>
  </Grid>
);

const PersonnelStatusStep = ({ formData, dropdowns, handleInputChange }) => (
  <Grid container spacing={3}>
    <Grid item xs={12} md={4}>
      <FormControl fullWidth>
        <InputLabel>Received By</InputLabel>
        <Select
          value={formData.received_by}
          onChange={handleInputChange('received_by')}
          label="Received By"
        >
          <MenuItem value="">
            <em>None</em>
          </MenuItem>
          {Array.isArray(dropdowns.users) && dropdowns.users.map((user) => (
            <MenuItem key={user.id} value={user.id}>
              {user.first_name && user.last_name
                ? `${user.first_name} ${user.last_name}`
                : user.username}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Grid>
    <Grid item xs={12} md={4}>
      <FormControl fullWidth>
        <InputLabel>Inspected By</InputLabel>
        <Select
          value={formData.inspected_by}
          onChange={handleInputChange('inspected_by')}
          label="Inspected By"
        >
          <MenuItem value="">
            <em>None</em>
          </MenuItem>
          {Array.isArray(dropdowns.users) && dropdowns.users.map((user) => (
            <MenuItem key={user.id} value={user.id}>
              {user.first_name && user.last_name
                ? `${user.first_name} ${user.last_name}`
                : user.username}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Grid>
    <Grid item xs={12} md={4}>
      <FormControl fullWidth>
        <InputLabel>Approval Status</InputLabel>
        <Select
          value={formData.approval_status}
          onChange={handleInputChange('approval_status')}
          label="Approval Status"
        >
          <MenuItem value="">
            <em>None</em>
          </MenuItem>
          {Array.isArray(dropdowns.approvalStatuses) && dropdowns.approvalStatuses.map((status) => (
            <MenuItem key={status.value} value={status.value}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Chip
                  size="small"
                  label={status.label}
                  sx={{
                    backgroundColor: status.color_code + '20',
                    color: status.color_code,
                    border: `1px solid ${status.color_code}`,
                    mr: 1
                  }}
                />
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Grid>
  </Grid>
);

const NotesCommentsStep = ({ formData, handleInputChange }) => (
  <Grid container spacing={3}>
    <Grid item xs={12} md={4}>
      <TextField
        fullWidth
        label="Delivery Notes"
        value={formData.delivery_notes}
        onChange={handleInputChange('delivery_notes')}
        multiline
        rows={4}
        placeholder="Notes about delivery..."
      />
    </Grid>
    <Grid item xs={12} md={4}>
      <TextField
        fullWidth
        label="Inspection Notes"
        value={formData.inspection_notes}
        onChange={handleInputChange('inspection_notes')}
        multiline
        rows={4}
        placeholder="Notes about inspection..."
      />
    </Grid>
    <Grid item xs={12} md={4}>
      <TextField
        fullWidth
        label="Rejection Reason"
        value={formData.rejection_reason}
        onChange={handleInputChange('rejection_reason')}
        multiline
        rows={4}
        placeholder="Reason for rejection (if any)..."
      />
    </Grid>
  </Grid>
);

const BatchItemForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { enqueueSnackbar } = useSnackbar();
  const isEdit = Boolean(id);

  // Form state - essential fields for BatchItem
  const [formData, setFormData] = useState({
    item_master: '',
    supplier: '',
    purchase_order_number: '',
    invoice_number: '',
    purchase_price: '',
    quantity_ordered: '',
    quantity_received: 0,
    quantity_accepted: 0,
    quantity_rejected: 0,
    order_date: '',
    expected_delivery_date: '',
    received_date: '',
    inspection_date: '',
    expiration_date: '',
    warranty_months: '',
    lot_number: '',
    received_by: '',
    inspected_by: '',
    approval_status: '',
    delivery_notes: '',
    inspection_notes: '',
    rejection_reason: '',
    is_active: true
  });

  // Dropdown options
  const [dropdowns, setDropdowns] = useState({
    itemMasters: [],
    suppliers: [],
    approvalStatuses: [],
    users: []
  });

  // Loading states
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [dropdownsLoaded, setDropdownsLoaded] = useState(false);

  // Stepper state
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState(new Set());

  // Define steps for vertical stepper using configuration
  const steps = STEP_CONFIGS.BATCH_ITEM.map(stepConfig => ({
    label: stepConfig.label,
    icon: getBatchStepIcon(stepConfig.key),
    color: stepConfig.color,
    fields: stepConfig.fields
  }));

  useEffect(() => {
    const initializeForm = async () => {
      try {
        console.log('🔄 Initializing form...');
        await loadDropdowns();
      } catch (error) {
        console.error('❌ Error loading dropdowns:', error);
      }
    };
    initializeForm();
  }, [id, isEdit]);

  // Load batch item after dropdowns are loaded
  useEffect(() => {
    if (dropdownsLoaded && isEdit) {
      console.log('✅ Dropdowns loaded, now loading batch item');
      loadBatchItem();
    }
  }, [dropdownsLoaded, isEdit, id]);

  const loadDropdowns = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Loading BatchItem dropdown data...');

      // Load required dropdowns in parallel
      const [itemMastersRes, suppliersRes, approvalStatusesRes, usersRes] = await Promise.all([
        api.get(API_ENDPOINTS.ITEM_MASTERS),
        api.get(API_ENDPOINTS.SUPPLIERS),
        api.get(API_ENDPOINTS.APPROVAL_STATUSES),
        api.get(API_ENDPOINTS.USERS)
      ]);

      // Debug the response data
      console.log('🔍 Raw API responses:', {
        itemMasters: itemMastersRes.data,
        suppliers: suppliersRes.data,
        approvalStatuses: approvalStatusesRes.data,
        users: usersRes.data
      });

      const itemMasters = itemMastersRes.data?.results || [];
      const suppliers = suppliersRes.data || [];
      const approvalStatuses = approvalStatusesRes.data || [];
      const users = usersRes.data?.results || [];

      console.log('🔍 Processed dropdown data:', {
        itemMasters: itemMasters.slice(0, 2),
        suppliers: suppliers.slice(0, 2),
        approvalStatuses: approvalStatuses.slice(0, 2),
        users: users.slice(0, 2)
      });

      setDropdowns({
        itemMasters,
        suppliers,
        approvalStatuses,
        users
      });

      setDropdownsLoaded(true);
      console.log('✅', MESSAGES.SUCCESS.DROPDOWN_LOADED);
    } catch (err) {
      console.error('❌ Error loading dropdowns:', err);
      setError(MESSAGES.ERROR.LOAD_DROPDOWN_FAILED);
      enqueueSnackbar(MESSAGES.ERROR.LOAD_DROPDOWN_FAILED, { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const loadBatchItem = async () => {
    try {
      setLoading(true);

      if (!dropdownsLoaded) {
        console.warn('⚠️ Dropdowns not loaded yet, waiting...');
        return;
      }

      const response = await api.get(`/batch-items/${id}/`);
      const batch = response.data;

      console.log('🔍 Raw batch data from API:', batch);

      // Validate dropdown values exist before setting them
      const validateDropdownValue = (value, options, fieldName) => {
        if (!value) return '';
        if (!Array.isArray(options) || options.length === 0) {
          console.warn(`⚠️ ${fieldName} dropdown options not loaded yet`);
          return value; // Return original value if options not loaded
        }
        const exists = options.some(option => option.id === value || option.value === value);
        if (!exists) {
          console.warn(`⚠️ ${fieldName} value "${value}" not found in dropdown options:`, options.map(o => o.id || o.value));
          return '';
        }
        return value;
      };

      const formDataToSet = {
        item_master: validateDropdownValue(batch.item_master, dropdowns.itemMasters, 'item_master'),
        supplier: validateDropdownValue(batch.supplier, dropdowns.suppliers, 'supplier'),
        purchase_order_number: batch.purchase_order_number || '',
        invoice_number: batch.invoice_number || '',
        purchase_price: batch.purchase_price || '',
        quantity_ordered: batch.quantity_ordered || '',
        quantity_received: batch.quantity_received || 0,
        quantity_accepted: batch.quantity_accepted || 0,
        quantity_rejected: batch.quantity_rejected || 0,
        order_date: batch.order_date || '',
        expected_delivery_date: batch.expected_delivery_date || '',
        received_date: batch.received_date || '',
        inspection_date: batch.inspection_date || '',
        expiration_date: batch.expiration_date || '',
        warranty_months: batch.warranty_months || '',
        lot_number: batch.lot_number || '',
        received_by: validateDropdownValue(batch.received_by, dropdowns.users, 'received_by'),
        inspected_by: validateDropdownValue(batch.inspected_by, dropdowns.users, 'inspected_by'),
        approval_status: validateDropdownValue(batch.approval_status, dropdowns.approvalStatuses, 'approval_status'),
        delivery_notes: batch.delivery_notes || '',
        inspection_notes: batch.inspection_notes || '',
        rejection_reason: batch.rejection_reason || '',
        is_active: batch.is_active !== false
      };

      console.log('🔍 Validated form data being set:', formDataToSet);
      setFormData(formDataToSet);

      console.log('✅ BatchItem loaded successfully');
    } catch (err) {
      console.error('❌ Error loading batch item:', err);
      setError('Failed to load batch item. Please try again.');
      enqueueSnackbar('Failed to load batch item', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = useCallback((field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  const validateForm = () => {
    const errors = [];

    if (!formData.item_master || formData.item_master === '') {
      errors.push(MESSAGES.ERROR.ITEM_MASTER_REQUIRED);
    }

    const quantityReceived = parseInt(formData.quantity_received) || 0;
    const quantityAccepted = parseInt(formData.quantity_accepted) || 0;
    const quantityRejected = parseInt(formData.quantity_rejected) || 0;

    if (quantityReceived < VALIDATION_RULES.MIN_VALUE) errors.push(MESSAGES.ERROR.QUANTITY_NEGATIVE);
    if (quantityAccepted < VALIDATION_RULES.MIN_VALUE) errors.push(MESSAGES.ERROR.QUANTITY_NEGATIVE);
    if (quantityRejected < VALIDATION_RULES.MIN_VALUE) errors.push(MESSAGES.ERROR.QUANTITY_NEGATIVE);

    const totalInspected = quantityAccepted + quantityRejected;
    if (totalInspected > quantityReceived) {
      errors.push(MESSAGES.ERROR.QUANTITY_INSPECTED_EXCEEDS);
    }

    if (formData.purchase_price && parseFloat(formData.purchase_price) < VALIDATION_RULES.MIN_VALUE) {
      errors.push(MESSAGES.ERROR.PURCHASE_PRICE_NEGATIVE);
    }

    return errors;
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      validationErrors.forEach(error => enqueueSnackbar(error, { variant: 'error' }));
      return;
    }

    setSaving(true);
    try {
      console.log('🔄 Submitting BatchItem form data:', formData);

      // Prepare data for submission - only include writable fields
      const submitData = {
        // Required fields
        item_master: formData.item_master, // UUID string
        quantity_received: parseInt(formData.quantity_received) || 0,
        quantity_accepted: parseInt(formData.quantity_accepted) || 0,
        quantity_rejected: parseInt(formData.quantity_rejected) || 0,
        is_active: Boolean(formData.is_active)
      };

      // Validate required fields before submission
      if (!submitData.item_master) {
        enqueueSnackbar('Item master is required', { variant: 'error' });
        return;
      }

      console.log('🔍 Base submit data (required fields):', submitData);

      // NOTE: Exclude read-only computed fields that cause 500 errors:
      // - quantity_available, quantity_pending_inspection, total_cost
      // - acceptance_rate, rejection_rate, is_fully_inspected
      // - is_expired, days_until_expiration, warranty_end_date, is_under_warranty
      // - serial_series, batch_number (auto-generated)

      // Add optional fields only if they have values
      if (formData.supplier && formData.supplier !== '') {
        submitData.supplier = formData.supplier; // UUID string
      }
      if (formData.purchase_order_number && formData.purchase_order_number.trim()) {
        submitData.purchase_order_number = formData.purchase_order_number.trim();
      }
      if (formData.invoice_number && formData.invoice_number.trim()) {
        submitData.invoice_number = formData.invoice_number.trim();
      }
      if (formData.purchase_price && formData.purchase_price !== '') {
        const price = parseFloat(formData.purchase_price);
        if (!isNaN(price) && price > 0) {
          submitData.purchase_price = price.toFixed(4); // Decimal with 4 places
        }
      }
      if (formData.quantity_ordered && formData.quantity_ordered !== '') {
        const qty = parseInt(formData.quantity_ordered);
        if (!isNaN(qty) && qty > 0) {
          submitData.quantity_ordered = qty;
        }
      }
      if (formData.warranty_months && formData.warranty_months !== '') {
        const months = parseInt(formData.warranty_months);
        if (!isNaN(months) && months > 0) {
          submitData.warranty_months = months;
        }
      }
      if (formData.lot_number && formData.lot_number.trim()) {
        submitData.lot_number = formData.lot_number.trim();
      }
      if (formData.received_by && formData.received_by !== '') {
        submitData.received_by = parseInt(formData.received_by); // User ID is integer
      }
      if (formData.inspected_by && formData.inspected_by !== '') {
        submitData.inspected_by = parseInt(formData.inspected_by); // User ID is integer
      }
      if (formData.approval_status && formData.approval_status !== '') {
        submitData.approval_status = formData.approval_status; // UUID string
      }
      if (formData.delivery_notes && formData.delivery_notes.trim()) {
        submitData.delivery_notes = formData.delivery_notes.trim();
      }
      if (formData.inspection_notes && formData.inspection_notes.trim()) {
        submitData.inspection_notes = formData.inspection_notes.trim();
      }
      if (formData.rejection_reason && formData.rejection_reason.trim()) {
        submitData.rejection_reason = formData.rejection_reason.trim();
      }

      // Add date fields only if they have values (YYYY-MM-DD format)
      if (formData.order_date && formData.order_date !== '') {
        submitData.order_date = formData.order_date;
      }
      if (formData.expected_delivery_date && formData.expected_delivery_date !== '') {
        submitData.expected_delivery_date = formData.expected_delivery_date;
      }
      if (formData.received_date && formData.received_date !== '') {
        submitData.received_date = formData.received_date;
      }
      if (formData.inspection_date && formData.inspection_date !== '') {
        submitData.inspection_date = formData.inspection_date;
      }
      if (formData.expiration_date && formData.expiration_date !== '') {
        submitData.expiration_date = formData.expiration_date;
      }

      // Final validation: ensure no read-only fields are included
      READ_ONLY_FIELDS.BATCH_ITEM.forEach(field => {
        if (submitData.hasOwnProperty(field)) {
          console.warn(`⚠️ Removing read-only field: ${field}`);
          delete submitData[field];
        }
      });

      console.log('🔍 Final submit data being sent to API:', submitData);

      let response;
      if (isEdit) {
        response = await api.put(API_ENDPOINTS.BATCH_ITEM_DETAIL(id), submitData);
        enqueueSnackbar(MESSAGES.SUCCESS.BATCH_ITEM_UPDATED, { variant: 'success' });
      } else {
        response = await api.post(API_ENDPOINTS.BATCH_ITEMS, submitData);
        enqueueSnackbar(MESSAGES.SUCCESS.BATCH_ITEM_CREATED, { variant: 'success' });
      }

      console.log('✅ BatchItem saved successfully:', response.data);
      navigate(ROUTES.BATCH_ITEMS_LIST);
    } catch (err) {
      console.error('❌ Error saving batch item:', err);
      console.error('❌ Error response data:', err.response?.data);
      console.error('❌ Error status:', err.response?.status);

      let errorMessage = MESSAGES.ERROR.SAVE_BATCH_FAILED;
      if (err.response?.data) {
        if (typeof err.response.data === 'string') {
          errorMessage = err.response.data;
        } else if (err.response.data.detail) {
          errorMessage = err.response.data.detail;
        } else if (err.response.data.message) {
          errorMessage = err.response.data.message;
        } else {
          // Handle field-specific errors
          const errors = [];
          Object.keys(err.response.data).forEach(field => {
            const fieldErrors = err.response.data[field];
            if (Array.isArray(fieldErrors)) {
              errors.push(`${field}: ${fieldErrors.join(', ')}`);
            } else {
              errors.push(`${field}: ${fieldErrors}`);
            }
          });
          if (errors.length > 0) {
            errorMessage = errors.join('; ');
          }
        }
      }

      enqueueSnackbar(errorMessage, { variant: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate(ROUTES.BATCH_ITEMS_LIST);
  };

  // Step navigation functions
  const handleNext = () => {
    if (validateCurrentStep()) {
      setCompletedSteps(prev => new Set([...prev, activeStep]));
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleStepClick = (stepIndex) => {
    // Allow clicking on completed steps or the next step
    if (completedSteps.has(stepIndex) || stepIndex <= activeStep) {
      setActiveStep(stepIndex);
    }
  };

  const validateCurrentStep = () => {
    const errors = [];

    // Step 0: Basic Information - only item_master is required
    if (activeStep === 0) {
      if (!formData.item_master) {
        errors.push('Item master is required');
      }
    }

    // Step 1: Financial & Quantities - validate quantities and price
    if (activeStep === 1) {
      if (formData.quantity_received < 0) errors.push('Quantity received cannot be negative');
      if (formData.quantity_accepted < 0) errors.push('Quantity accepted cannot be negative');
      if (formData.quantity_rejected < 0) errors.push('Quantity rejected cannot be negative');

      const totalInspected = parseInt(formData.quantity_accepted || 0) + parseInt(formData.quantity_rejected || 0);
      const received = parseInt(formData.quantity_received || 0);
      if (totalInspected > received) {
        errors.push('Total inspected quantity cannot exceed received quantity');
      }

      if (formData.purchase_price && parseFloat(formData.purchase_price) < 0) {
        errors.push('Purchase price cannot be negative');
      }
    }

    // Show errors if any
    if (errors.length > 0) {
      errors.forEach(error => enqueueSnackbar(error, { variant: 'error' }));
      return false;
    }

    return true;
  };

  const isStepCompleted = (stepIndex) => {
    return completedSteps.has(stepIndex);
  };

  const isStepOptional = (stepIndex) => {
    // All steps except the first one are optional
    return stepIndex > 0;
  };

  // Step content components
  const renderStepContent = (stepIndex) => {
    switch (stepIndex) {
      case 0:
        return (
          <BasicInformationStep
            formData={formData}
            dropdowns={dropdowns}
            handleInputChange={handleInputChange}
          />
        );
      case 1:
        return (
          <FinancialQuantitiesStep
            formData={formData}
            handleInputChange={handleInputChange}
          />
        );
      case 2:
        return (
          <DatesTrackingStep
            formData={formData}
            handleInputChange={handleInputChange}
          />
        );
      case 3:
        return (
          <PersonnelStatusStep
            formData={formData}
            dropdowns={dropdowns}
            handleInputChange={handleInputChange}
          />
        );
      case 4:
        return (
          <NotesCommentsStep
            formData={formData}
            handleInputChange={handleInputChange}
          />
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Loading...
        </Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Professional Header */}
      <Fade in={true}>
        <Paper
          elevation={0}
          sx={{
            p: 4,
            mb: 4,
            borderRadius: 3,
            background: 'linear-gradient(135deg, #2e7d32 0%, #4caf50 100%)',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          <Box sx={{ position: 'relative', zIndex: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Box display="flex" alignItems="center">
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.2)',
                    color: 'white',
                    width: 64,
                    height: 64,
                    mr: 3,
                    backdropFilter: 'blur(10px)'
                  }}
                >
                  <ShippingIcon sx={{ fontSize: 32 }} />
                </Avatar>
                <Box>
                  <Typography variant="h3" component="h1" fontWeight="bold" gutterBottom>
                    {isEdit ? 'Edit Batch Item' : 'Create Batch Item'}
                  </Typography>
                  <Typography variant="h6" sx={{ opacity: 0.9 }}>
                    {isEdit ? 'Update batch item information' : 'Define a new inventory batch with comprehensive tracking'}
                  </Typography>
                </Box>
              </Box>
              <Box textAlign="center">
                <Typography variant="h4" fontWeight="bold">
                  {activeStep + 1}/{steps.length}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Steps Complete
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={((completedSteps.size) / steps.length) * 100}
                  sx={{
                    mt: 1,
                    bgcolor: 'rgba(255,255,255,0.3)',
                    '& .MuiLinearProgress-bar': {
                      bgcolor: 'white'
                    }
                  }}
                />
              </Box>
            </Box>
          </Box>
          {/* Decorative background elements */}
          <Box
            sx={{
              position: 'absolute',
              top: -50,
              right: -50,
              width: 200,
              height: 200,
              borderRadius: '50%',
              bgcolor: 'rgba(255,255,255,0.1)',
              zIndex: 1
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              bottom: -30,
              left: -30,
              width: 150,
              height: 150,
              borderRadius: '50%',
              bgcolor: 'rgba(255,255,255,0.05)',
              zIndex: 1
            }}
          />
        </Paper>
      </Fade>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Grid container spacing={4}>
          {/* Vertical Stepper */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, borderRadius: 2, position: 'sticky', top: 20 }}>
              <Typography variant="h6" gutterBottom sx={{ mb: 3, fontWeight: 'bold' }}>
                Progress Overview
              </Typography>
              <Stepper activeStep={activeStep} orientation="vertical">
                {steps.map((step, index) => (
                  <Step
                    key={step.label}
                    completed={isStepCompleted(index)}
                    sx={{ cursor: 'pointer' }}
                    onClick={() => handleStepClick(index)}
                  >
                    <StepLabel
                      optional={isStepOptional(index) && (
                        <Typography variant="caption" color="text.secondary">
                          Optional
                        </Typography>
                      )}
                      StepIconComponent={({ active, completed }) => (
                        <Avatar
                          sx={{
                            width: 40,
                            height: 40,
                            bgcolor: completed ? 'success.main' : active ? step.color : 'grey.300',
                            color: 'white',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              transform: 'scale(1.1)',
                              boxShadow: 3
                            }
                          }}
                        >
                          {completed ? <CheckIcon /> : step.icon}
                        </Avatar>
                      )}
                    >
                      <Typography variant="subtitle1" fontWeight="bold">
                        {step.label}
                      </Typography>
                    </StepLabel>
                    <StepContent>
                      <Typography variant="caption" color="text.secondary">
                        Fields: {step.fields.join(', ')}
                      </Typography>
                    </StepContent>
                  </Step>
                ))}
              </Stepper>
            </Paper>
          </Grid>

          {/* Step Content */}
          <Grid item xs={12} md={8}>
            <Fade in={true} key={activeStep}>
              <Paper sx={{ p: 4, borderRadius: 2, minHeight: 500 }}>
                <Box sx={{ mb: 4 }}>
                  <Box display="flex" alignItems="center" sx={{ mb: 2 }}>
                    <Avatar
                      sx={{
                        bgcolor: steps[activeStep].color,
                        mr: 2,
                        width: 48,
                        height: 48
                      }}
                    >
                      {steps[activeStep].icon}
                    </Avatar>
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {steps[activeStep].label}
                      </Typography>
                    </Box>
                  </Box>
                  <Divider />
                </Box>

                {renderStepContent(activeStep)}

                {/* Navigation Buttons */}
                <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Button
                    variant="outlined"
                    onClick={handleCancel}
                    startIcon={<CancelIcon />}
                    disabled={saving}
                    size="large"
                  >
                    Cancel
                  </Button>

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button
                      variant="outlined"
                      onClick={handleBack}
                      startIcon={<BackIcon />}
                      disabled={activeStep === 0 || saving}
                      size="large"
                    >
                      Back
                    </Button>

                    {activeStep === steps.length - 1 ? (
                      <Button
                        type="submit"
                        variant="contained"
                        startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                        disabled={saving}
                        size="large"
                        sx={{
                          minWidth: 160,
                          background: 'linear-gradient(45deg, #2e7d32 30%, #4caf50 90%)',
                          '&:hover': {
                            background: 'linear-gradient(45deg, #1b5e20 30%, #388e3c 90%)',
                          }
                        }}
                      >
                        {saving ? 'Saving...' : (isEdit ? 'Update Batch' : 'Create Batch')}
                      </Button>
                    ) : (
                      <Button
                        variant="contained"
                        onClick={handleNext}
                        endIcon={<NextIcon />}
                        disabled={saving}
                        size="large"
                        sx={{
                          minWidth: 120,
                          bgcolor: steps[activeStep].color,
                          '&:hover': {
                            bgcolor: steps[activeStep].color,
                            filter: 'brightness(0.9)'
                          }
                        }}
                      >
                        Next
                      </Button>
                    )}
                  </Box>
                </Box>
              </Paper>
            </Fade>
          </Grid>
        </Grid>
      </form>
    </Container>
  );
};

export default BatchItemForm;
