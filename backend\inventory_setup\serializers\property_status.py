"""
Property Status Serializers
"""
from rest_framework import serializers
from ..models import PropertyStatus


class PropertyStatusSerializer(serializers.ModelSerializer):
    """Serializer for PropertyStatus model"""
    
    usage_count = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = PropertyStatus
        fields = [
            'id',
            'code',
            'name',
            'description',
            'status_type',
            'color_code',
            'is_active',
            'is_default',
            'affects_value',
            'requires_inspection',
            'display_name',
            'usage_count',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_name(self, value):
        """Validate name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
        return value


class PropertyStatusListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing property statuses"""
    
    usage_count = serializers.ReadOnly<PERSON>ield()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = PropertyStatus
        fields = [
            'id',
            'code',
            'name',
            'status_type',
            'color_code',
            'is_active',
            'is_default',
            'affects_value',
            'requires_inspection',
            'display_name',
            'usage_count'
        ]


class PropertyStatusDropdownSerializer(serializers.ModelSerializer):
    """Minimal serializer for dropdown/select options"""
    
    label = serializers.SerializerMethodField()
    value = serializers.CharField(source='id')
    
    class Meta:
        model = PropertyStatus
        fields = ['value', 'label', 'color_code']
    
    def get_label(self, obj):
        return obj.display_name


class PropertyStatusCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating property statuses"""
    
    class Meta:
        model = PropertyStatus
        fields = [
            'code',
            'name',
            'description',
            'status_type',
            'color_code',
            'is_active',
            'is_default',
            'affects_value',
            'requires_inspection'
        ]
