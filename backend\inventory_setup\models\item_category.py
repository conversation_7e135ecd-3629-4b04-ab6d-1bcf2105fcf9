"""
Item Category Model for Inventory Setup
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from .base import TimeStampedModel


class ItemCategory(TimeStampedModel):
    """
    Item Category model for categorizing inventory items by their category.
    Examples: Office Supplies, IT Equipment, Furniture, etc.
    """
    
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('Category Name'),
        help_text=_('Name of the item category')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description'),
        help_text=_('Detailed description of the item category')
    )

    class Meta:
        db_table = 'inventory_item_category'
        verbose_name = _('Item Category')
        verbose_name_plural = _('Item Categories')
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.name

    def clean(self):
        """Custom validation"""
        super().clean()
        if self.name:
            self.name = self.name.strip()

    def save(self, *args, **kwargs):
        """Override save to clean data"""
        if self.name:
            self.name = self.name.strip()
        super().save(*args, **kwargs)

    @property
    def usage_count(self):
        """Get count of how many items use this category"""
        # This would be implemented when item models are created
        # For now, return 0
        return 0
