"""
Professional Inventory Management API Views

Enhanced with:
- Advanced filtering and search capabilities
- Comprehensive analytics endpoints
- Professional error handling
- Performance optimizations
- Audit trail integration
"""
from rest_framework import viewsets, status, filters, pagination
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, DjangoModelPermissions
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Sum, Avg, F, Case, When, IntegerField
from django.db import models, transaction
from django.utils import timezone
from django.core.cache import cache
from datetime import timedelta, datetime
import logging

from .models import (
    SerialVoucherCategory, SerialVoucher, ItemMaster,
    BatchItem, InventoryItem
)
from .serializers import (
    SerialVoucherCategorySerializer, SerialVoucherSerializer,
    ItemMasterListSerializer, ItemMasterDetailSerializer,
    BatchItemListSerializer, BatchItemDetailSerializer,
    InventoryItemListSerializer, InventoryItemDetailSerializer
)

logger = logging.getLogger(__name__)


class StandardResultsSetPagination(pagination.PageNumberPagination):
    """Professional pagination with configurable page sizes"""
    page_size = 25
    page_size_query_param = 'page_size'
    max_page_size = 100
    
    def get_paginated_response(self, data):
        return Response({
            'pagination': {
                'count': self.page.paginator.count,
                'total_pages': self.page.paginator.num_pages,
                'current_page': self.page.number,
                'page_size': self.get_page_size(self.request),
                'has_next': self.page.has_next(),
                'has_previous': self.page.has_previous(),
            },
            'results': data
        })


class SerialVoucherCategoryViewSet(viewsets.ModelViewSet):
    """ViewSet for Serial Voucher Categories"""
    queryset = SerialVoucherCategory.objects.all()
    serializer_class = SerialVoucherCategorySerializer
    permission_classes = []  # Temporarily allow unauthenticated access for testing
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['title', 'report_title', 'description']
    ordering_fields = ['title', 'created_at']
    ordering = ['title']


class SerialVoucherViewSet(viewsets.ModelViewSet):
    """ViewSet for Serial Vouchers"""
    queryset = SerialVoucher.objects.all()
    serializer_class = SerialVoucherSerializer
    permission_classes = []  # Temporarily allow unauthenticated access for testing
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'is_active']  # Removed 'is_exhausted' as it's a property, not a model field
    search_fields = ['prefix', 'category__title']
    ordering_fields = ['prefix', 'current_number', 'created_at']
    ordering = ['category', 'prefix']

    @action(detail=True, methods=['post'])
    def reset_counter(self, request, pk=None):
        """Reset voucher counter"""
        voucher = self.get_object()
        start_number = request.data.get('start_number', 1)
        try:
            voucher.reset_counter(start_number)
            return Response({'message': f'Counter reset to {start_number}'})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def preview_next(self, request, pk=None):
        """Preview next serial number"""
        voucher = self.get_object()
        return Response({'next_serial': voucher.preview_next_serial()})


class ItemMasterViewSet(viewsets.ModelViewSet):
    """
    Professional Item Masters API ViewSet
    
    Enhanced with:
    - Advanced filtering and search
    - Analytics endpoints
    - Bulk operations
    - Professional error handling
    """
    queryset = ItemMaster.objects.all()
    permission_classes = [IsAuthenticated, DjangoModelPermissions]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'sub_classification', 'item_type', 'category', 'entry_mode',
        'brand', 'manufacturer', 'is_fixed_asset', 'is_serialized', 'is_active'
    ]
    search_fields = ['name', 'description', 'model', 'classification_code', 'sku']
    ordering_fields = ['name', 'classification_code', 'standard_cost', 'created_at', 'updated_at']
    ordering = ['classification_code']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ItemMasterListSerializer
        return ItemMasterDetailSerializer

    def get_queryset(self):
        """Optimize queryset with select_related"""
        queryset = super().get_queryset()
        if self.action in ['list', 'retrieve', 'update', 'partial_update']:
            queryset = queryset.select_related(
                'sub_classification', 'item_type', 'category', 'entry_mode',
                'brand', 'manufacturer', 'size', 'shape', 'quality', 'unit_of_measure',
                'created_by', 'updated_by'
            ).prefetch_related('tags')
        return queryset

    def perform_create(self, serializer):
        """Enhanced creation with audit trail"""
        try:
            with transaction.atomic():
                instance = serializer.save()
                instance.created_by = self.request.user
                instance.updated_by = self.request.user
                instance.save(update_fields=['created_by', 'updated_by'])
                
                # Update audit trail
                instance.update_audit_trail(self.request.user, 'CREATED')
                logger.info(f"ItemMaster created: {instance.classification_code} by {self.request.user.username}")
        except Exception as e:
            logger.error(f"Error creating ItemMaster: {e}")
            raise

    def perform_update(self, serializer):
        """Enhanced update with audit trail"""
        try:
            with transaction.atomic():
                old_instance = self.get_object()
                instance = serializer.save()
                instance.updated_by = self.request.user
                instance.save(update_fields=['updated_by'])
                
                # Update audit trail
                instance.update_audit_trail(self.request.user, 'UPDATED')
                logger.info(f"ItemMaster updated: {instance.classification_code} by {self.request.user.username}")
        except Exception as e:
            logger.error(f"Error updating ItemMaster: {e}")
            raise

    def perform_destroy(self, instance):
        """Soft delete with audit trail"""
        try:
            instance.is_active = False
            instance.updated_by = self.request.user
            instance.save(update_fields=['is_active', 'updated_by'])
            
            # Update audit trail
            instance.update_audit_trail(self.request.user, 'DELETED')
            logger.info(f"ItemMaster soft deleted: {instance.classification_code} by {self.request.user.username}")
        except Exception as e:
            logger.error(f"Error deleting ItemMaster: {e}")
            raise

    @action(detail=False, methods=['get'], url_path='stock-summary')
    def stock_summary(self, request):
        """Get stock summary statistics"""
        try:
            queryset = self.get_queryset()

            # Calculate stock status counts
            stock_counts = {
                'total_items': queryset.count(),
                'in_stock': 0,
                'low_stock': 0,
                'out_of_stock': 0,
                'needs_reorder': 0,
            }

            for item in queryset:
                try:
                    stock_status = item.stock_status
                    if stock_status == 'out_of_stock':
                        stock_counts['out_of_stock'] += 1
                    elif stock_status == 'low_stock':
                        stock_counts['low_stock'] += 1
                    elif stock_status == 'reorder_needed':
                        stock_counts['needs_reorder'] += 1
                    else:
                        stock_counts['in_stock'] += 1
                except Exception as e:
                    # If there's an error with an individual item, skip it
                    print(f"Error processing item {item.id}: {e}")
                    continue

            return Response(stock_counts)
        except Exception as e:
            # Return error details for debugging
            return Response({
                'error': str(e),
                'debug': 'stock_summary endpoint error'
            }, status=500)

    @action(detail=False, methods=['get'])
    def test_endpoint(self, request):
        """Simple test endpoint to verify ViewSet is working"""
        return Response({
            'message': 'Test endpoint working',
            'timestamp': timezone.now().isoformat(),
            'total_items': self.get_queryset().count()
        })

    @action(detail=False, methods=['get'])
    def reorder_suggestions(self, request):
        """Get items that need reordering"""
        items_needing_reorder = []
        for item in self.get_queryset():
            if item.needs_reorder():
                items_needing_reorder.append({
                    'id': item.id,
                    'name': item.name,
                    'classification_code': item.classification_code,
                    'current_stock': item.current_stock,
                    'reorder_point': item.reorder_point,
                    'suggested_quantity': item.suggested_order_quantity()
                })

        return Response(items_needing_reorder)

    @action(detail=True, methods=['get'])
    def stock_status(self, request, pk=None):
        """Get detailed stock status for an item"""
        item = self.get_object()
        return Response({
            'current_stock': item.current_stock,
            'stock_status': item.stock_status,
            'min_stock_level': item.min_stock_level,
            'max_stock_level': item.max_stock_level,
            'reorder_point': item.reorder_point,
            'needs_reorder': item.needs_reorder(),
            'suggested_order_quantity': item.suggested_order_quantity(),
            'total_value': float(item.total_value)
        })

    @action(detail=False, methods=['get'])
    def analytics(self, request):
        """Comprehensive analytics dashboard data"""
        cache_key = 'item_masters_analytics'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return Response(cached_data)
        
        try:
            queryset = self.get_queryset()
            
            # Stock analytics
            stock_analysis = queryset.aggregate(
                total_items=Count('id'),
                total_value=Sum(F('standard_cost') * F('min_stock_level')),
                avg_cost=Avg('standard_cost'),
                fixed_assets_count=Count(Case(When(is_fixed_asset=True, then=1))),
                serialized_count=Count(Case(When(is_serialized=True, then=1)))
            )
            
            # Category breakdown
            category_breakdown = list(
                queryset.values('category__name')
                .annotate(count=Count('id'), total_value=Sum('standard_cost'))
                .order_by('-count')[:10]
            )
            
            # Brand analysis
            brand_breakdown = list(
                queryset.filter(brand__isnull=False)
                .values('brand__name')
                .annotate(count=Count('id'))
                .order_by('-count')[:10]
            )
            
            # Cost distribution
            cost_ranges = [
                {'range': '0-100', 'count': queryset.filter(standard_cost__lt=100).count()},
                {'range': '100-500', 'count': queryset.filter(standard_cost__gte=100, standard_cost__lt=500).count()},
                {'range': '500-1000', 'count': queryset.filter(standard_cost__gte=500, standard_cost__lt=1000).count()},
                {'range': '1000+', 'count': queryset.filter(standard_cost__gte=1000).count()},
            ]
            
            analytics_data = {
                'overview': stock_analysis,
                'category_breakdown': category_breakdown,
                'brand_breakdown': brand_breakdown,
                'cost_distribution': cost_ranges,
                'generated_at': timezone.now().isoformat()
            }
            
            # Cache for 5 minutes
            cache.set(cache_key, analytics_data, 300)
            
            return Response(analytics_data)
            
        except Exception as e:
            logger.error(f"Error generating analytics: {e}")
            return Response(
                {'error': 'Failed to generate analytics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def bulk_update(self, request):
        """Bulk update multiple items"""
        try:
            with transaction.atomic():
                items_data = request.data.get('items', [])
                updated_count = 0
                
                for item_data in items_data:
                    item_id = item_data.get('id')
                    if not item_id:
                        continue
                        
                    try:
                        item = ItemMaster.objects.get(id=item_id)
                        for field, value in item_data.items():
                            if field != 'id' and hasattr(item, field):
                                setattr(item, field, value)
                        
                        item.updated_by = request.user
                        item.save()
                        item.update_audit_trail(request.user, 'BULK_UPDATED')
                        updated_count += 1
                        
                    except ItemMaster.DoesNotExist:
                        continue
                
                return Response({
                    'message': f'Successfully updated {updated_count} items',
                    'updated_count': updated_count
                })
                
        except Exception as e:
            logger.error(f"Error in bulk update: {e}")
            return Response(
                {'error': 'Bulk update failed'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def export_data(self, request):
        """Export items data for reporting"""
        try:
            queryset = self.filter_queryset(self.get_queryset())
            format_type = request.query_params.get('format', 'csv')
            
            # Prepare data for export
            export_data = []
            for item in queryset:
                export_data.append({
                    'classification_code': item.classification_code,
                    'sku': item.sku,
                    'name': item.name,
                    'description': item.description,
                    'brand': item.brand.name if item.brand else '',
                    'manufacturer': item.manufacturer.name if item.manufacturer else '',
                    'category': item.category.name,
                    'unit_of_measure': item.unit_of_measure.name,
                    'standard_cost': float(item.standard_cost),
                    'current_stock': item.current_stock,
                    'min_stock_level': item.min_stock_level,
                    'max_stock_level': item.max_stock_level or '',
                    'is_fixed_asset': item.is_fixed_asset,
                    'is_serialized': item.is_serialized,
                    'created_at': item.created_at.isoformat(),
                })
            
            return Response({
                'data': export_data,
                'count': len(export_data),
                'exported_at': timezone.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error exporting data: {e}")
            return Response(
                {'error': 'Export failed'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BatchItemViewSet(viewsets.ModelViewSet):
    """ViewSet for Batch Items"""
    queryset = BatchItem.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'item_master', 'supplier', 'approval_status', 'is_active'
    ]
    search_fields = [
        'batch_number', 'item_master__name', 'supplier__company_name',
        'invoice_number', 'purchase_order_number'
    ]
    ordering_fields = ['batch_number', 'received_date', 'created_at']
    ordering = ['-received_date']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return BatchItemListSerializer
        return BatchItemDetailSerializer

    def get_queryset(self):
        """Optimize queryset with select_related"""
        queryset = super().get_queryset()
        if self.action == 'list':
            queryset = queryset.select_related(
                'item_master', 'supplier', 'approval_status'
            )
        return queryset

    def perform_create(self, serializer):
        """Add debugging for batch item creation"""
        print(f"🔍 BatchItem create request data: {self.request.data}")
        try:
            instance = serializer.save()
            print(f"✅ BatchItem created successfully: {instance.id}")
        except Exception as e:
            print(f"❌ BatchItem creation error: {e}")
            import traceback
            traceback.print_exc()
            raise

    def perform_update(self, serializer):
        """Add debugging for batch item update"""
        print(f"🔍 BatchItem update request data: {self.request.data}")
        print(f"🔍 BatchItem instance: {serializer.instance.id}")
        try:
            instance = serializer.save()
            print(f"✅ BatchItem updated successfully: {instance.id}")
        except Exception as e:
            print(f"❌ BatchItem update error: {e}")
            import traceback
            traceback.print_exc()
            raise

    @action(detail=True, methods=['post'])
    def mark_inspected(self, request, pk=None):
        """Mark batch as inspected"""
        batch = self.get_object()
        accepted_qty = request.data.get('quantity_accepted', 0)
        rejected_qty = request.data.get('quantity_rejected', 0)
        notes = request.data.get('notes', '')

        try:
            batch.mark_as_inspected(
                accepted_qty, rejected_qty, request.user, notes
            )
            return Response({'message': 'Batch marked as inspected'})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def create_inventory_items(self, request, pk=None):
        """Create inventory items from batch"""
        batch = self.get_object()
        count = request.data.get('count', batch.quantity_available)

        try:
            items = batch.create_inventory_items(count)
            return Response({
                'message': f'Created {len(items)} inventory items',
                'items_created': len(items)
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class InventoryItemViewSet(viewsets.ModelViewSet):
    """ViewSet for Inventory Items"""
    queryset = InventoryItem.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'item_master', 'batch_item', 'status', 'property_status',
        'store', 'shelf', 'assigned_to', 'condition_rating', 'is_active'
    ]
    search_fields = [
        'registry_number', 'serial_number', 'barcode', 'asset_tag',
        'item_master__name', 'assigned_to__username', 'assigned_to__first_name',
        'assigned_to__last_name'
    ]
    ordering_fields = ['registry_number', 'received_date', 'created_at']
    ordering = ['-received_date']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return InventoryItemListSerializer
        return InventoryItemDetailSerializer

    def get_queryset(self):
        """Optimize queryset with select_related"""
        queryset = super().get_queryset()
        if self.action == 'list':
            queryset = queryset.select_related(
                'item_master', 'batch_item', 'batch_item__supplier',
                'status', 'property_status', 'approval_status',
                'store', 'shelf', 'assigned_to'
            )
        elif self.action in ['retrieve', 'update', 'partial_update']:
            queryset = queryset.select_related(
                'item_master', 'batch_item', 'batch_item__supplier',
                'status', 'property_status', 'approval_status',
                'store', 'shelf', 'assigned_to'
            )
        return queryset

    @action(detail=True, methods=['post'])
    def assign(self, request, pk=None):
        """Assign item to user"""
        item = self.get_object()
        user_id = request.data.get('user_id')
        notes = request.data.get('notes', '')

        try:
            from django.contrib.auth.models import User
            user = User.objects.get(id=user_id)
            item.assign_to_user(user, notes)
            return Response({'message': f'Item assigned to {user.get_full_name() or user.username}'})
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def unassign(self, request, pk=None):
        """Unassign item"""
        item = self.get_object()
        notes = request.data.get('notes', '')

        try:
            item.unassign(notes)
            return Response({'message': 'Item unassigned'})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def move(self, request, pk=None):
        """Move item to location"""
        item = self.get_object()
        store_id = request.data.get('store_id')
        shelf_id = request.data.get('shelf_id')
        notes = request.data.get('notes', '')

        try:
            from organization.models import Store, Shelf
            store = Store.objects.get(id=store_id)
            shelf = Shelf.objects.get(id=shelf_id) if shelf_id else None
            item.move_to_location(store, shelf, notes)
            return Response({'message': f'Item moved to {item.current_location}'})
        except (Store.DoesNotExist, Shelf.DoesNotExist):
            return Response({'error': 'Location not found'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def update_condition(self, request, pk=None):
        """Update item condition"""
        item = self.get_object()
        rating = request.data.get('rating')
        notes = request.data.get('notes', '')

        try:
            item.update_condition(rating, notes)
            return Response({'message': f'Condition updated to {item.condition_description}'})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
