from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.contrib.auth.models import User

from rest_framework import status, views, viewsets
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from .serializers import UserSerializer, LoginSerializer

@method_decorator(csrf_exempt, name='dispatch')
class LoginView(views.APIView):
    permission_classes = (AllowAny,)
    serializer_class = LoginSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = authenticate(
            username=serializer.validated_data['username'],
            password=serializer.validated_data['password']
        )

        if not user:
            return Response(
                {'error': 'Invalid credentials'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        refresh = RefreshToken.for_user(user)
        user_serializer = UserSerializer(user)

        return Response({
            'user': user_serializer.data,
            'token': str(refresh.access_token),
            'refresh': str(refresh)
        })

class UserView(views.APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data)


class UsersListView(views.APIView):
    """List all users for dropdowns and assignments"""
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        users = User.objects.filter(is_active=True).order_by('first_name', 'last_name', 'username')
        serializer = UserSerializer(users, many=True)
        return Response({
            'results': serializer.data,
            'count': users.count()
        })