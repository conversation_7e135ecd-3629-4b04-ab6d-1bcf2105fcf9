"""
Approval Status Serializers
"""
from rest_framework import serializers
from ..models import ApprovalStatus


class ApprovalStatusSerializer(serializers.ModelSerializer):
    """Serializer for ApprovalStatus model"""
    
    usage_count = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = ApprovalStatus
        fields = [
            'id',
            'code',
            'name',
            'description',
            'status_type',
            'color_code',
            'is_active',
            'is_default',
            'is_final',
            'allows_modification',
            'requires_comment',
            'display_name',
            'usage_count',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_name(self, value):
        """Validate name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
        return value


class ApprovalStatusListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing approval statuses"""
    
    usage_count = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = ApprovalStatus
        fields = [
            'id',
            'code',
            'name',
            'status_type',
            'color_code',
            'is_active',
            'is_default',
            'is_final',
            'allows_modification',
            'requires_comment',
            'display_name',
            'usage_count'
        ]


class ApprovalStatusDropdownSerializer(serializers.ModelSerializer):
    """Minimal serializer for dropdown/select options"""
    
    label = serializers.SerializerMethodField()
    value = serializers.CharField(source='id')
    
    class Meta:
        model = ApprovalStatus
        fields = ['value', 'label', 'color_code']
    
    def get_label(self, obj):
        return obj.display_name


class ApprovalStatusCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating approval statuses"""
    
    class Meta:
        model = ApprovalStatus
        fields = [
            'code',
            'name',
            'description',
            'status_type',
            'color_code',
            'is_active',
            'is_default',
            'is_final',
            'allows_modification',
            'requires_comment'
        ]
