from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from .models import UserProfile

# Define an inline admin descriptor for UserProfile model
class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'profile'

# Define a new User admin
class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline,)
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff', 'get_groups', 'get_assigned_office')
    list_filter = BaseUserAdmin.list_filter + ('groups', 'profile__assigned_office')

    def get_groups(self, obj):
        return ", ".join([g.name for g in obj.groups.all()])
    get_groups.short_description = 'Groups'

    def get_assigned_office(self, obj):
        try:
            return obj.profile.assigned_office
        except UserProfile.DoesNotExist:
            return None
    get_assigned_office.short_description = 'Assigned Office'

# Re-register UserAdmin
admin.site.unregister(User)
admin.site.register(User, UserAdmin)
