"""
Item Type Views
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from ..models import ItemType
from ..serializers import (
    ItemTypeSerializer,
    ItemTypeListSerializer,
    ItemTypeDropdownSerializer
)


class ItemTypeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Item Types
    
    Provides CRUD operations for item types with filtering,
    searching, and dropdown endpoints.
    """
    queryset = ItemType.objects.all()
    serializer_class = ItemTypeSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at', 'updated_at']
    ordering = ['name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ItemTypeListSerializer
        elif self.action == 'dropdown':
            return ItemTypeDropdownSerializer
        return ItemTypeSerializer

    def get_queryset(self):
        """Filter queryset based on action"""
        queryset = ItemType.objects.all()
        
        if self.action == 'dropdown':
            # Only return active items for dropdown
            queryset = queryset.filter(is_active=True)
        
        return queryset

    @swagger_auto_schema(
        method='get',
        operation_description="Get item types for dropdown/select options",
        responses={
            200: openapi.Response(
                description="List of item types for dropdown",
                examples={
                    "application/json": [
                        {
                            "value": "uuid-here",
                            "label": "Equipment"
                        }
                    ]
                }
            )
        }
    )
    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get item types formatted for dropdown/select options"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='post',
        operation_description="Bulk activate/deactivate item types",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'ids': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description='List of item type IDs'
                ),
                'is_active': openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    description='Active status to set'
                )
            },
            required=['ids', 'is_active']
        )
    )
    @action(detail=False, methods=['post'])
    def bulk_update_status(self, request):
        """Bulk update active status of item types"""
        ids = request.data.get('ids', [])
        is_active = request.data.get('is_active')
        
        if not ids:
            return Response(
                {'error': 'No IDs provided'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if is_active is None:
            return Response(
                {'error': 'is_active field is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        updated_count = ItemType.objects.filter(
            id__in=ids
        ).update(is_active=is_active)
        
        return Response({
            'message': f'Updated {updated_count} item types',
            'updated_count': updated_count
        })

    def destroy(self, request, *args, **kwargs):
        """Soft delete item type"""
        instance = self.get_object()
        instance.soft_delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
