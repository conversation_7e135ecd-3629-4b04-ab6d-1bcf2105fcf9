# Generated by Django 5.2.3 on 2025-06-21 15:48

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("inventory_setup", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="mainclassification",
            name="color",
            field=models.Char<PERSON>ield(
                default="#2196F3",
                help_text="Color code for visual identification (hex format)",
                max_length=7,
                validators=[
                    django.core.validators.RegexValidator(
                        message="Color must be a valid hex color code (e.g., #FF5722)",
                        regex="^#[0-9A-Fa-f]{6}$",
                    )
                ],
                verbose_name="Color",
            ),
        ),
        migrations.AddField(
            model_name="subclassification",
            name="color",
            field=models.CharField(
                default="#9C27B0",
                help_text="Color code for visual identification (hex format)",
                max_length=7,
                validators=[
                    django.core.validators.RegexValidator(
                        message="Color must be a valid hex color code (e.g., #FF5722)",
                        regex="^#[0-9A-Fa-f]{6}$",
                    )
                ],
                verbose_name="Color",
            ),
        ),
    ]
