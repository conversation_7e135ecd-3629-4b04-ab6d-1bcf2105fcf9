{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects v2\\\\asset management\\\\frontend\\\\src\\\\features\\\\help\\\\HelpMenu.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { Container, Typography, Grid, Card, CardContent, Box, Paper, Chip, useTheme, alpha, Breadcrumbs, Link, Alert, Button } from '@mui/material';\nimport { Help as HelpIcon, MenuBook as DocumentationIcon, VideoLibrary as TutorialsIcon, Support as SupportIcon, QuestionAnswer as FAQIcon, Home as HomeIcon, ContactSupport as ContactIcon, BugReport as BugReportIcon, Feedback as FeedbackIcon, School as TrainingIcon } from '@mui/icons-material';\nimport { Link as RouterLink } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HelpMenu = () => {\n  _s();\n  const theme = useTheme();\n\n  // Help management cards data\n  const helpCards = [{\n    id: 'documentation',\n    title: 'Documentation',\n    description: 'Comprehensive user guides and system documentation',\n    icon: /*#__PURE__*/_jsxDEV(DocumentationIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.primary.main,\n    path: '/help/documentation',\n    count: '50+',\n    adminOnly: false\n  }, {\n    id: 'video-tutorials',\n    title: 'Video Tutorials',\n    description: 'Step-by-step video guides for all features',\n    icon: /*#__PURE__*/_jsxDEV(TutorialsIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.secondary.main,\n    path: '/help/tutorials',\n    count: '25+',\n    adminOnly: false\n  }, {\n    id: 'faq',\n    title: 'FAQ',\n    description: 'Frequently asked questions and answers',\n    icon: /*#__PURE__*/_jsxDEV(FAQIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.info.main,\n    path: '/help/faq',\n    count: '100+',\n    adminOnly: false\n  }, {\n    id: 'contact-support',\n    title: 'Contact Support',\n    description: 'Get help from our technical support team',\n    icon: /*#__PURE__*/_jsxDEV(ContactIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.success.main,\n    path: '/help/contact',\n    count: '24/7',\n    adminOnly: false\n  }, {\n    id: 'training',\n    title: 'Training Resources',\n    description: 'Training materials and certification programs',\n    icon: /*#__PURE__*/_jsxDEV(TrainingIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.warning.main,\n    path: '/help/training',\n    count: 'New',\n    adminOnly: false\n  }, {\n    id: 'bug-report',\n    title: 'Report Issues',\n    description: 'Report bugs and submit feature requests',\n    icon: /*#__PURE__*/_jsxDEV(BugReportIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.error.main,\n    path: '/help/report',\n    count: 'Open',\n    adminOnly: false\n  }];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Breadcrumbs, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        component: RouterLink,\n        to: \"/dashboard\",\n        color: \"inherit\",\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(HomeIcon, {\n          sx: {\n            mr: 0.5\n          },\n          fontSize: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), \"Dashboard\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        color: \"text.primary\",\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(HelpIcon, {\n          sx: {\n            mr: 0.5\n          },\n          fontSize: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), \"Help & Support\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h3\",\n        component: \"h1\",\n        gutterBottom: true,\n        fontWeight: \"bold\",\n        children: \"Help & Support\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"Access documentation, tutorials, and support resources\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 4,\n        display: 'flex',\n        alignItems: 'center',\n        bgcolor: alpha(theme.palette.primary.main, 0.1),\n        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,\n        borderRadius: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(SupportIcon, {\n        color: \"primary\",\n        sx: {\n          mr: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"primary.main\",\n          children: \"Help & Support Center\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Find answers, learn new features, and get the support you need to maximize your productivity\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\uD83C\\uDD98 Need Immediate Help?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        paragraph: true,\n        children: \"For urgent issues or technical support, contact our support team directly:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2,\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          size: \"small\",\n          disabled: true,\n          children: \"\\uD83D\\uDCE7 <EMAIL>\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          size: \"small\",\n          disabled: true,\n          children: \"\\uD83D\\uDCDE +1 (555) 123-4567\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          size: \"small\",\n          disabled: true,\n          children: \"\\uD83D\\uDCAC Live Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: helpCards.map((card, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            transition: 'transform 0.3s, box-shadow 0.3s',\n            '&:hover': {\n              transform: 'translateY(-8px)',\n              boxShadow: '0 12px 20px rgba(0,0,0,0.1)'\n            },\n            borderTop: `4px solid ${card.color}`,\n            borderRadius: 2,\n            position: 'relative',\n            overflow: 'visible',\n            cursor: 'not-allowed',\n            opacity: 0.7\n          },\n          children: [card.count && /*#__PURE__*/_jsxDEV(Chip, {\n            label: card.count,\n            color: \"primary\",\n            size: \"small\",\n            sx: {\n              position: 'absolute',\n              top: -10,\n              right: 16,\n              fontWeight: 'bold',\n              boxShadow: '0 2px 5px rgba(0,0,0,0.2)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1,\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                width: 80,\n                height: 80,\n                borderRadius: '50%',\n                bgcolor: alpha(card.color, 0.1),\n                mb: 2,\n                mx: 'auto'\n              },\n              children: card.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"h2\",\n              gutterBottom: true,\n              textAlign: \"center\",\n              fontWeight: \"bold\",\n              children: card.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              textAlign: \"center\",\n              children: card.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"warning.main\",\n              textAlign: \"center\",\n              display: \"block\",\n              sx: {\n                mt: 1\n              },\n              children: \"Coming Soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 4,\n        borderRadius: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"System Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Version\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Asset Management v1.0.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Last Updated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"December 2024\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Environment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Development\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Support Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"success.main\",\n            children: \"Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 2,\n        borderRadius: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Quick Links\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            fullWidth: true,\n            disabled: true,\n            startIcon: /*#__PURE__*/_jsxDEV(DocumentationIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 70\n            }, this),\n            children: \"User Manual\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            fullWidth: true,\n            disabled: true,\n            startIcon: /*#__PURE__*/_jsxDEV(TutorialsIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 70\n            }, this),\n            children: \"Getting Started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            fullWidth: true,\n            disabled: true,\n            startIcon: /*#__PURE__*/_jsxDEV(FeedbackIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 70\n            }, this),\n            children: \"Send Feedback\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 5\n  }, this);\n};\n_s(HelpMenu, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c = HelpMenu;\nexport default HelpMenu;\nvar _c;\n$RefreshReg$(_c, \"HelpMenu\");", "map": {"version": 3, "names": ["useState", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Box", "Paper", "Chip", "useTheme", "alpha", "Breadcrumbs", "Link", "<PERSON><PERSON>", "<PERSON><PERSON>", "Help", "HelpIcon", "MenuBook", "DocumentationIcon", "VideoLibrary", "TutorialsIcon", "Support", "SupportIcon", "QuestionAnswer", "FAQIcon", "Home", "HomeIcon", "ContactSupport", "ContactIcon", "BugReport", "BugReportIcon", "<PERSON><PERSON><PERSON>", "FeedbackIcon", "School", "TrainingIcon", "RouterLink", "jsxDEV", "_jsxDEV", "HelpMenu", "_s", "theme", "helpCards", "id", "title", "description", "icon", "sx", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "palette", "primary", "main", "path", "count", "adminOnly", "secondary", "info", "success", "warning", "error", "max<PERSON><PERSON><PERSON>", "py", "children", "mb", "component", "to", "display", "alignItems", "mr", "variant", "gutterBottom", "fontWeight", "p", "bgcolor", "border", "borderRadius", "severity", "paragraph", "gap", "flexWrap", "size", "disabled", "container", "spacing", "map", "card", "index", "item", "xs", "sm", "md", "height", "flexDirection", "transition", "transform", "boxShadow", "borderTop", "position", "overflow", "cursor", "opacity", "label", "top", "right", "flexGrow", "justifyContent", "width", "mx", "textAlign", "mt", "fullWidth", "startIcon", "_c", "$RefreshReg$"], "sources": ["D:/Projects v2/asset management/frontend/src/features/help/HelpMenu.js"], "sourcesContent": ["import { useState } from 'react';\nimport {\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  Box,\n  Paper,\n  Chip,\n  useTheme,\n  alpha,\n  Breadcrum<PERSON>,\n  <PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON>\n} from '@mui/material';\nimport {\n  Help as HelpIcon,\n  MenuBook as DocumentationIcon,\n  VideoLibrary as TutorialsIcon,\n  Support as SupportIcon,\n  QuestionAnswer as FAQIcon,\n  Home as HomeIcon,\n  ContactSupport as ContactIcon,\n  BugReport as BugReportIcon,\n  Feedback as FeedbackIcon,\n  School as TrainingIcon\n} from '@mui/icons-material';\nimport { Link as RouterLink } from 'react-router-dom';\n\nconst HelpMenu = () => {\n  const theme = useTheme();\n\n  // Help management cards data\n  const helpCards = [\n    {\n      id: 'documentation',\n      title: 'Documentation',\n      description: 'Comprehensive user guides and system documentation',\n      icon: <DocumentationIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.primary.main,\n      path: '/help/documentation',\n      count: '50+',\n      adminOnly: false\n    },\n    {\n      id: 'video-tutorials',\n      title: 'Video Tutorials',\n      description: 'Step-by-step video guides for all features',\n      icon: <TutorialsIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.secondary.main,\n      path: '/help/tutorials',\n      count: '25+',\n      adminOnly: false\n    },\n    {\n      id: 'faq',\n      title: 'FAQ',\n      description: 'Frequently asked questions and answers',\n      icon: <FAQIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.info.main,\n      path: '/help/faq',\n      count: '100+',\n      adminOnly: false\n    },\n    {\n      id: 'contact-support',\n      title: 'Contact Support',\n      description: 'Get help from our technical support team',\n      icon: <ContactIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.success.main,\n      path: '/help/contact',\n      count: '24/7',\n      adminOnly: false\n    },\n    {\n      id: 'training',\n      title: 'Training Resources',\n      description: 'Training materials and certification programs',\n      icon: <TrainingIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.warning.main,\n      path: '/help/training',\n      count: 'New',\n      adminOnly: false\n    },\n    {\n      id: 'bug-report',\n      title: 'Report Issues',\n      description: 'Report bugs and submit feature requests',\n      icon: <BugReportIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.error.main,\n      path: '/help/report',\n      count: 'Open',\n      adminOnly: false\n    }\n  ];\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n      {/* Breadcrumbs */}\n      <Breadcrumbs sx={{ mb: 3 }}>\n        <Link\n          component={RouterLink}\n          to=\"/dashboard\"\n          color=\"inherit\"\n          sx={{ display: 'flex', alignItems: 'center' }}\n        >\n          <HomeIcon sx={{ mr: 0.5 }} fontSize=\"inherit\" />\n          Dashboard\n        </Link>\n        <Typography color=\"text.primary\" sx={{ display: 'flex', alignItems: 'center' }}>\n          <HelpIcon sx={{ mr: 0.5 }} fontSize=\"inherit\" />\n          Help & Support\n        </Typography>\n      </Breadcrumbs>\n\n      {/* Header */}\n      <Box sx={{ mb: 4 }}>\n        <Typography variant=\"h3\" component=\"h1\" gutterBottom fontWeight=\"bold\">\n          Help & Support\n        </Typography>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Access documentation, tutorials, and support resources\n        </Typography>\n      </Box>\n\n      {/* Info Paper */}\n      <Paper\n        sx={{\n          p: 2,\n          mb: 4,\n          display: 'flex',\n          alignItems: 'center',\n          bgcolor: alpha(theme.palette.primary.main, 0.1),\n          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,\n          borderRadius: 2\n        }}\n      >\n        <SupportIcon color=\"primary\" sx={{ mr: 2 }} />\n        <Box>\n          <Typography variant=\"h6\" color=\"primary.main\">\n            Help & Support Center\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Find answers, learn new features, and get the support you need to maximize your productivity\n          </Typography>\n        </Box>\n      </Paper>\n\n      {/* Quick Help Section */}\n      <Alert severity=\"info\" sx={{ mb: 4 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          🆘 Need Immediate Help?\n        </Typography>\n        <Typography variant=\"body2\" paragraph>\n          For urgent issues or technical support, contact our support team directly:\n        </Typography>\n        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n          <Button variant=\"contained\" size=\"small\" disabled>\n            📧 <EMAIL>\n          </Button>\n          <Button variant=\"outlined\" size=\"small\" disabled>\n            📞 +1 (555) 123-4567\n          </Button>\n          <Button variant=\"outlined\" size=\"small\" disabled>\n            💬 Live Chat\n          </Button>\n        </Box>\n      </Alert>\n\n      {/* Help Management Cards */}\n      <Grid container spacing={3}>\n        {helpCards.map((card, index) => (\n          <Grid item xs={12} sm={6} md={4} key={index}>\n            <Card\n              sx={{\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                transition: 'transform 0.3s, box-shadow 0.3s',\n                '&:hover': {\n                  transform: 'translateY(-8px)',\n                  boxShadow: '0 12px 20px rgba(0,0,0,0.1)',\n                },\n                borderTop: `4px solid ${card.color}`,\n                borderRadius: 2,\n                position: 'relative',\n                overflow: 'visible',\n                cursor: 'not-allowed',\n                opacity: 0.7\n              }}\n            >\n              {card.count && (\n                <Chip\n                  label={card.count}\n                  color=\"primary\"\n                  size=\"small\"\n                  sx={{\n                    position: 'absolute',\n                    top: -10,\n                    right: 16,\n                    fontWeight: 'bold',\n                    boxShadow: '0 2px 5px rgba(0,0,0,0.2)'\n                  }}\n                />\n              )}\n              <CardContent sx={{ flexGrow: 1, p: 3 }}>\n                <Box\n                  sx={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    width: 80,\n                    height: 80,\n                    borderRadius: '50%',\n                    bgcolor: alpha(card.color, 0.1),\n                    mb: 2,\n                    mx: 'auto'\n                  }}\n                >\n                  {card.icon}\n                </Box>\n                <Typography variant=\"h6\" component=\"h2\" gutterBottom textAlign=\"center\" fontWeight=\"bold\">\n                  {card.title}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\" textAlign=\"center\">\n                  {card.description}\n                </Typography>\n                <Typography variant=\"caption\" color=\"warning.main\" textAlign=\"center\" display=\"block\" sx={{ mt: 1 }}>\n                  Coming Soon\n                </Typography>\n              </CardContent>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* System Information */}\n      <Paper sx={{ p: 3, mt: 4, borderRadius: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          System Information\n        </Typography>\n        <Grid container spacing={2}>\n          <Grid item xs={12} sm={6} md={3}>\n            <Typography variant=\"subtitle2\" gutterBottom>\n              Version\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Asset Management v1.0.0\n            </Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Typography variant=\"subtitle2\" gutterBottom>\n              Last Updated\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              December 2024\n            </Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Typography variant=\"subtitle2\" gutterBottom>\n              Environment\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Development\n            </Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Typography variant=\"subtitle2\" gutterBottom>\n              Support Status\n            </Typography>\n            <Typography variant=\"body2\" color=\"success.main\">\n              Active\n            </Typography>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {/* Quick Links */}\n      <Paper sx={{ p: 3, mt: 2, borderRadius: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Quick Links\n        </Typography>\n        <Grid container spacing={2}>\n          <Grid item xs={12} sm={6} md={4}>\n            <Button variant=\"outlined\" fullWidth disabled startIcon={<DocumentationIcon />}>\n              User Manual\n            </Button>\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <Button variant=\"outlined\" fullWidth disabled startIcon={<TutorialsIcon />}>\n              Getting Started\n            </Button>\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <Button variant=\"outlined\" fullWidth disabled startIcon={<FeedbackIcon />}>\n              Send Feedback\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n    </Container>\n  );\n};\n\nexport default HelpMenu;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SACEC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,MAAM,QACD,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,iBAAiB,EAC7BC,YAAY,IAAIC,aAAa,EAC7BC,OAAO,IAAIC,WAAW,EACtBC,cAAc,IAAIC,OAAO,EACzBC,IAAI,IAAIC,QAAQ,EAChBC,cAAc,IAAIC,WAAW,EAC7BC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,YAAY,QACjB,qBAAqB;AAC5B,SAAStB,IAAI,IAAIuB,UAAU,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,KAAK,GAAG/B,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAMgC,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,oDAAoD;IACjEC,IAAI,eAAER,OAAA,CAACnB,iBAAiB;MAAC4B,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjDC,KAAK,EAAEZ,KAAK,CAACa,OAAO,CAACC,OAAO,CAACC,IAAI;IACjCC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE;EACb,CAAC,EACD;IACEhB,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,4CAA4C;IACzDC,IAAI,eAAER,OAAA,CAACjB,aAAa;MAAC0B,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7CC,KAAK,EAAEZ,KAAK,CAACa,OAAO,CAACM,SAAS,CAACJ,IAAI;IACnCC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE;EACb,CAAC,EACD;IACEhB,EAAE,EAAE,KAAK;IACTC,KAAK,EAAE,KAAK;IACZC,WAAW,EAAE,wCAAwC;IACrDC,IAAI,eAAER,OAAA,CAACb,OAAO;MAACsB,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvCC,KAAK,EAAEZ,KAAK,CAACa,OAAO,CAACO,IAAI,CAACL,IAAI;IAC9BC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE;EACb,CAAC,EACD;IACEhB,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,0CAA0C;IACvDC,IAAI,eAAER,OAAA,CAACT,WAAW;MAACkB,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3CC,KAAK,EAAEZ,KAAK,CAACa,OAAO,CAACQ,OAAO,CAACN,IAAI;IACjCC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE;EACb,CAAC,EACD;IACEhB,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,+CAA+C;IAC5DC,IAAI,eAAER,OAAA,CAACH,YAAY;MAACY,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5CC,KAAK,EAAEZ,KAAK,CAACa,OAAO,CAACS,OAAO,CAACP,IAAI;IACjCC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE;EACb,CAAC,EACD;IACEhB,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,yCAAyC;IACtDC,IAAI,eAAER,OAAA,CAACP,aAAa;MAACgB,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7CC,KAAK,EAAEZ,KAAK,CAACa,OAAO,CAACU,KAAK,CAACR,IAAI;IAC/BC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE;EACb,CAAC,CACF;EAED,oBACErB,OAAA,CAACpC,SAAS;IAAC+D,QAAQ,EAAC,IAAI;IAAClB,EAAE,EAAE;MAAEmB,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAErC7B,OAAA,CAAC1B,WAAW;MAACmC,EAAE,EAAE;QAAEqB,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACzB7B,OAAA,CAACzB,IAAI;QACHwD,SAAS,EAAEjC,UAAW;QACtBkC,EAAE,EAAC,YAAY;QACfjB,KAAK,EAAC,SAAS;QACfN,EAAE,EAAE;UAAEwB,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAL,QAAA,gBAE9C7B,OAAA,CAACX,QAAQ;UAACoB,EAAE,EAAE;YAAE0B,EAAE,EAAE;UAAI,CAAE;UAACzB,QAAQ,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAElD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPd,OAAA,CAACnC,UAAU;QAACkD,KAAK,EAAC,cAAc;QAACN,EAAE,EAAE;UAAEwB,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAL,QAAA,gBAC7E7B,OAAA,CAACrB,QAAQ;UAAC8B,EAAE,EAAE;YAAE0B,EAAE,EAAE;UAAI,CAAE;UAACzB,QAAQ,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAElD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGdd,OAAA,CAAC/B,GAAG;MAACwC,EAAE,EAAE;QAAEqB,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACjB7B,OAAA,CAACnC,UAAU;QAACuE,OAAO,EAAC,IAAI;QAACL,SAAS,EAAC,IAAI;QAACM,YAAY;QAACC,UAAU,EAAC,MAAM;QAAAT,QAAA,EAAC;MAEvE;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbd,OAAA,CAACnC,UAAU;QAACuE,OAAO,EAAC,IAAI;QAACrB,KAAK,EAAC,gBAAgB;QAAAc,QAAA,EAAC;MAEhD;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNd,OAAA,CAAC9B,KAAK;MACJuC,EAAE,EAAE;QACF8B,CAAC,EAAE,CAAC;QACJT,EAAE,EAAE,CAAC;QACLG,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBM,OAAO,EAAEnE,KAAK,CAAC8B,KAAK,CAACa,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,GAAG,CAAC;QAC/CuB,MAAM,EAAE,aAAapE,KAAK,CAAC8B,KAAK,CAACa,OAAO,CAACC,OAAO,CAACC,IAAI,EAAE,GAAG,CAAC,EAAE;QAC7DwB,YAAY,EAAE;MAChB,CAAE;MAAAb,QAAA,gBAEF7B,OAAA,CAACf,WAAW;QAAC8B,KAAK,EAAC,SAAS;QAACN,EAAE,EAAE;UAAE0B,EAAE,EAAE;QAAE;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Cd,OAAA,CAAC/B,GAAG;QAAA4D,QAAA,gBACF7B,OAAA,CAACnC,UAAU;UAACuE,OAAO,EAAC,IAAI;UAACrB,KAAK,EAAC,cAAc;UAAAc,QAAA,EAAC;QAE9C;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbd,OAAA,CAACnC,UAAU;UAACuE,OAAO,EAAC,OAAO;UAACrB,KAAK,EAAC,gBAAgB;UAAAc,QAAA,EAAC;QAEnD;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRd,OAAA,CAACxB,KAAK;MAACmE,QAAQ,EAAC,MAAM;MAAClC,EAAE,EAAE;QAAEqB,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACnC7B,OAAA,CAACnC,UAAU;QAACuE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAR,QAAA,EAAC;MAEtC;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbd,OAAA,CAACnC,UAAU;QAACuE,OAAO,EAAC,OAAO;QAACQ,SAAS;QAAAf,QAAA,EAAC;MAEtC;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbd,OAAA,CAAC/B,GAAG;QAACwC,EAAE,EAAE;UAAEwB,OAAO,EAAE,MAAM;UAAEY,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAjB,QAAA,gBACrD7B,OAAA,CAACvB,MAAM;UAAC2D,OAAO,EAAC,WAAW;UAACW,IAAI,EAAC,OAAO;UAACC,QAAQ;UAAAnB,QAAA,EAAC;QAElD;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTd,OAAA,CAACvB,MAAM;UAAC2D,OAAO,EAAC,UAAU;UAACW,IAAI,EAAC,OAAO;UAACC,QAAQ;UAAAnB,QAAA,EAAC;QAEjD;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTd,OAAA,CAACvB,MAAM;UAAC2D,OAAO,EAAC,UAAU;UAACW,IAAI,EAAC,OAAO;UAACC,QAAQ;UAAAnB,QAAA,EAAC;QAEjD;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRd,OAAA,CAAClC,IAAI;MAACmF,SAAS;MAACC,OAAO,EAAE,CAAE;MAAArB,QAAA,EACxBzB,SAAS,CAAC+C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBrD,OAAA,CAAClC,IAAI;QAACwF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eAC9B7B,OAAA,CAACjC,IAAI;UACH0C,EAAE,EAAE;YACFiD,MAAM,EAAE,MAAM;YACdzB,OAAO,EAAE,MAAM;YACf0B,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE,iCAAiC;YAC7C,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb,CAAC;YACDC,SAAS,EAAE,aAAaX,IAAI,CAACrC,KAAK,EAAE;YACpC2B,YAAY,EAAE,CAAC;YACfsB,QAAQ,EAAE,UAAU;YACpBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE,aAAa;YACrBC,OAAO,EAAE;UACX,CAAE;UAAAtC,QAAA,GAEDuB,IAAI,CAAChC,KAAK,iBACTpB,OAAA,CAAC7B,IAAI;YACHiG,KAAK,EAAEhB,IAAI,CAAChC,KAAM;YAClBL,KAAK,EAAC,SAAS;YACfgC,IAAI,EAAC,OAAO;YACZtC,EAAE,EAAE;cACFuD,QAAQ,EAAE,UAAU;cACpBK,GAAG,EAAE,CAAC,EAAE;cACRC,KAAK,EAAE,EAAE;cACThC,UAAU,EAAE,MAAM;cAClBwB,SAAS,EAAE;YACb;UAAE;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF,eACDd,OAAA,CAAChC,WAAW;YAACyC,EAAE,EAAE;cAAE8D,QAAQ,EAAE,CAAC;cAAEhC,CAAC,EAAE;YAAE,CAAE;YAAAV,QAAA,gBACrC7B,OAAA,CAAC/B,GAAG;cACFwC,EAAE,EAAE;gBACFwB,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBsC,cAAc,EAAE,QAAQ;gBACxBC,KAAK,EAAE,EAAE;gBACTf,MAAM,EAAE,EAAE;gBACVhB,YAAY,EAAE,KAAK;gBACnBF,OAAO,EAAEnE,KAAK,CAAC+E,IAAI,CAACrC,KAAK,EAAE,GAAG,CAAC;gBAC/Be,EAAE,EAAE,CAAC;gBACL4C,EAAE,EAAE;cACN,CAAE;cAAA7C,QAAA,EAEDuB,IAAI,CAAC5C;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACNd,OAAA,CAACnC,UAAU;cAACuE,OAAO,EAAC,IAAI;cAACL,SAAS,EAAC,IAAI;cAACM,YAAY;cAACsC,SAAS,EAAC,QAAQ;cAACrC,UAAU,EAAC,MAAM;cAAAT,QAAA,EACtFuB,IAAI,CAAC9C;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACbd,OAAA,CAACnC,UAAU;cAACuE,OAAO,EAAC,OAAO;cAACrB,KAAK,EAAC,gBAAgB;cAAC4D,SAAS,EAAC,QAAQ;cAAA9C,QAAA,EAClEuB,IAAI,CAAC7C;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACbd,OAAA,CAACnC,UAAU;cAACuE,OAAO,EAAC,SAAS;cAACrB,KAAK,EAAC,cAAc;cAAC4D,SAAS,EAAC,QAAQ;cAAC1C,OAAO,EAAC,OAAO;cAACxB,EAAE,EAAE;gBAAEmE,EAAE,EAAE;cAAE,CAAE;cAAA/C,QAAA,EAAC;YAErG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GA3D6BuC,KAAK;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4DrC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPd,OAAA,CAAC9B,KAAK;MAACuC,EAAE,EAAE;QAAE8B,CAAC,EAAE,CAAC;QAAEqC,EAAE,EAAE,CAAC;QAAElC,YAAY,EAAE;MAAE,CAAE;MAAAb,QAAA,gBAC1C7B,OAAA,CAACnC,UAAU;QAACuE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAR,QAAA,EAAC;MAEtC;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbd,OAAA,CAAClC,IAAI;QAACmF,SAAS;QAACC,OAAO,EAAE,CAAE;QAAArB,QAAA,gBACzB7B,OAAA,CAAClC,IAAI;UAACwF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,gBAC9B7B,OAAA,CAACnC,UAAU;YAACuE,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAR,QAAA,EAAC;UAE7C;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbd,OAAA,CAACnC,UAAU;YAACuE,OAAO,EAAC,OAAO;YAACrB,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAEnD;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPd,OAAA,CAAClC,IAAI;UAACwF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,gBAC9B7B,OAAA,CAACnC,UAAU;YAACuE,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAR,QAAA,EAAC;UAE7C;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbd,OAAA,CAACnC,UAAU;YAACuE,OAAO,EAAC,OAAO;YAACrB,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAEnD;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPd,OAAA,CAAClC,IAAI;UAACwF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,gBAC9B7B,OAAA,CAACnC,UAAU;YAACuE,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAR,QAAA,EAAC;UAE7C;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbd,OAAA,CAACnC,UAAU;YAACuE,OAAO,EAAC,OAAO;YAACrB,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAEnD;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPd,OAAA,CAAClC,IAAI;UAACwF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,gBAC9B7B,OAAA,CAACnC,UAAU;YAACuE,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAR,QAAA,EAAC;UAE7C;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbd,OAAA,CAACnC,UAAU;YAACuE,OAAO,EAAC,OAAO;YAACrB,KAAK,EAAC,cAAc;YAAAc,QAAA,EAAC;UAEjD;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRd,OAAA,CAAC9B,KAAK;MAACuC,EAAE,EAAE;QAAE8B,CAAC,EAAE,CAAC;QAAEqC,EAAE,EAAE,CAAC;QAAElC,YAAY,EAAE;MAAE,CAAE;MAAAb,QAAA,gBAC1C7B,OAAA,CAACnC,UAAU;QAACuE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAR,QAAA,EAAC;MAEtC;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbd,OAAA,CAAClC,IAAI;QAACmF,SAAS;QAACC,OAAO,EAAE,CAAE;QAAArB,QAAA,gBACzB7B,OAAA,CAAClC,IAAI;UAACwF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAC9B7B,OAAA,CAACvB,MAAM;YAAC2D,OAAO,EAAC,UAAU;YAACyC,SAAS;YAAC7B,QAAQ;YAAC8B,SAAS,eAAE9E,OAAA,CAACnB,iBAAiB;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAe,QAAA,EAAC;UAEhF;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACPd,OAAA,CAAClC,IAAI;UAACwF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAC9B7B,OAAA,CAACvB,MAAM;YAAC2D,OAAO,EAAC,UAAU;YAACyC,SAAS;YAAC7B,QAAQ;YAAC8B,SAAS,eAAE9E,OAAA,CAACjB,aAAa;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAe,QAAA,EAAC;UAE5E;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACPd,OAAA,CAAClC,IAAI;UAACwF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAC9B7B,OAAA,CAACvB,MAAM;YAAC2D,OAAO,EAAC,UAAU;YAACyC,SAAS;YAAC7B,QAAQ;YAAC8B,SAAS,eAAE9E,OAAA,CAACL,YAAY;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAe,QAAA,EAAC;UAE3E;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACZ,EAAA,CAjRID,QAAQ;EAAA,QACE7B,QAAQ;AAAA;AAAA2G,EAAA,GADlB9E,QAAQ;AAmRd,eAAeA,QAAQ;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}