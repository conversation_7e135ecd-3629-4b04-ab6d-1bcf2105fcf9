import { useState, useEffect, useCallback } from "react";

import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Switch,
  FormControlLabel,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  Paper,
  InputAdornment,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Fade,
  Avatar,
  Container,
  LinearProgress,
  Collapse,
} from "@mui/material";
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Add as AddIcon,
  Info as InfoIcon,
  Inventory as InventoryIcon,
  Category as CategoryIcon,
  AttachMoney as MoneyIcon,
  Settings as SettingsIcon,
  NavigateNext as NextIcon,
  NavigateBefore as BackIcon,
  CheckCircle as CheckIcon,
  Assignment as AssignmentIcon,
  Build as BuildIcon,
  TrendingUp as TrendingUpIcon,
} from "@mui/icons-material";
import { useNavigate, useParams } from "react-router-dom";
import { useSnackbar } from "notistack";
import api from "../../utils/axios";
import {
  API_ENDPOINTS,
  ROUTES,
  MESSAGES,
  FIELD_LABELS,
  PLACEHOLDERS,
  VALIDATION_RULES,
  UI_CONSTANTS,
  COLOR_THEMES,
  BUTTON_TEXT,
  STEP_CONFIGS,
} from "../../config/formConfig";

// Helper function to get step icons
function getStepIcon(stepKey) {
  const iconMap = {
    basic_info: <InfoIcon />,
    classification: <CategoryIcon />,
    physical_properties: <BuildIcon />,
    financial_info: <MoneyIcon />,
    inventory_management: <TrendingUpIcon />,
    asset_properties: <SettingsIcon />,
  };
  return iconMap[stepKey] || <InfoIcon />;
}

// Step Components - Defined outside to prevent recreation on each render
const BasicInformationStep = ({ formData, handleInputChange }) => (
  <Box sx={{ mt: 2 }}>
    <Grid container spacing={3}>
      <Grid item xs={UI_CONSTANTS.GRID_BREAKPOINTS.XS}>
        <TextField
          fullWidth
          label={FIELD_LABELS.ITEM_NAME}
          value={formData.name || ''}
          onChange={handleInputChange('name')}
          required
          placeholder={PLACEHOLDERS.ITEM_NAME}
          variant="outlined"
          sx={{ mb: UI_CONSTANTS.FIELD_MARGIN_BOTTOM }}
        />
      </Grid>
      <Grid item xs={UI_CONSTANTS.GRID_BREAKPOINTS.XS} md={UI_CONSTANTS.GRID_BREAKPOINTS.MD_HALF}>
        <TextField
          fullWidth
          label={FIELD_LABELS.MODEL_NUMBER}
          value={formData.model || ''}
          onChange={handleInputChange('model')}
          placeholder={PLACEHOLDERS.MODEL_NUMBER}
          variant="outlined"
        />
      </Grid>
      <Grid item xs={UI_CONSTANTS.GRID_BREAKPOINTS.XS} md={UI_CONSTANTS.GRID_BREAKPOINTS.MD_HALF}>
        <Box sx={{ height: '56px' }} /> {/* Spacer for alignment */}
      </Grid>
      <Grid item xs={UI_CONSTANTS.GRID_BREAKPOINTS.XS}>
        <TextField
          fullWidth
          label={FIELD_LABELS.DESCRIPTION}
          value={formData.description || ''}
          onChange={handleInputChange('description')}
          multiline
          rows={4}
          placeholder={PLACEHOLDERS.DESCRIPTION}
          variant="outlined"
        />
      </Grid>
    </Grid>
  </Box>
);

const ClassificationStep = ({ formData, dropdownOptions, handleInputChange }) => (
  <Box sx={{ mt: UI_CONSTANTS.FIELD_MARGIN_BOTTOM }}>
    <Alert severity="info" sx={{ mb: UI_CONSTANTS.SECTION_MARGIN_BOTTOM }}>
      <Typography variant="body2">
        <strong>Required:</strong> {MESSAGES.INFO.CLASSIFICATION_REQUIRED}
      </Typography>
    </Alert>
    <Grid container spacing={3}>
      <Grid item xs={UI_CONSTANTS.GRID_BREAKPOINTS.XS} md={UI_CONSTANTS.GRID_BREAKPOINTS.MD_HALF}>
        <FormControl fullWidth required>
          <InputLabel>{FIELD_LABELS.SUB_CLASSIFICATION}</InputLabel>
          <Select
            value={formData.sub_classification || ''}
            onChange={handleInputChange('sub_classification')}
            label={FIELD_LABELS.SUB_CLASSIFICATION}
          >
            {dropdownOptions.subClassifications.map((option) => (
              <MenuItem key={option.id || option.value} value={option.id || option.value}>
                {option.display_name || option.label || option.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={UI_CONSTANTS.GRID_BREAKPOINTS.XS} md={UI_CONSTANTS.GRID_BREAKPOINTS.MD_HALF}>
        <FormControl fullWidth required>
          <InputLabel>{FIELD_LABELS.ITEM_TYPE}</InputLabel>
          <Select
            value={formData.item_type || ''}
            onChange={handleInputChange('item_type')}
            label={FIELD_LABELS.ITEM_TYPE}
          >
            {dropdownOptions.itemTypes.map((option) => (
              <MenuItem key={option.id || option.value} value={option.id || option.value}>
                {option.name || option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth required>
          <InputLabel>Category</InputLabel>
          <Select
            value={formData.category || ''}
            onChange={handleInputChange('category')}
            label="Category"
          >
            {dropdownOptions.categories.map((option) => (
              <MenuItem key={option.id || option.value} value={option.id || option.value}>
                {option.name || option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth required>
          <InputLabel>Unit of Measure</InputLabel>
          <Select
            value={formData.unit_of_measure || ''}
            onChange={handleInputChange('unit_of_measure')}
            label="Unit of Measure"
          >
            {dropdownOptions.unitsOfMeasure.map((option) => (
              <MenuItem key={option.id || option.value} value={option.id || option.value}>
                {option.name || option.label} {option.symbol && `(${option.symbol})`}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12}>
        <FormControl fullWidth>
          <InputLabel>Entry Mode</InputLabel>
          <Select
            value={formData.entry_mode || ''}
            onChange={handleInputChange('entry_mode')}
            label="Entry Mode"
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {dropdownOptions.entryModes.map((option) => (
              <MenuItem key={option.id || option.value} value={option.id || option.value}>
                {option.name || option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
    </Grid>
  </Box>
);

const PhysicalPropertiesStep = ({ formData, dropdownOptions, handleInputChange, handleTagsChange }) => (
  <Box sx={{ mt: 2 }}>
    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
      Optional physical characteristics and branding information.
    </Typography>
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>Brand</InputLabel>
          <Select
            value={formData.brand || ''}
            onChange={handleInputChange("brand")}
            label="Brand"
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {dropdownOptions.brands.map((option) => (
              <MenuItem
                key={option.id || option.value}
                value={option.id || option.value}
              >
                {option.name || option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>Manufacturer</InputLabel>
          <Select
            value={formData.manufacturer || ''}
            onChange={handleInputChange("manufacturer")}
            label="Manufacturer"
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {dropdownOptions.manufacturers.map((option) => (
              <MenuItem
                key={option.id || option.value}
                value={option.id || option.value}
              >
                {option.name || option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} md={4}>
        <FormControl fullWidth>
          <InputLabel>Size</InputLabel>
          <Select
            value={formData.size || ''}
            onChange={handleInputChange("size")}
            label="Size"
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {dropdownOptions.sizes.map((option) => (
              <MenuItem
                key={option.id || option.value}
                value={option.id || option.value}
              >
                {option.name || option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} md={4}>
        <FormControl fullWidth>
          <InputLabel>Shape</InputLabel>
          <Select
            value={formData.shape || ''}
            onChange={handleInputChange("shape")}
            label="Shape"
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {dropdownOptions.shapes.map((option) => (
              <MenuItem
                key={option.id || option.value}
                value={option.id || option.value}
              >
                {option.name || option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} md={4}>
        <FormControl fullWidth>
          <InputLabel>Quality</InputLabel>
          <Select
            value={formData.quality || ''}
            onChange={handleInputChange("quality")}
            label="Quality"
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {dropdownOptions.qualities.map((option) => (
              <MenuItem
                key={option.id || option.value}
                value={option.id || option.value}
              >
                {option.name || option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12}>
        <FormControl fullWidth>
          <InputLabel>Tags</InputLabel>
          <Select
            multiple
            value={formData.tags}
            onChange={handleTagsChange}
            label="Tags"
            renderValue={(selected) => (
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                {selected.map((value) => {
                  const tag = dropdownOptions.tags.find(
                    (t) => (t.id || t.value) === value
                  );
                  return (
                    <Chip
                      key={value}
                      label={tag?.name || tag?.label || value}
                      size="small"
                    />
                  );
                })}
              </Box>
            )}
          >
            {dropdownOptions.tags.map((option) => (
              <MenuItem
                key={option.id || option.value}
                value={option.id || option.value}
              >
                {option.name || option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
    </Grid>
  </Box>
);

const FinancialInformationStep = ({ formData, handleInputChange }) => (
  <Box sx={{ mt: 2 }}>
    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
      Financial details including costs and depreciation information.
    </Typography>
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Standard Cost"
          type="number"
          value={formData.standard_cost || ''}
          onChange={handleInputChange('standard_cost')}
          InputProps={{
            startAdornment: <InputAdornment position="start">$</InputAdornment>,
          }}
          inputProps={{ min: 0, step: 0.01 }}
          placeholder="0.00"
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Annual Depreciation Rate"
          type="number"
          value={formData.depreciation_rate_annual || ''}
          onChange={handleInputChange('depreciation_rate_annual')}
          InputProps={{
            endAdornment: <InputAdornment position="end">%</InputAdornment>,
          }}
          inputProps={{ min: 0, max: 100, step: 0.1 }}
          placeholder="0.0"
        />
      </Grid>
    </Grid>
  </Box>
);

const InventoryManagementStep = ({ formData, handleInputChange }) => (
  <Box sx={{ mt: 2 }}>
    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
      Inventory control parameters and stock level management.
    </Typography>
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Minimum Stock Level"
          type="number"
          value={formData.min_stock_level || ''}
          onChange={handleInputChange('min_stock_level')}
          inputProps={{ min: 0 }}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Maximum Stock Level"
          type="number"
          value={formData.max_stock_level || ''}
          onChange={handleInputChange('max_stock_level')}
          inputProps={{ min: 0 }}
          placeholder="Optional"
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Reorder Point"
          type="number"
          value={formData.reorder_point || ''}
          onChange={handleInputChange('reorder_point')}
          inputProps={{ min: 0 }}
          placeholder="Optional"
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Economic Order Quantity"
          type="number"
          value={formData.economic_order_quantity || ''}
          onChange={handleInputChange('economic_order_quantity')}
          inputProps={{ min: 0 }}
          placeholder="Optional"
        />
      </Grid>
    </Grid>
  </Box>
);

const AssetPropertiesStep = ({ formData, handleInputChange }) => (
  <Box sx={{ mt: 2 }}>
    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
      Asset classification and lifecycle management settings.
    </Typography>
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <FormControlLabel
          control={
            <Switch
              checked={formData.is_fixed_asset}
              onChange={handleInputChange("is_fixed_asset")}
              color="primary"
            />
          }
          label="Fixed Asset"
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControlLabel
          control={
            <Switch
              checked={formData.is_serialized}
              onChange={handleInputChange("is_serialized")}
              color="primary"
            />
          }
          label="Serialized Item"
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Warranty Period"
          type="number"
          value={formData.warranty_period_months || ''}
          onChange={handleInputChange('warranty_period_months')}
          InputProps={{
            endAdornment: <InputAdornment position="end">months</InputAdornment>,
          }}
          inputProps={{ min: 0 }}
          placeholder="Optional"
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Expected Life"
          type="number"
          value={formData.expected_life_years || ''}
          onChange={handleInputChange('expected_life_years')}
          InputProps={{
            endAdornment: <InputAdornment position="end">years</InputAdornment>,
          }}
          inputProps={{ min: 0 }}
          placeholder="Optional"
        />
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={formData.is_active}
              onChange={handleInputChange("is_active")}
              color="primary"
            />
          }
          label="Active"
        />
      </Grid>
    </Grid>
  </Box>
);

const ModernItemMasterForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { enqueueSnackbar } = useSnackbar();
  const isEdit = Boolean(id);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    model: "",
    sub_classification: "",
    item_type: "",
    category: "",
    entry_mode: "",
    brand: "",
    manufacturer: "",
    size: "",
    shape: "",
    quality: "",
    unit_of_measure: "",
    standard_cost: "",
    depreciation_rate_annual: "",
    min_stock_level: 0,
    max_stock_level: "",
    reorder_point: "",
    economic_order_quantity: "",
    is_fixed_asset: false,
    is_serialized: false,
    warranty_period_months: "",
    expected_life_years: "",
    is_active: true,
    tags: [],
  });

  // Dropdown options
  const [dropdownOptions, setDropdownOptions] = useState({
    subClassifications: [],
    itemTypes: [],
    categories: [],
    entryModes: [],
    brands: [],
    manufacturers: [],
    sizes: [],
    shapes: [],
    qualities: [],
    unitsOfMeasure: [],
    tags: [],
  });

  // Loading and error states
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);

  // Stepper state
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState(new Set());



  // Define steps for vertical stepper using configuration
  const steps = STEP_CONFIGS.ITEM_MASTER.map((stepConfig) => ({
    label: stepConfig.label,
    icon: getStepIcon(stepConfig.key),
    color: stepConfig.color,
    fields: stepConfig.fields,
  }));

  // Helper function to get step icons
  function getStepIcon(stepKey) {
    const iconMap = {
      basic_info: <InfoIcon />,
      classification: <CategoryIcon />,
      physical_properties: <BuildIcon />,
      financial_info: <MoneyIcon />,
      inventory_management: <TrendingUpIcon />,
      asset_properties: <SettingsIcon />,
    };
    return iconMap[stepKey] || <InfoIcon />;
  }

  useEffect(() => {
    loadDropdownOptions();
    if (isEdit) {
      loadItemMaster();
    }
  }, [id, isEdit]);

  const loadDropdownOptions = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load all dropdown options in parallel
      const [
        subClassificationsRes,
        itemTypesRes,
        categoriesRes,
        entryModesRes,
        brandsRes,
        manufacturersRes,
        sizesRes,
        shapesRes,
        qualitiesRes,
        unitsRes,
        tagsRes,
      ] = await Promise.all([
        api.get(API_ENDPOINTS.SUB_CLASSIFICATIONS),
        api.get(API_ENDPOINTS.ITEM_TYPES),
        api.get(API_ENDPOINTS.ITEM_CATEGORIES),
        api.get(API_ENDPOINTS.ENTRY_MODES),
        api.get(API_ENDPOINTS.ITEM_BRANDS),
        api.get(API_ENDPOINTS.ITEM_MANUFACTURERS),
        api.get(API_ENDPOINTS.ITEM_SIZES),
        api.get(API_ENDPOINTS.ITEM_SHAPES),
        api.get(API_ENDPOINTS.ITEM_QUALITIES),
        api.get(API_ENDPOINTS.UNITS_OF_MEASURE),
        api.get(API_ENDPOINTS.ITEM_TAGS),
      ]);

      setDropdownOptions({
        subClassifications: subClassificationsRes.data || [],
        itemTypes: itemTypesRes.data || [],
        categories: categoriesRes.data || [],
        entryModes: entryModesRes.data || [],
        brands: brandsRes.data || [],
        manufacturers: manufacturersRes.data || [],
        sizes: sizesRes.data || [],
        shapes: shapesRes.data || [],
        qualities: qualitiesRes.data || [],
        unitsOfMeasure: unitsRes.data || [],
        tags: tagsRes.data || [],
      });

      console.log("✅", MESSAGES.SUCCESS.DROPDOWN_LOADED);
    } catch (err) {
      console.error("❌ Error loading dropdown options:", err);
      setError(MESSAGES.ERROR.LOAD_DROPDOWN_FAILED);
      enqueueSnackbar(MESSAGES.ERROR.LOAD_DROPDOWN_FAILED, {
        variant: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadItemMaster = async () => {
    try {
      setLoading(true);
      const response = await api.get(API_ENDPOINTS.ITEM_MASTER_DETAIL(id));
      const item = response.data;

      // Convert the item data to form format
      setFormData({
        name: item.name || "",
        description: item.description || "",
        model: item.model || "",
        sub_classification: item.sub_classification || "",
        item_type: item.item_type || "",
        category: item.category || "",
        entry_mode: item.entry_mode || "",
        brand: item.brand || "",
        manufacturer: item.manufacturer || "",
        size: item.size || "",
        shape: item.shape || "",
        quality: item.quality || "",
        unit_of_measure: item.unit_of_measure || "",
        standard_cost: item.standard_cost || "",
        depreciation_rate_annual: item.depreciation_rate_annual || "",
        min_stock_level: item.min_stock_level || 0,
        max_stock_level: item.max_stock_level || "",
        reorder_point: item.reorder_point || "",
        economic_order_quantity: item.economic_order_quantity || "",
        is_fixed_asset: item.is_fixed_asset || false,
        is_serialized: item.is_serialized || false,
        warranty_period_months: item.warranty_period_months || "",
        expected_life_years: item.expected_life_years || "",
        is_active: item.is_active !== false,
        tags: item.tags || [],
      });

      console.log("✅", MESSAGES.SUCCESS.ITEM_LOADED);
    } catch (err) {
      console.error("❌ Error loading item master:", err);
      setError(MESSAGES.ERROR.LOAD_ITEM_FAILED);
      enqueueSnackbar(MESSAGES.ERROR.LOAD_ITEM_FAILED, { variant: "error" });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = useCallback((field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  const handleTagsChange = useCallback((event) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      tags: typeof value === 'string' ? value.split(',') : value
    }));
  }, []);

  const validateForm = () => {
    const errors = [];

    if (!formData.name.trim()) errors.push(MESSAGES.ERROR.ITEM_NAME_REQUIRED);
    if (!formData.sub_classification)
      errors.push(MESSAGES.ERROR.SUB_CLASSIFICATION_REQUIRED);
    if (!formData.item_type) errors.push(MESSAGES.ERROR.ITEM_TYPE_REQUIRED);
    if (!formData.category) errors.push(MESSAGES.ERROR.CATEGORY_REQUIRED);
    if (!formData.unit_of_measure)
      errors.push(MESSAGES.ERROR.UNIT_OF_MEASURE_REQUIRED);

    if (
      formData.standard_cost &&
      parseFloat(formData.standard_cost) < VALIDATION_RULES.MIN_VALUE
    ) {
      errors.push(MESSAGES.ERROR.STANDARD_COST_NEGATIVE);
    }

    if (
      formData.max_stock_level &&
      formData.min_stock_level > parseInt(formData.max_stock_level)
    ) {
      errors.push(MESSAGES.ERROR.MAX_STOCK_INVALID);
    }

    return errors;
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      validationErrors.forEach((error) =>
        enqueueSnackbar(error, { variant: "error" })
      );
      return;
    }

    setSaving(true);
    try {
      // Prepare data for submission
      const submitData = {
        ...formData,
        // Convert empty strings to null for optional foreign keys
        brand: formData.brand || null,
        manufacturer: formData.manufacturer || null,
        size: formData.size || null,
        shape: formData.shape || null,
        quality: formData.quality || null,
        entry_mode: formData.entry_mode || null,
        // Convert empty strings to null for optional numbers
        max_stock_level: formData.max_stock_level || null,
        reorder_point: formData.reorder_point || null,
        economic_order_quantity: formData.economic_order_quantity || null,
        warranty_period_months: formData.warranty_period_months || null,
        expected_life_years: formData.expected_life_years || null,
        depreciation_rate_annual: formData.depreciation_rate_annual || null,
      };

      let response;
      if (isEdit) {
        response = await api.put(
          API_ENDPOINTS.ITEM_MASTER_DETAIL(id),
          submitData
        );
        enqueueSnackbar(MESSAGES.SUCCESS.ITEM_MASTER_UPDATED, {
          variant: "success",
        });
      } else {
        response = await api.post(API_ENDPOINTS.ITEM_MASTERS, submitData);
        enqueueSnackbar(MESSAGES.SUCCESS.ITEM_MASTER_CREATED, {
          variant: "success",
        });
      }

      console.log("✅ Item master saved successfully:", response.data);
      navigate(ROUTES.ITEM_MASTERS_LIST);
    } catch (err) {
      console.error("❌ Error saving item master:", err);
      const errorMessage =
        err.response?.data?.detail ||
        err.response?.data?.message ||
        MESSAGES.ERROR.SAVE_ITEM_FAILED;
      enqueueSnackbar(errorMessage, { variant: "error" });
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate(ROUTES.ITEM_MASTERS_LIST);
  };

  // Step navigation functions
  const handleNext = () => {
    if (validateCurrentStep()) {
      setCompletedSteps((prev) => new Set([...prev, activeStep]));
      setActiveStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  const handleStepClick = (stepIndex) => {
    // Allow clicking on completed steps or the next step
    if (completedSteps.has(stepIndex) || stepIndex <= activeStep) {
      setActiveStep(stepIndex);
    }
  };

  const validateCurrentStep = () => {
    const currentStepFields = steps[activeStep]?.fields || [];
    const errors = [];

    // Step 0: Basic Information - only name is required
    if (activeStep === 0) {
      if (!formData.name.trim()) {
        errors.push(MESSAGES.ERROR.ITEM_NAME_REQUIRED);
      }
    }

    // Step 1: Classification - required fields
    if (activeStep === 1) {
      if (!formData.sub_classification)
        errors.push(MESSAGES.ERROR.SUB_CLASSIFICATION_REQUIRED);
      if (!formData.item_type) errors.push(MESSAGES.ERROR.ITEM_TYPE_REQUIRED);
      if (!formData.category) errors.push(MESSAGES.ERROR.CATEGORY_REQUIRED);
      if (!formData.unit_of_measure)
        errors.push(MESSAGES.ERROR.UNIT_OF_MEASURE_REQUIRED);
    }

    // Step 3: Financial Information - validate costs
    if (activeStep === 3) {
      if (
        formData.standard_cost &&
        parseFloat(formData.standard_cost) < VALIDATION_RULES.MIN_VALUE
      ) {
        errors.push(MESSAGES.ERROR.STANDARD_COST_NEGATIVE);
      }
      if (
        formData.depreciation_rate_annual &&
        (parseFloat(formData.depreciation_rate_annual) <
          VALIDATION_RULES.MIN_VALUE ||
          parseFloat(formData.depreciation_rate_annual) >
            VALIDATION_RULES.MAX_DEPRECIATION_RATE)
      ) {
        errors.push(MESSAGES.ERROR.DEPRECIATION_RATE_INVALID);
      }
    }

    // Step 4: Inventory Management - validate stock levels
    if (activeStep === 4) {
      if (
        formData.max_stock_level &&
        formData.min_stock_level > parseInt(formData.max_stock_level)
      ) {
        errors.push(MESSAGES.ERROR.MAX_STOCK_INVALID);
      }
    }

    // Show errors if any
    if (errors.length > 0) {
      errors.forEach((error) => enqueueSnackbar(error, { variant: "error" }));
      return false;
    }

    return true;
  };

  const isStepCompleted = (stepIndex) => {
    return completedSteps.has(stepIndex);
  };

  const isStepOptional = (stepIndex) => {
    // Steps 2, 3, 4, 5 are optional
    return stepIndex > 1;
  };

  // Step content components
  const renderStepContent = (stepIndex) => {
    switch (stepIndex) {
      case 0:
        return (
          <BasicInformationStep
            formData={formData}
            handleInputChange={handleInputChange}
          />
        );
      case 1:
        return (
          <ClassificationStep
            formData={formData}
            dropdownOptions={dropdownOptions}
            handleInputChange={handleInputChange}
          />
        );
      case 2:
        return (
          <PhysicalPropertiesStep
            formData={formData}
            dropdownOptions={dropdownOptions}
            handleInputChange={handleInputChange}
            handleTagsChange={handleTagsChange}
          />
        );
      case 3:
        return (
          <FinancialInformationStep
            formData={formData}
            handleInputChange={handleInputChange}
          />
        );
      case 4:
        return (
          <InventoryManagementStep
            formData={formData}
            handleInputChange={handleInputChange}
          />
        );
      case 5:
        return (
          <AssetPropertiesStep
            formData={formData}
            handleInputChange={handleInputChange}
          />
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="400px"
        >
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 3 }}>
            Loading form...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Professional Header */}
      <Fade in={true}>
        <Paper
          elevation={0}
          sx={{
            p: 4,
            mb: 4,
            borderRadius: 3,
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            color: "white",
            position: "relative",
            overflow: "hidden",
          }}
        >
          <Box sx={{ position: "relative", zIndex: 2 }}>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Box display="flex" alignItems="center">
                <Avatar
                  sx={{
                    bgcolor: "rgba(255,255,255,0.2)",
                    color: "white",
                    width: 64,
                    height: 64,
                    mr: 3,
                    backdropFilter: "blur(10px)",
                  }}
                >
                  <InventoryIcon sx={{ fontSize: 32 }} />
                </Avatar>
                <Box>
                  <Typography
                    variant="h3"
                    component="h1"
                    fontWeight="bold"
                    gutterBottom
                  >
                    {isEdit ? "Edit Item Master" : "Create Item Master"}
                  </Typography>
                  <Typography variant="h6" sx={{ opacity: 0.9 }}>
                    {isEdit
                      ? "Update item master information"
                      : "Define a new inventory item with comprehensive details"}
                  </Typography>
                </Box>
              </Box>
              <Box textAlign="center">
                <Typography variant="h4" fontWeight="bold">
                  {activeStep + 1}/{steps.length}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Steps Complete
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={(completedSteps.size / steps.length) * 100}
                  sx={{
                    mt: 1,
                    bgcolor: "rgba(255,255,255,0.3)",
                    "& .MuiLinearProgress-bar": {
                      bgcolor: "white",
                    },
                  }}
                />
              </Box>
            </Box>
          </Box>
          {/* Decorative background elements */}
          <Box
            sx={{
              position: "absolute",
              top: -50,
              right: -50,
              width: 200,
              height: 200,
              borderRadius: "50%",
              bgcolor: "rgba(255,255,255,0.1)",
              zIndex: 1,
            }}
          />
          <Box
            sx={{
              position: "absolute",
              bottom: -30,
              left: -30,
              width: 150,
              height: 150,
              borderRadius: "50%",
              bgcolor: "rgba(255,255,255,0.05)",
              zIndex: 1,
            }}
          />
        </Paper>
      </Fade>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Grid container spacing={4}>
          {/* Vertical Stepper */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, borderRadius: 2, position: "sticky", top: 20 }}>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ mb: 3, fontWeight: "bold" }}
              >
                Progress Overview
              </Typography>
              <Stepper activeStep={activeStep} orientation="vertical">
                {steps.map((step, index) => (
                  <Step
                    key={step.label}
                    completed={isStepCompleted(index)}
                    sx={{ cursor: "pointer" }}
                    onClick={() => handleStepClick(index)}
                  >
                    <StepLabel
                      optional={
                        isStepOptional(index) && (
                          <Typography variant="caption" color="text.secondary">
                            Optional
                          </Typography>
                        )
                      }
                      StepIconComponent={({ active, completed }) => (
                        <Avatar
                          sx={{
                            width: 40,
                            height: 40,
                            bgcolor: completed
                              ? "success.main"
                              : active
                              ? step.color
                              : "grey.300",
                            color: "white",
                            transition: "all 0.3s ease",
                            "&:hover": {
                              transform: "scale(1.1)",
                              boxShadow: 3,
                            },
                          }}
                        >
                          {completed ? <CheckIcon /> : step.icon}
                        </Avatar>
                      )}
                    >
                      <Typography variant="subtitle1" fontWeight="bold">
                        {step.label}
                      </Typography>
                    </StepLabel>
                    <StepContent>
                      <Typography variant="caption" color="text.secondary">
                        Fields: {step.fields.join(", ")}
                      </Typography>
                    </StepContent>
                  </Step>
                ))}
              </Stepper>
            </Paper>
          </Grid>

          {/* Step Content */}
          <Grid item xs={12} md={8}>
            <Fade in={true} key={activeStep}>
              <Paper sx={{ p: 4, borderRadius: 2, minHeight: 500 }}>
                <Box sx={{ mb: 4 }}>
                  <Box display="flex" alignItems="center" sx={{ mb: 2 }}>
                    <Avatar
                      sx={{
                        bgcolor: steps[activeStep].color,
                        mr: 2,
                        width: 48,
                        height: 48,
                      }}
                    >
                      {steps[activeStep].icon}
                    </Avatar>
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {steps[activeStep].label}
                      </Typography>
                    </Box>
                  </Box>
                  <Divider />
                </Box>

                {renderStepContent(activeStep)}

                {/* Navigation Buttons */}
                <Box
                  sx={{
                    mt: 4,
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Button
                    variant="outlined"
                    onClick={handleCancel}
                    startIcon={<CancelIcon />}
                    disabled={saving}
                    size="large"
                  >
                    Cancel
                  </Button>

                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Button
                      variant="outlined"
                      onClick={handleBack}
                      startIcon={<BackIcon />}
                      disabled={activeStep === 0 || saving}
                      size="large"
                    >
                      Back
                    </Button>

                    {activeStep === steps.length - 1 ? (
                      <Button
                        type="submit"
                        variant="contained"
                        startIcon={
                          saving ? <CircularProgress size={20} /> : <SaveIcon />
                        }
                        disabled={saving}
                        size="large"
                        sx={{
                          minWidth: 160,
                          background:
                            "linear-gradient(45deg, #667eea 30%, #764ba2 90%)",
                          "&:hover": {
                            background:
                              "linear-gradient(45deg, #5a6fd8 30%, #6a4190 90%)",
                          },
                        }}
                      >
                        {saving
                          ? "Saving..."
                          : isEdit
                          ? "Update Item"
                          : "Create Item"}
                      </Button>
                    ) : (
                      <Button
                        variant="contained"
                        onClick={handleNext}
                        endIcon={<NextIcon />}
                        disabled={saving}
                        size="large"
                        sx={{
                          minWidth: 120,
                          bgcolor: steps[activeStep].color,
                          "&:hover": {
                            bgcolor: steps[activeStep].color,
                            filter: "brightness(0.9)",
                          },
                        }}
                      >
                        Next
                      </Button>
                    )}
                  </Box>
                </Box>
              </Paper>
            </Fade>
          </Grid>
        </Grid>
      </form>
    </Container>
  );
};

export default ModernItemMasterForm;
