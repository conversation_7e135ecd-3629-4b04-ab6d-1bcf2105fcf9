"""
Entry Mode Model for Inventory Setup
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from .base import TimeStampedModel


class EntryMode(TimeStampedModel):
    """
    Entry Mode model for defining different ways inventory items can be entered into the system.
    Examples: Purchase, Transfer, Production, Donation, etc.
    """
    
    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Entry Mode Name'),
        help_text=_('Name of the inventory entry mode')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description'),
        help_text=_('Detailed description of the entry mode')
    )
    
    code = models.Char<PERSON>ield(
        max_length=10,
        unique=True,
        blank=True,
        verbose_name=_('Entry Mode Code'),
        help_text=_('Short code for the entry mode (auto-generated if not provided)')
    )
    
    is_default = models.BooleanField(
        default=False,
        verbose_name=_('Default Entry Mode'),
        help_text=_('Designates if this is the default entry mode')
    )
    
    requires_approval = models.<PERSON><PERSON>anField(
        default=False,
        verbose_name=_('Requires Approval'),
        help_text=_('Designates if entries using this mode require approval')
    )
    
    allows_negative_stock = models.BooleanField(
        default=False,
        verbose_name=_('Allows Negative Stock'),
        help_text=_('Designates if this entry mode allows negative stock levels')
    )

    class Meta:
        db_table = 'inventory_entry_mode'
        verbose_name = _('Entry Mode')
        verbose_name_plural = _('Entry Modes')
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['code']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_default']),
        ]

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        """Override save to auto-generate code and handle default logic"""
        # Auto-generate code if not provided
        if not self.code:
            # Create code from name (first 10 chars, uppercase, replace spaces with underscores)
            self.code = self.name.upper().replace(' ', '_')[:10]
        
        # Ensure only one default entry mode
        if self.is_default:
            EntryMode.objects.filter(is_default=True).exclude(pk=self.pk).update(is_default=False)
        
        super().save(*args, **kwargs)

    @classmethod
    def get_default(cls):
        """Get the default entry mode"""
        return cls.objects.filter(is_default=True, is_active=True).first()

    @property
    def usage_count(self):
        """Get count of how many times this entry mode has been used"""
        # This would be implemented when inventory entry models are created
        # For now, return 0
        return 0
