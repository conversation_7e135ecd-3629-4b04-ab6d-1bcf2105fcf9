"""
Unit of Measure Model for Inventory Setup
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from .base import TimeStampedModel


class UnitOfMeasure(TimeStampedModel):
    """
    Unit of Measure model for defining measurement units for inventory items.
    Examples: Piece (pcs), Kilogram (kg), Meter (m), Liter (L), etc.
    """
    
    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Unit Name'),
        help_text=_('Full name of the unit of measure')
    )
    
    symbol = models.Char<PERSON>ield(
        max_length=10,
        unique=True,
        verbose_name=_('Symbol'),
        help_text=_('Short symbol for the unit (e.g., kg, m, L)')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description'),
        help_text=_('Detailed description of the unit of measure')
    )

    class Meta:
        db_table = 'inventory_unit_of_measure'
        verbose_name = _('Unit of Measure')
        verbose_name_plural = _('Units of Measure')
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['symbol']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.name} ({self.symbol})"

    def clean(self):
        """Custom validation"""
        super().clean()
        if self.name:
            self.name = self.name.strip()
        if self.symbol:
            self.symbol = self.symbol.strip()

    def save(self, *args, **kwargs):
        """Override save to clean data"""
        if self.name:
            self.name = self.name.strip()
        if self.symbol:
            self.symbol = self.symbol.strip()
        super().save(*args, **kwargs)

    @property
    def usage_count(self):
        """Get count of how many items use this unit"""
        # This would be implemented when item models are created
        # For now, return 0
        return 0

    @property
    def display_name(self):
        """Get display name with symbol"""
        return f"{self.name} ({self.symbol})"
