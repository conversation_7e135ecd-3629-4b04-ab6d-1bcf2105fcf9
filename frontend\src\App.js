import React, { useEffect } from 'react';
import './styles/print.css';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { colors, typography, shadows, gradients } from './theme/designSystem';
import { SnackbarProvider } from 'notistack';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { initializeErrorHandling } from './utils/errorHandler';
import ErrorBoundary from './components/ErrorBoundary';

// Organization components
import OrganizationList from './features/organizations/OrganizationList';
import OrganizationDetail from './features/organizations/OrganizationDetail';
import OrganizationTypeList from './features/organizations/OrganizationTypeList';
import OfficeList from './features/organizations/OfficeList';
import { GatesList } from './features/organization';
import OrganizationMenu from './features/organization/OrganizationMenu';



// Supplier components
import SuppliersMenu from './features/suppliers/SuppliersMenu';
import SuppliersPage from './features/suppliers/SuppliersPage';
import SupplierTypesList from './features/suppliers/SupplierTypesList';

// Model 19 components
import { Model19Menu, Model19FormsList, Model19FormDetail, Model19FormCreate } from './features/model19';

// Inventory Setup components
import InventorySetupDashboard from './features/inventorySetup/InventorySetupDashboard';
import InventorySetupMenu from './features/inventorySetup/InventorySetupMenu';
import ItemStatusPage from './features/inventorySetup/ItemStatusPage';
import PropertyStatusPage from './features/inventorySetup/PropertyStatusPage';
import ApprovalStatusPage from './features/inventorySetup/ApprovalStatusPage';
import ItemTagPage from './features/inventorySetup/ItemTagPage';
import MainClassificationPage from './features/inventorySetup/MainClassificationPage';
import SubClassificationPage from './features/inventorySetup/SubClassificationPage';
import EntryModePage from './features/inventorySetup/EntryModePage';
import ItemTypePage from './features/inventorySetup/ItemTypePage';
import ItemCategoryPage from './features/inventorySetup/ItemCategoryPage';
import ItemBrandPage from './features/inventorySetup/ItemBrandPage';
import UnitOfMeasurePage from './features/inventorySetup/UnitOfMeasurePage';
import ItemSizePage from './features/inventorySetup/ItemSizePage';
import ItemQualityPage from './features/inventorySetup/ItemQualityPage';
import {
  ItemManufacturerPage,
  ItemShapePage
} from './features/inventorySetup/GenericInventoryPage';

// Dashboard component
import Dashboard from './features/dashboard/Dashboard';
import MainDashboardMenu from './features/dashboard/MainDashboardMenu';

// Storage components
import StorageMenu from './features/storage/StorageMenu';
import StoragePage from './features/storage/StoragePage';
import StorageManagementDashboard from './features/storage/StorageManagementDashboard';
import StoreTypesList from './features/storage/StoreTypesList';
import StoresList from './features/storage/StoresList';
import ShelvesList from './features/storage/ShelvesList';

// Analytics components
import AnalyticsMenu from './features/analytics/AnalyticsMenu';

// Help components
import HelpMenu from './features/help/HelpMenu';



// Items components
import ItemsDashboard from './features/items/ItemsDashboard';
import ItemMastersList from './features/items/ItemMastersList';
import StandardItemMastersList from './features/items/StandardItemMastersList';
import StandardBatchItemsList from './features/items/StandardBatchItemsList';
import ItemManagementMenu from './features/items/ItemManagementMenu';
import ItemMasterDetail from './features/items/ItemMasterDetail';
import ProfessionalItemMasterForm from './features/items/ProfessionalItemMasterForm';
import ModernItemMasterForm from './features/items/ModernItemMasterForm';
import BatchItemsList from './features/items/BatchItemsList';
import BatchItemForm from './features/items/BatchItemForm';
import BatchItemDetail from './features/items/BatchItemDetail';
import InventoryItemForm from './features/items/InventoryItemForm';
import InventoryItemsList from './features/items/InventoryItemsList';
import InventoryItemDetail from './features/items/InventoryItemDetail';
import DebugInventoryAPI from './features/items/DebugInventoryAPI';
import EndToEndTest from './features/items/EndToEndTest';
import MaintenanceSchedule from './features/items/MaintenanceSchedule';
import SerialVouchersList from './features/items/SerialVouchersList';
import SerialVoucherForm from './features/items/SerialVoucherForm';
import ItemManagementDashboard from './features/items/ItemManagementDashboard';
import APITest from './features/debug/APITest';

// Auth and Layout
import Login from './features/auth/Login';
import Layout from './components/Layout';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: colors.primary[500],
      light: colors.primary[400],
      dark: colors.primary[600],
      contrastText: '#ffffff',
      50: colors.primary[50],
      100: colors.primary[100],
      200: colors.primary[200],
      300: colors.primary[300],
      400: colors.primary[400],
      500: colors.primary[500],
      600: colors.primary[600],
      700: colors.primary[700],
      800: colors.primary[800],
      900: colors.primary[900],
    },
    secondary: {
      main: colors.secondary[500],
      light: colors.secondary[400],
      dark: colors.secondary[600],
      contrastText: '#ffffff',
      50: colors.secondary[50],
      100: colors.secondary[100],
      200: colors.secondary[200],
      300: colors.secondary[300],
      400: colors.secondary[400],
      500: colors.secondary[500],
      600: colors.secondary[600],
      700: colors.secondary[700],
      800: colors.secondary[800],
      900: colors.secondary[900],
    },
    success: {
      main: colors.success[500],
      light: colors.success[400],
      dark: colors.success[600],
      50: colors.success[50],
      100: colors.success[100],
      200: colors.success[200],
      300: colors.success[300],
      400: colors.success[400],
      500: colors.success[500],
      600: colors.success[600],
      700: colors.success[700],
      800: colors.success[800],
      900: colors.success[900],
    },
    error: {
      main: colors.error[500],
      light: colors.error[400],
      dark: colors.error[600],
      50: colors.error[50],
      100: colors.error[100],
      200: colors.error[200],
      300: colors.error[300],
      400: colors.error[400],
      500: colors.error[500],
      600: colors.error[600],
      700: colors.error[700],
      800: colors.error[800],
      900: colors.error[900],
    },
    warning: {
      main: colors.warning[500],
      light: colors.warning[400],
      dark: colors.warning[600],
      50: colors.warning[50],
      100: colors.warning[100],
      200: colors.warning[200],
      300: colors.warning[300],
      400: colors.warning[400],
      500: colors.warning[500],
      600: colors.warning[600],
      700: colors.warning[700],
      800: colors.warning[800],
      900: colors.warning[900],
    },
    info: {
      main: colors.primary[500],
      light: colors.primary[400],
      dark: colors.primary[600],
    },
    background: {
      default: colors.gray[50],
      paper: '#ffffff',
    },
    text: {
      primary: colors.slate[800],
      secondary: colors.slate[600],
    },
    divider: colors.gray[200],
    grey: colors.gray,
  },
  typography: {
    fontFamily: typography.fontFamily.sans.join(', '),
    h1: {
      fontWeight: typography.fontWeight.extrabold,
      letterSpacing: '-0.025em',
      fontSize: typography.fontSize['5xl'],
      lineHeight: typography.lineHeight.tight,
    },
    h2: {
      fontWeight: typography.fontWeight.bold,
      letterSpacing: '-0.025em',
      fontSize: typography.fontSize['4xl'],
      lineHeight: typography.lineHeight.tight,
    },
    h3: {
      fontWeight: typography.fontWeight.bold,
      letterSpacing: '-0.025em',
      fontSize: typography.fontSize['3xl'],
      lineHeight: typography.lineHeight.snug,
    },
    h4: {
      fontWeight: typography.fontWeight.bold,
      fontSize: typography.fontSize['2xl'],
      lineHeight: typography.lineHeight.snug,
    },
    h5: {
      fontWeight: typography.fontWeight.semibold,
      fontSize: typography.fontSize.xl,
      lineHeight: typography.lineHeight.snug,
    },
    h6: {
      fontWeight: typography.fontWeight.semibold,
      fontSize: typography.fontSize.lg,
      lineHeight: typography.lineHeight.normal,
    },
    subtitle1: {
      fontWeight: typography.fontWeight.medium,
      fontSize: typography.fontSize.base,
      lineHeight: typography.lineHeight.normal,
    },
    subtitle2: {
      fontWeight: typography.fontWeight.medium,
      fontSize: typography.fontSize.sm,
      lineHeight: typography.lineHeight.normal,
    },
    body1: {
      fontSize: typography.fontSize.base,
      lineHeight: typography.lineHeight.relaxed,
    },
    body2: {
      fontSize: typography.fontSize.sm,
      lineHeight: typography.lineHeight.normal,
    },
    button: {
      textTransform: 'none',
      fontWeight: typography.fontWeight.semibold,
      letterSpacing: '0.025em',
    },
    caption: {
      fontSize: typography.fontSize.xs,
      lineHeight: typography.lineHeight.normal,
      fontWeight: typography.fontWeight.medium,
    },
  },
  shape: {
    borderRadius: 20,
  },
  shadows: [
    'none',
    shadows.sm,
    shadows.base,
    shadows.md,
    shadows.lg,
    shadows.xl,
    shadows['2xl'],
    shadows.glow,
    ...Array(17).fill('none'),
  ],
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          padding: '12px 24px',
          boxShadow: 'none',
          fontWeight: typography.fontWeight.semibold,
          letterSpacing: '0.025em',
          textTransform: 'none',
          position: 'relative',
          overflow: 'hidden',
          '&:before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: '-100%',
            width: '100%',
            height: '100%',
            background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
            transition: 'left 0.5s',
          },
          '&:hover:before': {
            left: '100%',
          },
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: shadows.lg,
          },
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        },
        contained: {
          boxShadow: shadows.md,
          '&:hover': {
            boxShadow: shadows.xl,
          },
        },
        containedPrimary: {
          background: gradients.primary,
          '&:hover': {
            background: `linear-gradient(135deg, ${colors.primary[600]} 0%, ${colors.primary[700]} 50%, ${colors.primary[800]} 100%)`,
          },
        },
        containedSecondary: {
          background: gradients.secondary,
          '&:hover': {
            background: `linear-gradient(135deg, ${colors.secondary[600]} 0%, ${colors.secondary[700]} 50%, ${colors.secondary[800]} 100%)`,
          },
        },
        outlined: {
          borderWidth: '2px',
          borderColor: colors.gray[300],
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(10px)',
          '&:hover': {
            borderWidth: '2px',
            borderColor: colors.primary[500],
            backgroundColor: colors.primary[50],
            transform: 'translateY(-2px)',
            boxShadow: shadows.md,
          },
        },
        text: {
          '&:hover': {
            backgroundColor: colors.gray[100],
            transform: 'translateY(-1px)',
          },
        },
        sizeLarge: {
          padding: '16px 32px',
          fontSize: typography.fontSize.lg,
          borderRadius: 20,
        },
        sizeSmall: {
          padding: '8px 16px',
          fontSize: typography.fontSize.sm,
          borderRadius: 12,
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 24,
          backgroundImage: 'none',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          border: `1px solid ${colors.gray[200]}`,
        },
        elevation1: {
          boxShadow: shadows.sm,
        },
        elevation2: {
          boxShadow: shadows.base,
        },
        elevation3: {
          boxShadow: shadows.md,
        },
        elevation4: {
          boxShadow: shadows.lg,
        },
        elevation8: {
          boxShadow: shadows.xl,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 24,
          overflow: 'hidden',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          border: `1px solid ${colors.gray[200]}`,
          boxShadow: shadows.md,
          position: 'relative',
          '&:before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '1px',
            background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent)',
          },
          '&:hover': {
            transform: 'translateY(-4px) scale(1.02)',
            boxShadow: shadows.xl,
            border: `1px solid ${colors.primary[200]}`,
          },
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: 24,
          '&:last-child': {
            paddingBottom: 24,
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 12,
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderWidth: '2px',
              borderColor: '#6366f1',
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#6366f1',
            },
          },
          '& .MuiInputLabel-root.Mui-focused': {
            color: '#6366f1',
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 6,
          fontWeight: 500,
          '&.MuiChip-filled': {
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
          },
        },
        filledPrimary: {
          background: 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)',
        },
        filledSecondary: {
          background: 'linear-gradient(135deg, #ec4899 0%, #db2777 100%)',
        },
      },
    },
    MuiListItem: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          '&.Mui-selected': {
            backgroundColor: 'rgba(99, 102, 241, 0.08)',
          },
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          '&.Mui-selected': {
            backgroundColor: 'rgba(99, 102, 241, 0.08)',
            '&:hover': {
              backgroundColor: 'rgba(99, 102, 241, 0.12)',
            },
          },
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          },
        },
      },
    },
    MuiAvatar: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1)',
          backgroundImage: 'none',
        },
        colorDefault: {
          backgroundColor: '#ffffff',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          borderRight: '1px solid rgba(0, 0, 0, 0.05)',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: 'rgba(0, 0, 0, 0.02)',
          '& .MuiTableCell-root': {
            fontWeight: 600,
          },
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:hover': {
            backgroundColor: '#f1f5f9',
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: '1px solid rgba(0, 0, 0, 0.05)',
        },
        head: {
          fontWeight: 600,
          backgroundColor: '#f8fafc',
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderRadius: 8,
          padding: '8px 12px',
          fontSize: '0.75rem',
        },
      },
    },
    // MuiTableCell, MuiTableRow, and MuiChip are already defined above
  },
});

const PrivateRoute = ({ children }) => {
  const token = localStorage.getItem('token');
  return token ? children : <Navigate to="/login" />;
};

function App() {
  // Initialize error handling on app start
  useEffect(() => {
    initializeErrorHandling();
  }, []);

  return (
    <ErrorBoundary>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Box sx={{ width: '100%', maxWidth: '100vw', overflow: 'hidden' }}>
          <SnackbarProvider
            maxSnack={5}
            autoHideDuration={5000}
            preventDuplicate
            dense
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
          >
            <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
            <Routes>
              <Route path="/login" element={<Login />} />

              {/* Organization Routes */}
              <Route
                path="/organizations"
                element={
                  <PrivateRoute>
                    <Layout>
                      <OrganizationList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/organizations/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <OrganizationDetail />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/organization-types"
                element={
                  <PrivateRoute>
                    <Layout>
                      <OrganizationTypeList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/offices"
                element={
                  <PrivateRoute>
                    <Layout>
                      <OfficeList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/gates"
                element={
                  <PrivateRoute>
                    <Layout>
                      <GatesList />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Professional Dashboard Menu Routes */}
              <Route
                path="/organization-menu"
                element={
                  <PrivateRoute>
                    <Layout>
                      <OrganizationMenu />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup-menu"
                element={
                  <PrivateRoute>
                    <Layout>
                      <InventorySetupMenu />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/item-management-menu"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemManagementDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />

              <Route
                path="/suppliers-menu"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SuppliersMenu />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/suppliers/:type"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SuppliersPage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/storage-menu"
                element={
                  <PrivateRoute>
                    <Layout>
                      <StorageManagementDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/storage/:type"
                element={
                  <PrivateRoute>
                    <Layout>
                      <StoragePage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/analytics-menu"
                element={
                  <PrivateRoute>
                    <Layout>
                      <AnalyticsMenu />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/help-menu"
                element={
                  <PrivateRoute>
                    <Layout>
                      <HelpMenu />
                    </Layout>
                  </PrivateRoute>
                }
              />




              {/* Supplier Routes */}
              <Route
                path="/suppliers"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SuppliersList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/suppliers-dashboard"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SupplierDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Supplier Types Routes */}
              <Route
                path="/supplier-types"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SupplierTypesList />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Supplier Categories Routes */}
              <Route
                path="/supplier-categories"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SupplierCategoriesList />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Model 19 Routes */}
              <Route
                path="/model19-menu"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19Menu />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/model19/forms"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19FormsList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/model19/forms/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19FormDetail />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/model19/create"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19FormCreate />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/model19/generate"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19FormCreate />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Inventory Setup Routes */}
              <Route
                path="/inventory-setup"
                element={
                  <PrivateRoute>
                    <Layout>
                      <InventorySetupDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />

              <Route
                path="/inventory-setup/item-statuses"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemStatusPage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/property-statuses"
                element={
                  <PrivateRoute>
                    <Layout>
                      <PropertyStatusPage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/approval-statuses"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ApprovalStatusPage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/item-tags"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemTagPage />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Additional Inventory Setup Routes */}
              <Route
                path="/inventory-setup/main-classifications"
                element={
                  <PrivateRoute>
                    <Layout>
                      <MainClassificationPage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/sub-classifications"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SubClassificationPage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/entry-modes"
                element={
                  <PrivateRoute>
                    <Layout>
                      <EntryModePage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/item-types"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemTypePage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/item-categories"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemCategoryPage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/item-brands"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemBrandPage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/manufacturers"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemManufacturerPage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/item-qualities"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemQualityPage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/item-shapes"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemShapePage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/item-sizes"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemSizePage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-setup/units-of-measure"
                element={
                  <PrivateRoute>
                    <Layout>
                      <UnitOfMeasurePage />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Items Management Routes */}
              <Route
                path="/items"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemsDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/masters"
                element={
                  <PrivateRoute>
                    <Layout>
                      <StandardItemMastersList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/masters/new"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ModernItemMasterForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/masters/new-professional"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ProfessionalItemMasterForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/masters/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemMasterDetail />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/masters/:id/edit"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ModernItemMasterForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/batches"
                element={
                  <PrivateRoute>
                    <Layout>
                      <StandardBatchItemsList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/batches/new"
                element={
                  <PrivateRoute>
                    <Layout>
                      <BatchItemForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/batches/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <BatchItemDetail />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/batches/:id/edit"
                element={
                  <PrivateRoute>
                    <Layout>
                      <BatchItemForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/inventory"
                element={
                  <PrivateRoute>
                    <Layout>
                      <InventoryItemsList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/inventory/new"
                element={
                  <PrivateRoute>
                    <Layout>
                      <InventoryItemForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/inventory/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <InventoryItemDetail />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/inventory/:id/edit"
                element={
                  <PrivateRoute>
                    <Layout>
                      <InventoryItemForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/debug/api"
                element={
                  <PrivateRoute>
                    <Layout>
                      <DebugInventoryAPI />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/test/end-to-end"
                element={
                  <PrivateRoute>
                    <Layout>
                      <EndToEndTest />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/maintenance"
                element={
                  <PrivateRoute>
                    <Layout>
                      <MaintenanceSchedule />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/serial-vouchers"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SerialVouchersList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/serial-vouchers/new"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SerialVoucherForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/serial-vouchers/:id/edit"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SerialVoucherForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/debug/api-test"
                element={
                  <PrivateRoute>
                    <Layout>
                      <APITest />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Dashboard Routes */}
              <Route
                path="/dashboard"
                element={
                  <PrivateRoute>
                    <Layout>
                      <MainDashboardMenu />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/dashboard/legacy"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Dashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />

              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="*" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </Router>
        </LocalizationProvider>
      </SnackbarProvider>
      </Box>
    </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;