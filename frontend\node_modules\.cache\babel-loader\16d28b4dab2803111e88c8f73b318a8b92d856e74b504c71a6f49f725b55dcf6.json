{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects v2\\\\asset management\\\\frontend\\\\src\\\\features\\\\analytics\\\\AnalyticsMenu.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { Container, Typography, Grid, Card, CardContent, Box, Paper, Chip, useTheme, alpha, Breadcrumbs, Link, Alert } from '@mui/material';\nimport { Analytics as AnalyticsIcon, Assessment as ReportsIcon, TrendingUp as TrendsIcon, Dashboard as DashboardIcon, Insights as InsightsIcon, Home as HomeIcon, BarChart as ChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon } from '@mui/icons-material';\nimport { Link as RouterLink } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnalyticsMenu = () => {\n  _s();\n  const theme = useTheme();\n\n  // Analytics management cards data\n  const analyticsCards = [{\n    id: 'inventory-reports',\n    title: 'Inventory Reports',\n    description: 'Generate comprehensive inventory and stock reports',\n    icon: /*#__PURE__*/_jsxDEV(ReportsIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.primary.main,\n    path: '/analytics/inventory-reports',\n    count: '12+',\n    adminOnly: false\n  }, {\n    id: 'financial-analytics',\n    title: 'Financial Analytics',\n    description: 'Analyze costs, valuations, and financial metrics',\n    icon: /*#__PURE__*/_jsxDEV(TrendsIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.secondary.main,\n    path: '/analytics/financial',\n    count: '8+',\n    adminOnly: false\n  }, {\n    id: 'performance-dashboard',\n    title: 'Performance Dashboard',\n    description: 'Real-time performance metrics and KPIs',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.info.main,\n    path: '/analytics/performance',\n    count: 'Live',\n    adminOnly: false\n  }, {\n    id: 'trend-analysis',\n    title: 'Trend Analysis',\n    description: 'Historical trends and predictive analytics',\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.success.main,\n    path: '/analytics/trends',\n    count: 'New',\n    adminOnly: false\n  }, {\n    id: 'custom-reports',\n    title: 'Custom Reports',\n    description: 'Build and schedule custom reports',\n    icon: /*#__PURE__*/_jsxDEV(ChartIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.warning.main,\n    path: '/analytics/custom-reports',\n    count: '5+',\n    adminOnly: false\n  }, {\n    id: 'data-visualization',\n    title: 'Data Visualization',\n    description: 'Interactive charts and visual analytics',\n    icon: /*#__PURE__*/_jsxDEV(PieChartIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 13\n    }, this),\n    color: theme.palette.error.main,\n    path: '/analytics/visualization',\n    count: 'Beta',\n    adminOnly: false\n  }];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Breadcrumbs, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        component: RouterLink,\n        to: \"/dashboard\",\n        color: \"inherit\",\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(HomeIcon, {\n          sx: {\n            mr: 0.5\n          },\n          fontSize: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), \"Dashboard\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        color: \"text.primary\",\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n          sx: {\n            mr: 0.5\n          },\n          fontSize: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), \"Analytics & Reports\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h3\",\n        component: \"h1\",\n        gutterBottom: true,\n        fontWeight: \"bold\",\n        children: \"Analytics & Reports\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"Generate comprehensive reports and analytics across all modules\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 4,\n        display: 'flex',\n        alignItems: 'center',\n        bgcolor: alpha(theme.palette.error.main, 0.1),\n        border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`,\n        borderRadius: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(InsightsIcon, {\n        color: \"error\",\n        sx: {\n          mr: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"error.main\",\n          children: \"Analytics & Reporting Center\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Access comprehensive analytics, generate reports, and gain insights from your inventory data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\uD83D\\uDEA7 Analytics Platform Under Development\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: \"Our comprehensive analytics and reporting platform is currently under development. It will provide powerful insights, customizable reports, and real-time dashboards for all aspects of your inventory management system.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: analyticsCards.map((card, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            transition: 'transform 0.3s, box-shadow 0.3s',\n            '&:hover': {\n              transform: 'translateY(-8px)',\n              boxShadow: '0 12px 20px rgba(0,0,0,0.1)'\n            },\n            borderTop: `4px solid ${card.color}`,\n            borderRadius: 2,\n            position: 'relative',\n            overflow: 'visible',\n            cursor: 'not-allowed',\n            opacity: 0.7\n          },\n          children: [card.count && /*#__PURE__*/_jsxDEV(Chip, {\n            label: card.count,\n            color: \"primary\",\n            size: \"small\",\n            sx: {\n              position: 'absolute',\n              top: -10,\n              right: 16,\n              fontWeight: 'bold',\n              boxShadow: '0 2px 5px rgba(0,0,0,0.2)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1,\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                width: 80,\n                height: 80,\n                borderRadius: '50%',\n                bgcolor: alpha(card.color, 0.1),\n                mb: 2,\n                mx: 'auto'\n              },\n              children: card.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"h2\",\n              gutterBottom: true,\n              textAlign: \"center\",\n              fontWeight: \"bold\",\n              children: card.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              textAlign: \"center\",\n              children: card.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"warning.main\",\n              textAlign: \"center\",\n              display: \"block\",\n              sx: {\n                mt: 1\n              },\n              children: \"Coming Soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 4,\n        borderRadius: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Upcoming Features\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"\\uD83D\\uDCCA Real-time Dashboards\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Live KPI monitoring and alerts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"\\uD83D\\uDCC8 Predictive Analytics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"AI-powered forecasting and insights\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"\\uD83D\\uDCCB Custom Reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Drag-and-drop report builder\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"\\uD83D\\uDD04 Automated Scheduling\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Scheduled report generation and delivery\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n_s(AnalyticsMenu, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c = AnalyticsMenu;\nexport default AnalyticsMenu;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsMenu\");", "map": {"version": 3, "names": ["useState", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Box", "Paper", "Chip", "useTheme", "alpha", "Breadcrumbs", "Link", "<PERSON><PERSON>", "Analytics", "AnalyticsIcon", "Assessment", "ReportsIcon", "TrendingUp", "TrendsIcon", "Dashboard", "DashboardIcon", "Insights", "InsightsIcon", "Home", "HomeIcon", "<PERSON><PERSON><PERSON>", "ChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "RouterLink", "jsxDEV", "_jsxDEV", "AnalyticsMenu", "_s", "theme", "analyticsCards", "id", "title", "description", "icon", "sx", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "palette", "primary", "main", "path", "count", "adminOnly", "secondary", "info", "success", "warning", "error", "max<PERSON><PERSON><PERSON>", "py", "children", "mb", "component", "to", "display", "alignItems", "mr", "variant", "gutterBottom", "fontWeight", "p", "bgcolor", "border", "borderRadius", "severity", "container", "spacing", "map", "card", "index", "item", "xs", "sm", "md", "height", "flexDirection", "transition", "transform", "boxShadow", "borderTop", "position", "overflow", "cursor", "opacity", "label", "size", "top", "right", "flexGrow", "justifyContent", "width", "mx", "textAlign", "mt", "_c", "$RefreshReg$"], "sources": ["D:/Projects v2/asset management/frontend/src/features/analytics/AnalyticsMenu.js"], "sourcesContent": ["import { useState } from 'react';\nimport {\n  Container,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  Box,\n  Paper,\n  Chip,\n  useTheme,\n  alpha,\n  Breadcrumbs,\n  Link,\n  Alert\n} from '@mui/material';\nimport {\n  Analytics as AnalyticsIcon,\n  Assessment as ReportsIcon,\n  TrendingUp as TrendsIcon,\n  Dashboard as DashboardIcon,\n  Insights as InsightsIcon,\n  Home as HomeIcon,\n  BarChart as ChartIcon,\n  <PERSON>Chart as PieChartIcon,\n  Timeline as TimelineIcon\n} from '@mui/icons-material';\nimport { Link as RouterLink } from 'react-router-dom';\n\nconst AnalyticsMenu = () => {\n  const theme = useTheme();\n\n  // Analytics management cards data\n  const analyticsCards = [\n    {\n      id: 'inventory-reports',\n      title: 'Inventory Reports',\n      description: 'Generate comprehensive inventory and stock reports',\n      icon: <ReportsIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.primary.main,\n      path: '/analytics/inventory-reports',\n      count: '12+',\n      adminOnly: false\n    },\n    {\n      id: 'financial-analytics',\n      title: 'Financial Analytics',\n      description: 'Analyze costs, valuations, and financial metrics',\n      icon: <TrendsIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.secondary.main,\n      path: '/analytics/financial',\n      count: '8+',\n      adminOnly: false\n    },\n    {\n      id: 'performance-dashboard',\n      title: 'Performance Dashboard',\n      description: 'Real-time performance metrics and KPIs',\n      icon: <DashboardIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.info.main,\n      path: '/analytics/performance',\n      count: 'Live',\n      adminOnly: false\n    },\n    {\n      id: 'trend-analysis',\n      title: 'Trend Analysis',\n      description: 'Historical trends and predictive analytics',\n      icon: <TimelineIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.success.main,\n      path: '/analytics/trends',\n      count: 'New',\n      adminOnly: false\n    },\n    {\n      id: 'custom-reports',\n      title: 'Custom Reports',\n      description: 'Build and schedule custom reports',\n      icon: <ChartIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.warning.main,\n      path: '/analytics/custom-reports',\n      count: '5+',\n      adminOnly: false\n    },\n    {\n      id: 'data-visualization',\n      title: 'Data Visualization',\n      description: 'Interactive charts and visual analytics',\n      icon: <PieChartIcon sx={{ fontSize: 40 }} />,\n      color: theme.palette.error.main,\n      path: '/analytics/visualization',\n      count: 'Beta',\n      adminOnly: false\n    }\n  ];\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n      {/* Breadcrumbs */}\n      <Breadcrumbs sx={{ mb: 3 }}>\n        <Link\n          component={RouterLink}\n          to=\"/dashboard\"\n          color=\"inherit\"\n          sx={{ display: 'flex', alignItems: 'center' }}\n        >\n          <HomeIcon sx={{ mr: 0.5 }} fontSize=\"inherit\" />\n          Dashboard\n        </Link>\n        <Typography color=\"text.primary\" sx={{ display: 'flex', alignItems: 'center' }}>\n          <AnalyticsIcon sx={{ mr: 0.5 }} fontSize=\"inherit\" />\n          Analytics & Reports\n        </Typography>\n      </Breadcrumbs>\n\n      {/* Header */}\n      <Box sx={{ mb: 4 }}>\n        <Typography variant=\"h3\" component=\"h1\" gutterBottom fontWeight=\"bold\">\n          Analytics & Reports\n        </Typography>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Generate comprehensive reports and analytics across all modules\n        </Typography>\n      </Box>\n\n      {/* Info Paper */}\n      <Paper\n        sx={{\n          p: 2,\n          mb: 4,\n          display: 'flex',\n          alignItems: 'center',\n          bgcolor: alpha(theme.palette.error.main, 0.1),\n          border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`,\n          borderRadius: 2\n        }}\n      >\n        <InsightsIcon color=\"error\" sx={{ mr: 2 }} />\n        <Box>\n          <Typography variant=\"h6\" color=\"error.main\">\n            Analytics & Reporting Center\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Access comprehensive analytics, generate reports, and gain insights from your inventory data\n          </Typography>\n        </Box>\n      </Paper>\n\n      {/* Under Development Notice */}\n      <Alert severity=\"info\" sx={{ mb: 4 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          🚧 Analytics Platform Under Development\n        </Typography>\n        <Typography variant=\"body2\">\n          Our comprehensive analytics and reporting platform is currently under development. \n          It will provide powerful insights, customizable reports, and real-time dashboards \n          for all aspects of your inventory management system.\n        </Typography>\n      </Alert>\n\n      {/* Analytics Management Cards */}\n      <Grid container spacing={3}>\n        {analyticsCards.map((card, index) => (\n          <Grid item xs={12} sm={6} md={4} key={index}>\n            <Card\n              sx={{\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                transition: 'transform 0.3s, box-shadow 0.3s',\n                '&:hover': {\n                  transform: 'translateY(-8px)',\n                  boxShadow: '0 12px 20px rgba(0,0,0,0.1)',\n                },\n                borderTop: `4px solid ${card.color}`,\n                borderRadius: 2,\n                position: 'relative',\n                overflow: 'visible',\n                cursor: 'not-allowed',\n                opacity: 0.7\n              }}\n            >\n              {card.count && (\n                <Chip\n                  label={card.count}\n                  color=\"primary\"\n                  size=\"small\"\n                  sx={{\n                    position: 'absolute',\n                    top: -10,\n                    right: 16,\n                    fontWeight: 'bold',\n                    boxShadow: '0 2px 5px rgba(0,0,0,0.2)'\n                  }}\n                />\n              )}\n              <CardContent sx={{ flexGrow: 1, p: 3 }}>\n                <Box\n                  sx={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    width: 80,\n                    height: 80,\n                    borderRadius: '50%',\n                    bgcolor: alpha(card.color, 0.1),\n                    mb: 2,\n                    mx: 'auto'\n                  }}\n                >\n                  {card.icon}\n                </Box>\n                <Typography variant=\"h6\" component=\"h2\" gutterBottom textAlign=\"center\" fontWeight=\"bold\">\n                  {card.title}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\" textAlign=\"center\">\n                  {card.description}\n                </Typography>\n                <Typography variant=\"caption\" color=\"warning.main\" textAlign=\"center\" display=\"block\" sx={{ mt: 1 }}>\n                  Coming Soon\n                </Typography>\n              </CardContent>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* Features Preview */}\n      <Paper sx={{ p: 3, mt: 4, borderRadius: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Upcoming Features\n        </Typography>\n        <Grid container spacing={2}>\n          <Grid item xs={12} sm={6} md={3}>\n            <Typography variant=\"subtitle2\" gutterBottom>\n              📊 Real-time Dashboards\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Live KPI monitoring and alerts\n            </Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Typography variant=\"subtitle2\" gutterBottom>\n              📈 Predictive Analytics\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              AI-powered forecasting and insights\n            </Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Typography variant=\"subtitle2\" gutterBottom>\n              📋 Custom Reports\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Drag-and-drop report builder\n            </Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Typography variant=\"subtitle2\" gutterBottom>\n              🔄 Automated Scheduling\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Scheduled report generation and delivery\n            </Typography>\n          </Grid>\n        </Grid>\n      </Paper>\n    </Container>\n  );\n};\n\nexport default AnalyticsMenu;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SACEC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,IAAI,EACJC,KAAK,QACA,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,WAAW,EACzBC,UAAU,IAAIC,UAAU,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,SAAS,EACrBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASnB,IAAI,IAAIoB,UAAU,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,KAAK,GAAG5B,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAM6B,cAAc,GAAG,CACrB;IACEC,EAAE,EAAE,mBAAmB;IACvBC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,oDAAoD;IACjEC,IAAI,eAAER,OAAA,CAACjB,WAAW;MAAC0B,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3CC,KAAK,EAAEZ,KAAK,CAACa,OAAO,CAACC,OAAO,CAACC,IAAI;IACjCC,IAAI,EAAE,8BAA8B;IACpCC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE;EACb,CAAC,EACD;IACEhB,EAAE,EAAE,qBAAqB;IACzBC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,kDAAkD;IAC/DC,IAAI,eAAER,OAAA,CAACf,UAAU;MAACwB,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1CC,KAAK,EAAEZ,KAAK,CAACa,OAAO,CAACM,SAAS,CAACJ,IAAI;IACnCC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE;EACb,CAAC,EACD;IACEhB,EAAE,EAAE,uBAAuB;IAC3BC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,wCAAwC;IACrDC,IAAI,eAAER,OAAA,CAACb,aAAa;MAACsB,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7CC,KAAK,EAAEZ,KAAK,CAACa,OAAO,CAACO,IAAI,CAACL,IAAI;IAC9BC,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE;EACb,CAAC,EACD;IACEhB,EAAE,EAAE,gBAAgB;IACpBC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,4CAA4C;IACzDC,IAAI,eAAER,OAAA,CAACH,YAAY;MAACY,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5CC,KAAK,EAAEZ,KAAK,CAACa,OAAO,CAACQ,OAAO,CAACN,IAAI;IACjCC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE;EACb,CAAC,EACD;IACEhB,EAAE,EAAE,gBAAgB;IACpBC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,mCAAmC;IAChDC,IAAI,eAAER,OAAA,CAACP,SAAS;MAACgB,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzCC,KAAK,EAAEZ,KAAK,CAACa,OAAO,CAACS,OAAO,CAACP,IAAI;IACjCC,IAAI,EAAE,2BAA2B;IACjCC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE;EACb,CAAC,EACD;IACEhB,EAAE,EAAE,oBAAoB;IACxBC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,yCAAyC;IACtDC,IAAI,eAAER,OAAA,CAACL,YAAY;MAACc,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5CC,KAAK,EAAEZ,KAAK,CAACa,OAAO,CAACU,KAAK,CAACR,IAAI;IAC/BC,IAAI,EAAE,0BAA0B;IAChCC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE;EACb,CAAC,CACF;EAED,oBACErB,OAAA,CAACjC,SAAS;IAAC4D,QAAQ,EAAC,IAAI;IAAClB,EAAE,EAAE;MAAEmB,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAErC7B,OAAA,CAACvB,WAAW;MAACgC,EAAE,EAAE;QAAEqB,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACzB7B,OAAA,CAACtB,IAAI;QACHqD,SAAS,EAAEjC,UAAW;QACtBkC,EAAE,EAAC,YAAY;QACfjB,KAAK,EAAC,SAAS;QACfN,EAAE,EAAE;UAAEwB,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAL,QAAA,gBAE9C7B,OAAA,CAACT,QAAQ;UAACkB,EAAE,EAAE;YAAE0B,EAAE,EAAE;UAAI,CAAE;UAACzB,QAAQ,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAElD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPd,OAAA,CAAChC,UAAU;QAAC+C,KAAK,EAAC,cAAc;QAACN,EAAE,EAAE;UAAEwB,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAL,QAAA,gBAC7E7B,OAAA,CAACnB,aAAa;UAAC4B,EAAE,EAAE;YAAE0B,EAAE,EAAE;UAAI,CAAE;UAACzB,QAAQ,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,uBAEvD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGdd,OAAA,CAAC5B,GAAG;MAACqC,EAAE,EAAE;QAAEqB,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACjB7B,OAAA,CAAChC,UAAU;QAACoE,OAAO,EAAC,IAAI;QAACL,SAAS,EAAC,IAAI;QAACM,YAAY;QAACC,UAAU,EAAC,MAAM;QAAAT,QAAA,EAAC;MAEvE;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbd,OAAA,CAAChC,UAAU;QAACoE,OAAO,EAAC,IAAI;QAACrB,KAAK,EAAC,gBAAgB;QAAAc,QAAA,EAAC;MAEhD;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNd,OAAA,CAAC3B,KAAK;MACJoC,EAAE,EAAE;QACF8B,CAAC,EAAE,CAAC;QACJT,EAAE,EAAE,CAAC;QACLG,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBM,OAAO,EAAEhE,KAAK,CAAC2B,KAAK,CAACa,OAAO,CAACU,KAAK,CAACR,IAAI,EAAE,GAAG,CAAC;QAC7CuB,MAAM,EAAE,aAAajE,KAAK,CAAC2B,KAAK,CAACa,OAAO,CAACU,KAAK,CAACR,IAAI,EAAE,GAAG,CAAC,EAAE;QAC3DwB,YAAY,EAAE;MAChB,CAAE;MAAAb,QAAA,gBAEF7B,OAAA,CAACX,YAAY;QAAC0B,KAAK,EAAC,OAAO;QAACN,EAAE,EAAE;UAAE0B,EAAE,EAAE;QAAE;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7Cd,OAAA,CAAC5B,GAAG;QAAAyD,QAAA,gBACF7B,OAAA,CAAChC,UAAU;UAACoE,OAAO,EAAC,IAAI;UAACrB,KAAK,EAAC,YAAY;UAAAc,QAAA,EAAC;QAE5C;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbd,OAAA,CAAChC,UAAU;UAACoE,OAAO,EAAC,OAAO;UAACrB,KAAK,EAAC,gBAAgB;UAAAc,QAAA,EAAC;QAEnD;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRd,OAAA,CAACrB,KAAK;MAACgE,QAAQ,EAAC,MAAM;MAAClC,EAAE,EAAE;QAAEqB,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACnC7B,OAAA,CAAChC,UAAU;QAACoE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAR,QAAA,EAAC;MAEtC;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbd,OAAA,CAAChC,UAAU;QAACoE,OAAO,EAAC,OAAO;QAAAP,QAAA,EAAC;MAI5B;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGRd,OAAA,CAAC/B,IAAI;MAAC2E,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAhB,QAAA,EACxBzB,cAAc,CAAC0C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC9BhD,OAAA,CAAC/B,IAAI;QAACgF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAvB,QAAA,eAC9B7B,OAAA,CAAC9B,IAAI;UACHuC,EAAE,EAAE;YACF4C,MAAM,EAAE,MAAM;YACdpB,OAAO,EAAE,MAAM;YACfqB,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE,iCAAiC;YAC7C,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb,CAAC;YACDC,SAAS,EAAE,aAAaX,IAAI,CAAChC,KAAK,EAAE;YACpC2B,YAAY,EAAE,CAAC;YACfiB,QAAQ,EAAE,UAAU;YACpBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE,aAAa;YACrBC,OAAO,EAAE;UACX,CAAE;UAAAjC,QAAA,GAEDkB,IAAI,CAAC3B,KAAK,iBACTpB,OAAA,CAAC1B,IAAI;YACHyF,KAAK,EAAEhB,IAAI,CAAC3B,KAAM;YAClBL,KAAK,EAAC,SAAS;YACfiD,IAAI,EAAC,OAAO;YACZvD,EAAE,EAAE;cACFkD,QAAQ,EAAE,UAAU;cACpBM,GAAG,EAAE,CAAC,EAAE;cACRC,KAAK,EAAE,EAAE;cACT5B,UAAU,EAAE,MAAM;cAClBmB,SAAS,EAAE;YACb;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF,eACDd,OAAA,CAAC7B,WAAW;YAACsC,EAAE,EAAE;cAAE0D,QAAQ,EAAE,CAAC;cAAE5B,CAAC,EAAE;YAAE,CAAE;YAAAV,QAAA,gBACrC7B,OAAA,CAAC5B,GAAG;cACFqC,EAAE,EAAE;gBACFwB,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBkC,cAAc,EAAE,QAAQ;gBACxBC,KAAK,EAAE,EAAE;gBACThB,MAAM,EAAE,EAAE;gBACVX,YAAY,EAAE,KAAK;gBACnBF,OAAO,EAAEhE,KAAK,CAACuE,IAAI,CAAChC,KAAK,EAAE,GAAG,CAAC;gBAC/Be,EAAE,EAAE,CAAC;gBACLwC,EAAE,EAAE;cACN,CAAE;cAAAzC,QAAA,EAEDkB,IAAI,CAACvC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACNd,OAAA,CAAChC,UAAU;cAACoE,OAAO,EAAC,IAAI;cAACL,SAAS,EAAC,IAAI;cAACM,YAAY;cAACkC,SAAS,EAAC,QAAQ;cAACjC,UAAU,EAAC,MAAM;cAAAT,QAAA,EACtFkB,IAAI,CAACzC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACbd,OAAA,CAAChC,UAAU;cAACoE,OAAO,EAAC,OAAO;cAACrB,KAAK,EAAC,gBAAgB;cAACwD,SAAS,EAAC,QAAQ;cAAA1C,QAAA,EAClEkB,IAAI,CAACxC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACbd,OAAA,CAAChC,UAAU;cAACoE,OAAO,EAAC,SAAS;cAACrB,KAAK,EAAC,cAAc;cAACwD,SAAS,EAAC,QAAQ;cAACtC,OAAO,EAAC,OAAO;cAACxB,EAAE,EAAE;gBAAE+D,EAAE,EAAE;cAAE,CAAE;cAAA3C,QAAA,EAAC;YAErG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GA3D6BkC,KAAK;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4DrC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPd,OAAA,CAAC3B,KAAK;MAACoC,EAAE,EAAE;QAAE8B,CAAC,EAAE,CAAC;QAAEiC,EAAE,EAAE,CAAC;QAAE9B,YAAY,EAAE;MAAE,CAAE;MAAAb,QAAA,gBAC1C7B,OAAA,CAAChC,UAAU;QAACoE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAR,QAAA,EAAC;MAEtC;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbd,OAAA,CAAC/B,IAAI;QAAC2E,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAhB,QAAA,gBACzB7B,OAAA,CAAC/B,IAAI;UAACgF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAvB,QAAA,gBAC9B7B,OAAA,CAAChC,UAAU;YAACoE,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAR,QAAA,EAAC;UAE7C;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbd,OAAA,CAAChC,UAAU;YAACoE,OAAO,EAAC,OAAO;YAACrB,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAEnD;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPd,OAAA,CAAC/B,IAAI;UAACgF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAvB,QAAA,gBAC9B7B,OAAA,CAAChC,UAAU;YAACoE,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAR,QAAA,EAAC;UAE7C;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbd,OAAA,CAAChC,UAAU;YAACoE,OAAO,EAAC,OAAO;YAACrB,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAEnD;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPd,OAAA,CAAC/B,IAAI;UAACgF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAvB,QAAA,gBAC9B7B,OAAA,CAAChC,UAAU;YAACoE,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAR,QAAA,EAAC;UAE7C;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbd,OAAA,CAAChC,UAAU;YAACoE,OAAO,EAAC,OAAO;YAACrB,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAEnD;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPd,OAAA,CAAC/B,IAAI;UAACgF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAvB,QAAA,gBAC9B7B,OAAA,CAAChC,UAAU;YAACoE,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAR,QAAA,EAAC;UAE7C;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbd,OAAA,CAAChC,UAAU;YAACoE,OAAO,EAAC,OAAO;YAACrB,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAEnD;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACZ,EAAA,CAhPID,aAAa;EAAA,QACH1B,QAAQ;AAAA;AAAAkG,EAAA,GADlBxE,aAAa;AAkPnB,eAAeA,aAAa;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}