"""
Main Classification Model for Inventory Setup
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
from .base import TimeStampedModel


class MainClassification(TimeStampedModel):
    """
    Main Classification model for categorizing inventory items at the highest level.
    Uses a 4-character code system for identification.
    """
    
    code = models.CharField(
        max_length=4,
        unique=True,
        verbose_name=_('Classification Code'),
        help_text=_('4-character unique code for the main classification'),
        validators=[
            RegexValidator(
                regex='^[A-Z0-9]{4}$',
                message=_('Code must be exactly 4 characters (letters and numbers only)')
            )
        ]
    )
    
    name = models.CharField(
        max_length=100,
        verbose_name=_('Classification Name'),
        help_text=_('Descriptive name for the main classification')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description'),
        help_text=_('Detailed description of the main classification')
    )

    color = models.Char<PERSON>ield(
        max_length=7,
        default='#2196F3',
        verbose_name=_('Color'),
        help_text=_('Color code for visual identification (hex format)'),
        validators=[
            RegexValidator(
                regex='^#[0-9A-Fa-f]{6}$',
                message=_('Color must be a valid hex color code (e.g., #FF5722)')
            )
        ]
    )

    class Meta:
        db_table = 'inventory_main_classification'
        verbose_name = _('Main Classification')
        verbose_name_plural = _('Main Classifications')
        ordering = ['code']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    def clean(self):
        """Custom validation"""
        super().clean()
        if self.code:
            self.code = self.code.upper()

    def save(self, *args, **kwargs):
        """Override save to ensure code is uppercase"""
        if self.code:
            self.code = self.code.upper()
        super().save(*args, **kwargs)

    @property
    def usage_count(self):
        """Get count of active sub-classifications"""
        return self.subclassification_set.filter(is_active=True).count()
