#!/usr/bin/env python
"""
Script to create test data for serial vouchers and store management
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from items.models import SerialVoucherCategory, SerialVoucher
from organization.models import StoreType, Store, Organization, OrganizationType, <PERSON><PERSON>

def create_test_data():
    print("Creating test data for serial vouchers and stores...")
    
    # Create Serial Voucher Categories
    print("Creating serial voucher categories...")
    categories = [
        {
            'title': 'Inventory Items',
            'report_title': 'Inventory Item Vouchers',
            'description': 'Serial vouchers for inventory item tracking'
        },
        {
            'title': 'Batch Items',
            'report_title': 'Batch Item Vouchers', 
            'description': 'Serial vouchers for batch item tracking'
        },
        {
            'title': 'Fixed Assets',
            'report_title': 'Fixed Asset Vouchers',
            'description': 'Serial vouchers for fixed asset tracking'
        }
    ]
    
    for cat_data in categories:
        category, created = SerialVoucherCategory.objects.get_or_create(
            title=cat_data['title'],
            defaults=cat_data
        )
        if created:
            print(f"Created category: {category.title}")
        else:
            print(f"Category already exists: {category.title}")
    
    # Create Serial Vouchers
    print("Creating serial vouchers...")
    vouchers = [
        {
            'category': SerialVoucherCategory.objects.get(title='Inventory Items'),
            'prefix': 'INV',
            'current_number': 1,
            'number_length': 6,
            'max_number': 999999,
            'is_active': True
        },
        {
            'category': SerialVoucherCategory.objects.get(title='Batch Items'),
            'prefix': 'BATCH',
            'current_number': 1,
            'number_length': 5,
            'max_number': 99999,
            'is_active': True
        },
        {
            'category': SerialVoucherCategory.objects.get(title='Fixed Assets'),
            'prefix': 'ASSET',
            'current_number': 1,
            'number_length': 4,
            'max_number': None,
            'is_active': True
        }
    ]
    
    for voucher_data in vouchers:
        voucher, created = SerialVoucher.objects.get_or_create(
            category=voucher_data['category'],
            prefix=voucher_data['prefix'],
            defaults=voucher_data
        )
        if created:
            print(f"Created voucher: {voucher.category.title} - {voucher.prefix}")
        else:
            print(f"Voucher already exists: {voucher.category.title} - {voucher.prefix}")
    
    # Create Organization Type
    print("Creating organization types...")
    org_type, created = OrganizationType.objects.get_or_create(
        name='University',
        defaults={'description': 'Educational institution'}
    )
    if created:
        print(f"Created organization type: {org_type.name}")
    
    # Create Organization
    print("Creating organizations...")
    # Check if there's already a main organization
    main_org = Organization.objects.filter(is_main=True).first()
    if main_org:
        organization = main_org
        print(f"Using existing main organization: {organization.name}")
    else:
        organization, created = Organization.objects.get_or_create(
            name='Test University',
            defaults={
                'organization_type': org_type,
                'shortcode': 'TU',
                'email': '<EMAIL>',
                'phone': '******-0123',
                'address': '123 University Ave',
                'city': 'Test City',
                'country': 'Test Country',
                'tin_number': '123456789',  # Required field
                'is_active': True,
                'is_main': True
            }
        )
        if created:
            print(f"Created organization: {organization.name}")
        else:
            print(f"Organization already exists: {organization.name}")
    
    # Create Store Types
    print("Creating store types...")
    store_types = [
        {
            'name': 'Warehouse',
            'description': 'Main storage warehouse'
        },
        {
            'name': 'Office Storage',
            'description': 'Office storage room'
        },
        {
            'name': 'Laboratory',
            'description': 'Laboratory equipment storage'
        }
    ]
    
    for store_type_data in store_types:
        store_type, created = StoreType.objects.get_or_create(
            name=store_type_data['name'],
            defaults=store_type_data
        )
        if created:
            print(f"Created store type: {store_type.name}")
        else:
            print(f"Store type already exists: {store_type.name}")
    
    # Create Stores
    print("Creating stores...")
    stores = [
        {
            'name': 'Main Warehouse',
            'code': 'WH001',
            'store_type': StoreType.objects.get(name='Warehouse'),
            'organization': organization,
            'location': 'Building A, Ground Floor',
            'manager_name': 'John Smith',
            'phone': '******-0124',
            'email': '<EMAIL>',
            'is_active': True
        },
        {
            'name': 'IT Storage',
            'code': 'IT001',
            'store_type': StoreType.objects.get(name='Office Storage'),
            'organization': organization,
            'location': 'IT Department, 2nd Floor',
            'manager_name': 'Jane Doe',
            'phone': '******-0125',
            'email': '<EMAIL>',
            'is_active': True
        },
        {
            'name': 'Chemistry Lab Storage',
            'code': 'LAB001',
            'store_type': StoreType.objects.get(name='Laboratory'),
            'organization': organization,
            'location': 'Science Building, Room 101',
            'manager_name': 'Dr. Brown',
            'phone': '******-0126',
            'email': '<EMAIL>',
            'is_active': True
        }
    ]
    
    for store_data in stores:
        store, created = Store.objects.get_or_create(
            code=store_data['code'],
            defaults=store_data
        )
        if created:
            print(f"Created store: {store.name} ({store.code})")
        else:
            print(f"Store already exists: {store.name} ({store.code})")
    
    # Create Shelves
    print("Creating shelves...")
    shelves_data = [
        # Main Warehouse shelves
        {
            'store': Store.objects.get(code='WH001'),
            'code': 'A1',
            'row': 1,
            'column': 1,
            'description': 'Electronics storage section',
            'capacity': 50,
            'is_active': True
        },
        {
            'store': Store.objects.get(code='WH001'),
            'code': 'A2',
            'row': 1,
            'column': 2,
            'description': 'Office supplies section',
            'capacity': 30,
            'is_active': True
        },
        {
            'store': Store.objects.get(code='WH001'),
            'code': 'B1',
            'row': 2,
            'column': 1,
            'description': 'Heavy equipment storage',
            'capacity': 20,
            'is_active': True
        },
        # IT Storage shelves
        {
            'store': Store.objects.get(code='IT001'),
            'code': 'IT-A',
            'row': 1,
            'column': 1,
            'description': 'Computer hardware',
            'capacity': 25,
            'is_active': True
        },
        {
            'store': Store.objects.get(code='IT001'),
            'code': 'IT-B',
            'row': 1,
            'column': 2,
            'description': 'Network equipment',
            'capacity': 15,
            'is_active': True
        },
        # Chemistry Lab shelves
        {
            'store': Store.objects.get(code='LAB001'),
            'code': 'LAB-1',
            'row': 1,
            'column': 1,
            'description': 'Chemical storage',
            'capacity': 40,
            'is_active': True
        },
        {
            'store': Store.objects.get(code='LAB001'),
            'code': 'LAB-2',
            'row': 1,
            'column': 2,
            'description': 'Lab equipment',
            'capacity': 35,
            'is_active': True
        }
    ]

    for shelf_data in shelves_data:
        shelf, created = Shelf.objects.get_or_create(
            store=shelf_data['store'],
            code=shelf_data['code'],
            defaults=shelf_data
        )
        if created:
            print(f"Created shelf: {shelf.store.code}-{shelf.code} (R{shelf.row}C{shelf.column})")
        else:
            print(f"Shelf already exists: {shelf.store.code}-{shelf.code}")

    print("\nTest data creation completed!")
    print(f"Serial Voucher Categories: {SerialVoucherCategory.objects.count()}")
    print(f"Serial Vouchers: {SerialVoucher.objects.count()}")
    print(f"Store Types: {StoreType.objects.count()}")
    print(f"Stores: {Store.objects.count()}")
    print(f"Shelves: {Shelf.objects.count()}")
    print(f"Organizations: {Organization.objects.count()}")

if __name__ == '__main__':
    create_test_data()
