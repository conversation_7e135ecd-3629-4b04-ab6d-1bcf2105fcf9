"""
Item Tag Serializers
"""
from rest_framework import serializers
from ..models import ItemTag


class ItemTagSerializer(serializers.ModelSerializer):
    """Serializer for ItemTag model"""
    
    usage_count = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    display_style = serializers.SerializerMethodField()
    css_class = serializers.SerializerMethodField()
    
    class Meta:
        model = ItemTag
        fields = [
            'id',
            'name',
            'slug',
            'tag_type',
            'description',
            'color_code',
            'is_active',
            'is_system_tag',
            'display_name',
            'display_style',
            'css_class',
            'usage_count',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'slug', 'created_at', 'updated_at']

    def get_display_style(self, obj):
        """Get display style for the tag"""
        return obj.get_display_style()

    def get_css_class(self, obj):
        """Get CSS class for the tag"""
        return obj.get_css_class()

    def validate_name(self, value):
        """Validate name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
        return value


class ItemTagListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing item tags"""
    
    usage_count = serializers.ReadOnlyField()
    display_style = serializers.SerializerMethodField()
    
    class Meta:
        model = ItemTag
        fields = [
            'id',
            'name',
            'slug',
            'tag_type',
            'color_code',
            'is_active',
            'is_system_tag',
            'display_style',
            'usage_count'
        ]

    def get_display_style(self, obj):
        """Get display style for the tag"""
        return {
            'color': obj.color_code or '#607D8B',
            'background': f"{obj.color_code or '#607D8B'}20",
            'border': obj.color_code or '#607D8B'
        }


class ItemTagDropdownSerializer(serializers.ModelSerializer):
    """Minimal serializer for dropdown/select options"""
    
    label = serializers.CharField(source='name')
    value = serializers.CharField(source='id')
    display_style = serializers.SerializerMethodField()
    
    class Meta:
        model = ItemTag
        fields = ['value', 'label', 'color_code', 'display_style']
    
    def get_display_style(self, obj):
        """Get display style for the tag"""
        return {
            'color': obj.color_code or '#607D8B',
            'background': f"{obj.color_code or '#607D8B'}20",
            'border': obj.color_code or '#607D8B'
        }


class ItemTagCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating item tags"""
    
    class Meta:
        model = ItemTag
        fields = [
            'name',
            'tag_type',
            'description',
            'color_code',
            'is_active'
        ]

    def validate_name(self, value):
        """Validate and format name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
        return value
