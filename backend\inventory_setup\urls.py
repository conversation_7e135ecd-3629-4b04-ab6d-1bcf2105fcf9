"""
Inventory Setup URLs
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    MainClassificationViewSet,
    SubClassificationViewSet,
    EntryModeViewSet,
    ItemTypeViewSet,
    ItemCategoryViewSet,
    ItemBrandViewSet,
    ItemManufacturerViewSet,
    ItemQualityViewSet,
    ItemShapeViewSet,
    ItemSizeViewSet,
    UnitOfMeasureViewSet,
    ItemStatusViewSet,
    PropertyStatusViewSet,
    ApprovalStatusViewSet,
    ItemTagViewSet
)

# Create router and register viewsets
router = DefaultRouter()
router.register(r'main-classifications', MainClassificationViewSet, basename='main-classification')
router.register(r'sub-classifications', SubClassificationViewSet, basename='sub-classification')
router.register(r'entry-modes', EntryModeViewSet, basename='entry-mode')
router.register(r'item-types', ItemTypeViewSet, basename='item-type')
router.register(r'item-categories', ItemCategoryViewSet, basename='item-category')
router.register(r'item-brands', ItemBrandViewSet, basename='item-brand')
router.register(r'item-manufacturers', ItemManufacturerViewSet, basename='item-manufacturer')
router.register(r'item-qualities', ItemQualityViewSet, basename='item-quality')
router.register(r'item-shapes', ItemShapeViewSet, basename='item-shape')
router.register(r'item-sizes', ItemSizeViewSet, basename='item-size')
router.register(r'units-of-measure', UnitOfMeasureViewSet, basename='unit-of-measure')
router.register(r'item-statuses', ItemStatusViewSet, basename='item-status')
router.register(r'property-statuses', PropertyStatusViewSet, basename='property-status')
router.register(r'approval-statuses', ApprovalStatusViewSet, basename='approval-status')
router.register(r'item-tags', ItemTagViewSet, basename='item-tag')

app_name = 'inventory_setup'

urlpatterns = [
    path('', include(router.urls)),
]
