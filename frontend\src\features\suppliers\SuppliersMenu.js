/**
 * Suppliers Management Menu Page
 * Professional card-based navigation matching Organization Menu design
 */

import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  Paper,
  useTheme,
  alpha,
  Chip,
} from '@mui/material';
import {
  Business as BusinessIcon,
  Category as CategoryIcon,
  Insights as InsightsIcon,
  LocalShipping,
} from '@mui/icons-material';

const SuppliersMenu = () => {
  const theme = useTheme();

  // Suppliers management cards data - only backend-supported features
  const supplierCards = [
    {
      title: 'Supplier Types',
      description: 'Define and manage supplier business types',
      icon: <CategoryIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.success.main,
      path: '/suppliers/supplier-types',
      count: '8+',
      adminOnly: false
    },
    {
      title: 'Supplier Categories',
      description: 'Organize suppliers by product categories',
      icon: <CategoryIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.info.main,
      path: '/suppliers/supplier-categories',
      count: '12+',
      adminOnly: false
    },
    {
      title: 'Supplier Directory',
      description: 'Manage supplier profiles and contact information',
      icon: <BusinessIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.primary.main,
      path: '/suppliers/suppliers',
      count: '45+',
      adminOnly: false
    }
  ];

  // Filter cards based on user role (for now showing all)
  const filteredCards = supplierCards.filter(card => !card.adminOnly || true);

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center' }}>
        <LocalShipping color="info" sx={{ fontSize: 40, mr: 2 }} />
        <Typography variant="h4" component="h1" gutterBottom>
          Suppliers Management
        </Typography>
      </Box>

      {/* Info Paper */}
      <Paper
        sx={{
          p: 2,
          mb: 4,
          display: 'flex',
          alignItems: 'center',
          bgcolor: alpha(theme.palette.info.main, 0.1),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
          borderRadius: 2
        }}
      >
        <InsightsIcon color="info" sx={{ mr: 2 }} />
        <Box>
          <Typography variant="h6" color="info.main">
            Supplier Management Center
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Complete supplier relationship management system for vendor profiles, purchase orders, performance tracking, and supplier analytics
          </Typography>
        </Box>
      </Paper>

      {/* Suppliers Management Cards */}
      <Grid container spacing={3}>
        {filteredCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card
              component={RouterLink}
              to={card.path}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0,0,0,0.1)',
                },
                textDecoration: 'none',
                borderTop: `4px solid ${card.color}`,
                borderRadius: 2,
                position: 'relative',
                overflow: 'visible'
              }}
            >
              {card.count && (
                <Chip
                  label={card.count}
                  color="primary"
                  size="small"
                  sx={{
                    position: 'absolute',
                    top: -10,
                    right: 16,
                    fontWeight: 'bold',
                    boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
                  }}
                />
              )}
              <Box
                sx={{
                  p: 2,
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: alpha(card.color, 0.1),
                }}
              >
                <Box
                  sx={{
                    mr: 2,
                    p: 1.5,
                    borderRadius: '50%',
                    bgcolor: alpha(card.color, 0.2),
                    color: card.color,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {card.icon}
                </Box>
                <Typography variant="h6" component="div" color="text.primary">
                  {card.title}
                </Typography>
              </Box>
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  {card.description}
                </Typography>
              </CardContent>
              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  size="small"
                  sx={{
                    color: card.color,
                    '&:hover': {
                      bgcolor: alpha(card.color, 0.1)
                    }
                  }}
                >
                  Manage
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
};

export default SuppliersMenu;
