[{"D:\\Projects v2\\asset management\\frontend\\src\\index.js": "1", "D:\\Projects v2\\asset management\\frontend\\src\\reportWebVitals.js": "2", "D:\\Projects v2\\asset management\\frontend\\src\\App.js": "3", "D:\\Projects v2\\asset management\\frontend\\src\\theme\\designSystem.js": "4", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandler.js": "5", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ErrorBoundary.js": "6", "D:\\Projects v2\\asset management\\frontend\\src\\components\\Layout.js": "7", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeList.js": "8", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\OrganizationMenu.js": "9", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationList.js": "10", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeList.js": "11", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDetail.js": "12", "D:\\Projects v2\\asset management\\frontend\\src\\features\\suppliers\\SuppliersMenu.js": "13", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ApprovalStatusPage.js": "14", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupDashboard.js": "15", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierTypesList.js": "16", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemStatusPage.js": "17", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupMenu.js": "18", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTagPage.js": "19", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\PropertyStatusPage.js": "20", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTypePage.js": "21", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\EntryModePage.js": "22", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\MainClassificationPage.js": "23", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemBrandPage.js": "24", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemCategoryPage.js": "25", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\UnitOfMeasurePage.js": "26", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemQualityPage.js": "27", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemSizePage.js": "28", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\SubClassificationPage.js": "29", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\GenericInventoryPage.js": "30", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemsDashboard.js": "31", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMastersList.js": "32", "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\MainDashboardMenu.js": "33", "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\Dashboard.js": "34", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardItemMastersList.js": "35", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardBatchItemsList.js": "36", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMasterDetail.js": "37", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemManagementMenu.js": "38", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ProfessionalItemMasterForm.js": "39", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemForm.js": "40", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemsList.js": "41", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemDetail.js": "42", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ModernItemMasterForm.js": "43", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemForm.js": "44", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemsList.js": "45", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVouchersList.js": "46", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\EndToEndTest.js": "47", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\DebugInventoryAPI.js": "48", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemDetail.js": "49", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\MaintenanceSchedule.js": "50", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\index.js": "51", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\index.js": "52", "D:\\Projects v2\\asset management\\frontend\\src\\features\\debug\\APITest.js": "53", "D:\\Projects v2\\asset management\\frontend\\src\\features\\auth\\Login.js": "54", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\index.js": "55", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\axios.js": "56", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandling.js": "57", "D:\\Projects v2\\asset management\\frontend\\src\\theme\\professionalTheme.js": "58", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeDialog.js": "59", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeDialog.js": "60", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDialog.js": "61", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\permissions.js": "62", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\StatusManagement.js": "63", "D:\\Projects v2\\asset management\\frontend\\src\\components\\DashboardBanner.js": "64", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\GatesList.js": "65", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SuppliersList.js": "66", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDetailView.js": "67", "D:\\Projects v2\\asset management\\frontend\\src\\services\\auth.js": "68", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernSidebar.js": "69", "D:\\Projects v2\\asset management\\frontend\\src\\services\\organizations.js": "70", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernBreadcrumbs.js": "71", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernCardGrid.js": "72", "D:\\Projects v2\\asset management\\frontend\\src\\services\\suppliers.js": "73", "D:\\Projects v2\\asset management\\frontend\\src\\services\\inventorySetup.js": "74", "D:\\Projects v2\\asset management\\frontend\\src\\services\\items.js": "75", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDashboard.js": "76", "D:\\Projects v2\\asset management\\frontend\\src\\services\\supplier.js": "77", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierCategoriesList.js": "78", "D:\\Projects v2\\asset management\\frontend\\src\\config\\itemManagementDesign.js": "79", "D:\\Projects v2\\asset management\\frontend\\src\\config\\formConfig.js": "80", "D:\\Projects v2\\asset management\\frontend\\src\\config\\constants.js": "81", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormTemplate.js": "82", "D:\\Projects v2\\asset management\\frontend\\src\\services\\model19.js": "83", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormsList.js": "84", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19Menu.js": "85", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormDetail.js": "86", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormCreate.js": "87", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\filters.js": "88", "D:\\Projects v2\\asset management\\frontend\\src\\components\\forms\\FormValidation.js": "89", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\validation.js": "90", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StorageMenu.js": "91", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoragePage.js": "92", "D:\\Projects v2\\asset management\\frontend\\src\\features\\analytics\\AnalyticsMenu.js": "93", "D:\\Projects v2\\asset management\\frontend\\src\\features\\help\\HelpMenu.js": "94", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVoucherForm.js": "95", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoresList.js": "96", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoreTypesList.js": "97", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\ShelvesList.js": "98"}, {"size": 618, "mtime": 1749121146000, "results": "99", "hashOfConfig": "100"}, {"size": 362, "mtime": 1744151842000, "results": "101", "hashOfConfig": "100"}, {"size": 36734, "mtime": 1751051613762, "results": "102", "hashOfConfig": "100"}, {"size": 7358, "mtime": 1749460684000, "results": "103", "hashOfConfig": "100"}, {"size": 14601, "mtime": 1750941396982, "results": "104", "hashOfConfig": "100"}, {"size": 7808, "mtime": 1750503868000, "results": "105", "hashOfConfig": "100"}, {"size": 18491, "mtime": 1750062608000, "results": "106", "hashOfConfig": "100"}, {"size": 7248, "mtime": 1750100376000, "results": "107", "hashOfConfig": "100"}, {"size": 6567, "mtime": 1750588146000, "results": "108", "hashOfConfig": "100"}, {"size": 7649, "mtime": 1750100362000, "results": "109", "hashOfConfig": "100"}, {"size": 7291, "mtime": 1750100348000, "results": "110", "hashOfConfig": "100"}, {"size": 20275, "mtime": 1750497690000, "results": "111", "hashOfConfig": "100"}, {"size": 5553, "mtime": 1750587110000, "results": "112", "hashOfConfig": "100"}, {"size": 1543, "mtime": 1749321316000, "results": "113", "hashOfConfig": "100"}, {"size": 17426, "mtime": 1750482586000, "results": "114", "hashOfConfig": "100"}, {"size": 9822, "mtime": 1750587406000, "results": "115", "hashOfConfig": "100"}, {"size": 1446, "mtime": 1749321274000, "results": "116", "hashOfConfig": "100"}, {"size": 14437, "mtime": 1750573144000, "results": "117", "hashOfConfig": "100"}, {"size": 1939, "mtime": 1749321336000, "results": "118", "hashOfConfig": "100"}, {"size": 1456, "mtime": 1749321296000, "results": "119", "hashOfConfig": "100"}, {"size": 12481, "mtime": 1750495760000, "results": "120", "hashOfConfig": "100"}, {"size": 14492, "mtime": 1750485988000, "results": "121", "hashOfConfig": "100"}, {"size": 16270, "mtime": 1750495734000, "results": "122", "hashOfConfig": "100"}, {"size": 13843, "mtime": 1750495814000, "results": "123", "hashOfConfig": "100"}, {"size": 12194, "mtime": 1750485914000, "results": "124", "hashOfConfig": "100"}, {"size": 12917, "mtime": 1750497900000, "results": "125", "hashOfConfig": "100"}, {"size": 13503, "mtime": 1750486292000, "results": "126", "hashOfConfig": "100"}, {"size": 11930, "mtime": 1750486214000, "results": "127", "hashOfConfig": "100"}, {"size": 16201, "mtime": 1750485170000, "results": "128", "hashOfConfig": "100"}, {"size": 6298, "mtime": 1749460684000, "results": "129", "hashOfConfig": "100"}, {"size": 14558, "mtime": 1749494266000, "results": "130", "hashOfConfig": "100"}, {"size": 13420, "mtime": 1750947652189, "results": "131", "hashOfConfig": "100"}, {"size": 6934, "mtime": 1750590990000, "results": "132", "hashOfConfig": "100"}, {"size": 11114, "mtime": 1749460684000, "results": "133", "hashOfConfig": "100"}, {"size": 21268, "mtime": 1750532258000, "results": "134", "hashOfConfig": "100"}, {"size": 24046, "mtime": 1750503298000, "results": "135", "hashOfConfig": "100"}, {"size": 38782, "mtime": 1749541954000, "results": "136", "hashOfConfig": "100"}, {"size": 12462, "mtime": 1750573110000, "results": "137", "hashOfConfig": "100"}, {"size": 45490, "mtime": 1750947122266, "results": "138", "hashOfConfig": "100"}, {"size": 37780, "mtime": 1750999336205, "results": "139", "hashOfConfig": "100"}, {"size": 19827, "mtime": 1749538128000, "results": "140", "hashOfConfig": "100"}, {"size": 25161, "mtime": 1749538944000, "results": "141", "hashOfConfig": "100"}, {"size": 39922, "mtime": 1750999036332, "results": "142", "hashOfConfig": "100"}, {"size": 36856, "mtime": 1750578874000, "results": "143", "hashOfConfig": "100"}, {"size": 19936, "mtime": 1750592184000, "results": "144", "hashOfConfig": "100"}, {"size": 11045, "mtime": 1751004656405, "results": "145", "hashOfConfig": "100"}, {"size": 12291, "mtime": 1749576472000, "results": "146", "hashOfConfig": "100"}, {"size": 6165, "mtime": 1749574028000, "results": "147", "hashOfConfig": "100"}, {"size": 18044, "mtime": 1750499802000, "results": "148", "hashOfConfig": "100"}, {"size": 1622, "mtime": 1749493532000, "results": "149", "hashOfConfig": "100"}, {"size": 323, "mtime": 1750588036000, "results": "150", "hashOfConfig": "100"}, {"size": 430, "mtime": 1750587482000, "results": "151", "hashOfConfig": "100"}, {"size": 6925, "mtime": 1750572170000, "results": "152", "hashOfConfig": "100"}, {"size": 15577, "mtime": 1749244308000, "results": "153", "hashOfConfig": "100"}, {"size": 415, "mtime": 1750592080000, "results": "154", "hashOfConfig": "100"}, {"size": 1263, "mtime": 1749243990000, "results": "155", "hashOfConfig": "100"}, {"size": 8607, "mtime": 1750503624000, "results": "156", "hashOfConfig": "100"}, {"size": 7700, "mtime": 1750061828000, "results": "157", "hashOfConfig": "100"}, {"size": 8779, "mtime": 1744341666000, "results": "158", "hashOfConfig": "100"}, {"size": 3425, "mtime": 1744162122000, "results": "159", "hashOfConfig": "100"}, {"size": 14617, "mtime": 1749196086000, "results": "160", "hashOfConfig": "100"}, {"size": 8029, "mtime": 1749493422000, "results": "161", "hashOfConfig": "100"}, {"size": 28449, "mtime": 1750482954000, "results": "162", "hashOfConfig": "100"}, {"size": 12377, "mtime": 1749244482000, "results": "163", "hashOfConfig": "100"}, {"size": 14628, "mtime": 1750497014000, "results": "164", "hashOfConfig": "100"}, {"size": 35316, "mtime": 1749460684000, "results": "165", "hashOfConfig": "100"}, {"size": 16392, "mtime": 1749259392000, "results": "166", "hashOfConfig": "100"}, {"size": 1272, "mtime": 1748021328000, "results": "167", "hashOfConfig": "100"}, {"size": 11325, "mtime": 1749460684000, "results": "168", "hashOfConfig": "100"}, {"size": 7867, "mtime": 1750670878000, "results": "169", "hashOfConfig": "100"}, {"size": 6155, "mtime": 1749460684000, "results": "170", "hashOfConfig": "100"}, {"size": 11465, "mtime": 1749460684000, "results": "171", "hashOfConfig": "100"}, {"size": 6947, "mtime": 1750587052000, "results": "172", "hashOfConfig": "100"}, {"size": 6776, "mtime": 1750943098693, "results": "173", "hashOfConfig": "100"}, {"size": 9892, "mtime": 1750573370000, "results": "174", "hashOfConfig": "100"}, {"size": 14652, "mtime": 1749486778000, "results": "175", "hashOfConfig": "100"}, {"size": 7916, "mtime": 1749460684000, "results": "176", "hashOfConfig": "100"}, {"size": 9563, "mtime": 1749258762000, "results": "177", "hashOfConfig": "100"}, {"size": 7273, "mtime": 1750502106000, "results": "178", "hashOfConfig": "100"}, {"size": 11010, "mtime": 1750957135442, "results": "179", "hashOfConfig": "100"}, {"size": 2951, "mtime": 1750590930000, "results": "180", "hashOfConfig": "100"}, {"size": 15459, "mtime": 1750672966000, "results": "181", "hashOfConfig": "100"}, {"size": 8010, "mtime": 1750626786000, "results": "182", "hashOfConfig": "100"}, {"size": 16382, "mtime": 1750591964000, "results": "183", "hashOfConfig": "100"}, {"size": 7365, "mtime": 1750591030000, "results": "184", "hashOfConfig": "100"}, {"size": 12742, "mtime": 1750591874000, "results": "185", "hashOfConfig": "100"}, {"size": 24262, "mtime": 1750755554000, "results": "186", "hashOfConfig": "100"}, {"size": 2271, "mtime": 1744341630000, "results": "187", "hashOfConfig": "100"}, {"size": 9322, "mtime": 1749321190000, "results": "188", "hashOfConfig": "100"}, {"size": 14293, "mtime": 1750940433844, "results": "189", "hashOfConfig": "100"}, {"size": 8154, "mtime": 1751051370464, "results": "190", "hashOfConfig": "100"}, {"size": 5411, "mtime": 1751051491333, "results": "191", "hashOfConfig": "100"}, {"size": 8739, "mtime": 1750999786596, "results": "192", "hashOfConfig": "100"}, {"size": 9579, "mtime": 1750999827928, "results": "193", "hashOfConfig": "100"}, {"size": 12709, "mtime": 1751004618727, "results": "194", "hashOfConfig": "100"}, {"size": 16413, "mtime": 1751004754752, "results": "195", "hashOfConfig": "100"}, {"size": 11351, "mtime": 1751004706550, "results": "196", "hashOfConfig": "100"}, {"size": 15638, "mtime": 1751051432766, "results": "197", "hashOfConfig": "100"}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gs8ssj", {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Projects v2\\asset management\\frontend\\src\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\App.js", ["492", "493", "494", "495", "496"], [], "D:\\Projects v2\\asset management\\frontend\\src\\theme\\designSystem.js", ["497"], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandler.js", ["498"], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ErrorBoundary.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\Layout.js", ["499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeList.js", ["524", "525"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\OrganizationMenu.js", ["526"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationList.js", ["527", "528", "529"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeList.js", ["530", "531"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDetail.js", ["532", "533", "534", "535"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\suppliers\\SuppliersMenu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ApprovalStatusPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupDashboard.js", ["536", "537", "538", "539", "540", "541", "542"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierTypesList.js", ["543", "544"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemStatusPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupMenu.js", ["545"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTagPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\PropertyStatusPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTypePage.js", ["546"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\EntryModePage.js", ["547"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\MainClassificationPage.js", ["548", "549", "550"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemBrandPage.js", ["551"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemCategoryPage.js", ["552"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\UnitOfMeasurePage.js", ["553"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemQualityPage.js", ["554"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemSizePage.js", ["555"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\SubClassificationPage.js", ["556", "557", "558"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\GenericInventoryPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemsDashboard.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMastersList.js", ["559"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\MainDashboardMenu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\Dashboard.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardItemMastersList.js", ["560", "561", "562", "563", "564", "565"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardBatchItemsList.js", ["566", "567", "568", "569"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMasterDetail.js", ["570", "571", "572", "573", "574", "575", "576"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemManagementMenu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ProfessionalItemMasterForm.js", ["577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemForm.js", ["592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemsList.js", ["603", "604", "605"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemDetail.js", ["606", "607", "608"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ModernItemMasterForm.js", ["609", "610", "611", "612", "613", "614", "615", "616", "617"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemForm.js", ["618", "619", "620", "621", "622", "623", "624"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemsList.js", ["625", "626", "627"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVouchersList.js", ["628", "629", "630", "631", "632"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\EndToEndTest.js", ["633", "634"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\DebugInventoryAPI.js", ["635"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemDetail.js", ["636"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\MaintenanceSchedule.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\debug\\APITest.js", ["637"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\auth\\Login.js", ["638"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\axios.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandling.js", ["639"], [], "D:\\Projects v2\\asset management\\frontend\\src\\theme\\professionalTheme.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeDialog.js", [], ["640"], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeDialog.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDialog.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\permissions.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\StatusManagement.js", ["641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652"], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\DashboardBanner.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\GatesList.js", ["653", "654", "655"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SuppliersList.js", ["656"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDetailView.js", ["657", "658"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\auth.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernSidebar.js", ["659", "660"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\organizations.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernBreadcrumbs.js", ["661"], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernCardGrid.js", ["662", "663"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\suppliers.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\inventorySetup.js", ["664"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\items.js", ["665"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDashboard.js", ["666", "667", "668", "669", "670", "671", "672"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\supplier.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierCategoriesList.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\config\\itemManagementDesign.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\config\\formConfig.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\config\\constants.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormTemplate.js", ["673", "674"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\model19.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormsList.js", ["675", "676"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19Menu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormDetail.js", ["677"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormCreate.js", ["678", "679", "680", "681", "682", "683", "684", "685"], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\filters.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\forms\\FormValidation.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\validation.js", ["686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StorageMenu.js", ["709", "710", "711"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoragePage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\analytics\\AnalyticsMenu.js", ["712"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\help\\HelpMenu.js", ["713"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVoucherForm.js", ["714", "715"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoresList.js", ["716", "717", "718"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoreTypesList.js", ["719", "720"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\ShelvesList.js", ["721", "722"], [], {"ruleId": "723", "severity": 1, "message": "724", "line": 59, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 59, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "727", "line": 60, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 60, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "728", "line": 61, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 61, "endColumn": 19}, {"ruleId": "723", "severity": 1, "message": "729", "line": 73, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 73, "endColumn": 23}, {"ruleId": "723", "severity": 1, "message": "730", "line": 80, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 80, "endColumn": 22}, {"ruleId": "731", "severity": 1, "message": "732", "line": 305, "column": 1, "nodeType": "733", "endLine": 315, "endColumn": 3}, {"ruleId": "723", "severity": 1, "message": "734", "line": 284, "column": 11, "nodeType": "725", "messageId": "726", "endLine": 284, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "735", "line": 21, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 21, "endColumn": 11}, {"ruleId": "723", "severity": 1, "message": "736", "line": 22, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 22, "endColumn": 7}, {"ruleId": "723", "severity": 1, "message": "737", "line": 23, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 23, "endColumn": 11}, {"ruleId": "723", "severity": 1, "message": "738", "line": 34, "column": 15, "nodeType": "725", "messageId": "726", "endLine": 34, "endColumn": 27}, {"ruleId": "723", "severity": 1, "message": "739", "line": 35, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 35, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "740", "line": 41, "column": 15, "nodeType": "725", "messageId": "726", "endLine": 41, "endColumn": 27}, {"ruleId": "723", "severity": 1, "message": "741", "line": 42, "column": 12, "nodeType": "725", "messageId": "726", "endLine": 42, "endColumn": 28}, {"ruleId": "723", "severity": 1, "message": "742", "line": 43, "column": 12, "nodeType": "725", "messageId": "726", "endLine": 43, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "743", "line": 44, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 44, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "744", "line": 45, "column": 14, "nodeType": "725", "messageId": "726", "endLine": 45, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "745", "line": 46, "column": 16, "nodeType": "725", "messageId": "726", "endLine": 46, "endColumn": 29}, {"ruleId": "723", "severity": 1, "message": "746", "line": 47, "column": 12, "nodeType": "725", "messageId": "726", "endLine": 47, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "747", "line": 48, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 48, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "748", "line": 49, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 49, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "749", "line": 50, "column": 18, "nodeType": "725", "messageId": "726", "endLine": 50, "endColumn": 33}, {"ruleId": "723", "severity": 1, "message": "750", "line": 51, "column": 15, "nodeType": "725", "messageId": "726", "endLine": 51, "endColumn": 27}, {"ruleId": "723", "severity": 1, "message": "751", "line": 52, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 52, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "752", "line": 54, "column": 12, "nodeType": "725", "messageId": "726", "endLine": 54, "endColumn": 27}, {"ruleId": "723", "severity": 1, "message": "753", "line": 61, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 61, "endColumn": 16}, {"ruleId": "723", "severity": 1, "message": "754", "line": 61, "column": 18, "nodeType": "725", "messageId": "726", "endLine": 61, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "755", "line": 61, "column": 27, "nodeType": "725", "messageId": "726", "endLine": 61, "endColumn": 36}, {"ruleId": "723", "severity": 1, "message": "756", "line": 62, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 62, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "757", "line": 63, "column": 18, "nodeType": "725", "messageId": "726", "endLine": 63, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "758", "line": 63, "column": 39, "nodeType": "725", "messageId": "726", "endLine": 63, "endColumn": 50}, {"ruleId": "723", "severity": 1, "message": "759", "line": 204, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 204, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "760", "line": 36, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 36, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "762", "line": 67, "column": 6, "nodeType": "763", "endLine": 67, "endColumn": 8, "suggestions": "764"}, {"ruleId": "723", "severity": 1, "message": "765", "line": 16, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 16, "endColumn": 10}, {"ruleId": "723", "severity": 1, "message": "766", "line": 21, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 21, "endColumn": 8}, {"ruleId": "723", "severity": 1, "message": "760", "line": 40, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 40, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "767", "line": 73, "column": 6, "nodeType": "763", "endLine": 73, "endColumn": 8, "suggestions": "768"}, {"ruleId": "723", "severity": 1, "message": "760", "line": 36, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 36, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "769", "line": 67, "column": 6, "nodeType": "763", "endLine": 67, "endColumn": 8, "suggestions": "770"}, {"ruleId": "723", "severity": 1, "message": "765", "line": 23, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 23, "endColumn": 10}, {"ruleId": "723", "severity": 1, "message": "771", "line": 40, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 40, "endColumn": 26}, {"ruleId": "723", "severity": 1, "message": "760", "line": 43, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 43, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "772", "line": 64, "column": 6, "nodeType": "763", "endLine": 64, "endColumn": 10, "suggestions": "773"}, {"ruleId": "723", "severity": 1, "message": "774", "line": 13, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 13, "endColumn": 10}, {"ruleId": "723", "severity": 1, "message": "775", "line": 19, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 19, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "776", "line": 19, "column": 16, "nodeType": "725", "messageId": "726", "endLine": 19, "endColumn": 27}, {"ruleId": "723", "severity": 1, "message": "777", "line": 21, "column": 8, "nodeType": "725", "messageId": "726", "endLine": 21, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "760", "line": 64, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 64, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "778", "line": 277, "column": 6, "nodeType": "763", "endLine": 277, "endColumn": 8, "suggestions": "779"}, {"ruleId": "723", "severity": 1, "message": "780", "line": 284, "column": 11, "nodeType": "725", "messageId": "726", "endLine": 284, "endColumn": 19}, {"ruleId": "723", "severity": 1, "message": "775", "line": 11, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 11, "endColumn": 7}, {"ruleId": "761", "severity": 1, "message": "781", "line": 58, "column": 6, "nodeType": "763", "endLine": 58, "endColumn": 37, "suggestions": "782"}, {"ruleId": "761", "severity": 1, "message": "783", "line": 232, "column": 6, "nodeType": "763", "endLine": 232, "endColumn": 8, "suggestions": "784"}, {"ruleId": "761", "severity": 1, "message": "785", "line": 66, "column": 6, "nodeType": "763", "endLine": 66, "endColumn": 8, "suggestions": "786"}, {"ruleId": "761", "severity": 1, "message": "787", "line": 75, "column": 6, "nodeType": "763", "endLine": 75, "endColumn": 8, "suggestions": "788"}, {"ruleId": "723", "severity": 1, "message": "775", "line": 10, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 7}, {"ruleId": "723", "severity": 1, "message": "776", "line": 11, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 11, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "789", "line": 84, "column": 6, "nodeType": "763", "endLine": 84, "endColumn": 8, "suggestions": "790"}, {"ruleId": "761", "severity": 1, "message": "791", "line": 68, "column": 6, "nodeType": "763", "endLine": 68, "endColumn": 8, "suggestions": "792"}, {"ruleId": "761", "severity": 1, "message": "793", "line": 66, "column": 6, "nodeType": "763", "endLine": 66, "endColumn": 8, "suggestions": "794"}, {"ruleId": "761", "severity": 1, "message": "795", "line": 67, "column": 6, "nodeType": "763", "endLine": 67, "endColumn": 8, "suggestions": "796"}, {"ruleId": "761", "severity": 1, "message": "797", "line": 68, "column": 6, "nodeType": "763", "endLine": 68, "endColumn": 8, "suggestions": "798"}, {"ruleId": "761", "severity": 1, "message": "799", "line": 66, "column": 6, "nodeType": "763", "endLine": 66, "endColumn": 8, "suggestions": "800"}, {"ruleId": "723", "severity": 1, "message": "775", "line": 10, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 10, "endColumn": 7}, {"ruleId": "723", "severity": 1, "message": "776", "line": 11, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 11, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "801", "line": 89, "column": 6, "nodeType": "763", "endLine": 89, "endColumn": 8, "suggestions": "802"}, {"ruleId": "761", "severity": 1, "message": "803", "line": 70, "column": 6, "nodeType": "763", "endLine": 70, "endColumn": 37, "suggestions": "804"}, {"ruleId": "723", "severity": 1, "message": "766", "line": 38, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 38, "endColumn": 8}, {"ruleId": "723", "severity": 1, "message": "805", "line": 57, "column": 25, "nodeType": "725", "messageId": "726", "endLine": 57, "endColumn": 38}, {"ruleId": "723", "severity": 1, "message": "806", "line": 57, "column": 56, "nodeType": "725", "messageId": "726", "endLine": 57, "endColumn": 66}, {"ruleId": "723", "severity": 1, "message": "760", "line": 62, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 62, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "807", "line": 85, "column": 6, "nodeType": "763", "endLine": 85, "endColumn": 65, "suggestions": "808"}, {"ruleId": "723", "severity": 1, "message": "809", "line": 103, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 103, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "766", "line": 38, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 38, "endColumn": 8}, {"ruleId": "723", "severity": 1, "message": "810", "line": 63, "column": 40, "nodeType": "725", "messageId": "726", "endLine": 63, "endColumn": 54}, {"ruleId": "723", "severity": 1, "message": "760", "line": 67, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 67, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "811", "line": 92, "column": 6, "nodeType": "763", "endLine": 92, "endColumn": 51, "suggestions": "812"}, {"ruleId": "723", "severity": 1, "message": "813", "line": 50, "column": 12, "nodeType": "725", "messageId": "726", "endLine": 50, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "814", "line": 54, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 54, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "815", "line": 59, "column": 15, "nodeType": "725", "messageId": "726", "endLine": 59, "endColumn": 27}, {"ruleId": "723", "severity": 1, "message": "816", "line": 61, "column": 14, "nodeType": "725", "messageId": "726", "endLine": 61, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "817", "line": 62, "column": 18, "nodeType": "725", "messageId": "726", "endLine": 62, "endColumn": 28}, {"ruleId": "723", "severity": 1, "message": "818", "line": 63, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 63, "endColumn": 25}, {"ruleId": "761", "severity": 1, "message": "819", "line": 105, "column": 6, "nodeType": "763", "endLine": 105, "endColumn": 10, "suggestions": "820"}, {"ruleId": "723", "severity": 1, "message": "821", "line": 29, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 29, "endColumn": 13}, {"ruleId": "723", "severity": 1, "message": "765", "line": 34, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 34, "endColumn": 10}, {"ruleId": "723", "severity": 1, "message": "822", "line": 35, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 35, "endColumn": 8}, {"ruleId": "723", "severity": 1, "message": "823", "line": 37, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 37, "endColumn": 13}, {"ruleId": "723", "severity": 1, "message": "774", "line": 38, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 38, "endColumn": 10}, {"ruleId": "723", "severity": 1, "message": "735", "line": 39, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 39, "endColumn": 11}, {"ruleId": "723", "severity": 1, "message": "816", "line": 57, "column": 14, "nodeType": "725", "messageId": "726", "endLine": 57, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "738", "line": 58, "column": 15, "nodeType": "725", "messageId": "726", "endLine": 58, "endColumn": 27}, {"ruleId": "723", "severity": 1, "message": "748", "line": 62, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 62, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "747", "line": 63, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 63, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "824", "line": 122, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 122, "endColumn": 22}, {"ruleId": "723", "severity": 1, "message": "825", "line": 122, "column": 24, "nodeType": "725", "messageId": "726", "endLine": 122, "endColumn": 39}, {"ruleId": "761", "severity": 1, "message": "826", "line": 157, "column": 6, "nodeType": "763", "endLine": 157, "endColumn": 8, "suggestions": "827"}, {"ruleId": "761", "severity": 1, "message": "828", "line": 164, "column": 6, "nodeType": "763", "endLine": 164, "endColumn": 18, "suggestions": "829"}, {"ruleId": "761", "severity": 1, "message": "830", "line": 167, "column": 29, "nodeType": "725", "endLine": 167, "endColumn": 40}, {"ruleId": "723", "severity": 1, "message": "775", "line": 4, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 7}, {"ruleId": "723", "severity": 1, "message": "776", "line": 5, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "735", "line": 28, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 28, "endColumn": 11}, {"ruleId": "723", "severity": 1, "message": "739", "line": 39, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 39, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "831", "line": 51, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 51, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "832", "line": 52, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 52, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "833", "line": 54, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 54, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "834", "line": 55, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 55, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "835", "line": 56, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 56, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "836", "line": 452, "column": 6, "nodeType": "763", "endLine": 452, "endColumn": 18, "suggestions": "837"}, {"ruleId": "761", "severity": 1, "message": "838", "line": 460, "column": 6, "nodeType": "763", "endLine": 460, "endColumn": 35, "suggestions": "839"}, {"ruleId": "723", "severity": 1, "message": "775", "line": 23, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 23, "endColumn": 7}, {"ruleId": "723", "severity": 1, "message": "776", "line": 24, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 24, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "840", "line": 86, "column": 6, "nodeType": "763", "endLine": 86, "endColumn": 37, "suggestions": "841"}, {"ruleId": "723", "severity": 1, "message": "842", "line": 34, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 34, "endColumn": 9}, {"ruleId": "723", "severity": 1, "message": "843", "line": 43, "column": 15, "nodeType": "725", "messageId": "726", "endLine": 43, "endColumn": 27}, {"ruleId": "761", "severity": 1, "message": "844", "line": 73, "column": 6, "nodeType": "763", "endLine": 73, "endColumn": 10, "suggestions": "845"}, {"ruleId": "723", "severity": 1, "message": "775", "line": 5, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 7}, {"ruleId": "723", "severity": 1, "message": "776", "line": 6, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 6, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "735", "line": 31, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 31, "endColumn": 11}, {"ruleId": "723", "severity": 1, "message": "743", "line": 36, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 36, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "739", "line": 45, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 45, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "834", "line": 60, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 60, "endColumn": 15}, {"ruleId": "723", "severity": 1, "message": "835", "line": 61, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 61, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "846", "line": 66, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 66, "endColumn": 21}, {"ruleId": "761", "severity": 1, "message": "847", "line": 626, "column": 6, "nodeType": "763", "endLine": 626, "endColumn": 18, "suggestions": "848"}, {"ruleId": "723", "severity": 1, "message": "765", "line": 25, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 25, "endColumn": 10}, {"ruleId": "723", "severity": 1, "message": "849", "line": 34, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 34, "endColumn": 7}, {"ruleId": "723", "severity": 1, "message": "766", "line": 36, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 36, "endColumn": 8}, {"ruleId": "723", "severity": 1, "message": "760", "line": 63, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 63, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "850", "line": 131, "column": 6, "nodeType": "763", "endLine": 131, "endColumn": 18, "suggestions": "851"}, {"ruleId": "852", "severity": 1, "message": "853", "line": 308, "column": 5, "nodeType": "854", "messageId": "855", "endLine": 354, "endColumn": 6}, {"ruleId": "856", "severity": 1, "message": "857", "line": 326, "column": 45, "nodeType": "858", "messageId": "859", "endLine": 326, "endColumn": 46, "suggestions": "860"}, {"ruleId": "723", "severity": 1, "message": "766", "line": 38, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 38, "endColumn": 8}, {"ruleId": "723", "severity": 1, "message": "760", "line": 61, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 61, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "861", "line": 77, "column": 6, "nodeType": "763", "endLine": 77, "endColumn": 51, "suggestions": "862"}, {"ruleId": "723", "severity": 1, "message": "822", "line": 21, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 21, "endColumn": 8}, {"ruleId": "723", "severity": 1, "message": "863", "line": 29, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 29, "endColumn": 12}, {"ruleId": "723", "severity": 1, "message": "864", "line": 36, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 36, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "865", "line": 51, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 51, "endColumn": 20}, {"ruleId": "761", "severity": 1, "message": "866", "line": 58, "column": 6, "nodeType": "763", "endLine": 58, "endColumn": 8, "suggestions": "867"}, {"ruleId": "723", "severity": 1, "message": "868", "line": 5, "column": 27, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 36}, {"ruleId": "723", "severity": 1, "message": "869", "line": 59, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 59, "endColumn": 18}, {"ruleId": "761", "severity": 1, "message": "870", "line": 106, "column": 6, "nodeType": "763", "endLine": 106, "endColumn": 8, "suggestions": "871"}, {"ruleId": "761", "severity": 1, "message": "872", "line": 61, "column": 6, "nodeType": "763", "endLine": 61, "endColumn": 10, "suggestions": "873"}, {"ruleId": "723", "severity": 1, "message": "874", "line": 69, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 69, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "875", "line": 12, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 12, "endColumn": 9}, {"ruleId": "731", "severity": 1, "message": "732", "line": 315, "column": 1, "nodeType": "733", "endLine": 327, "endColumn": 3}, {"ruleId": "761", "severity": 1, "message": "876", "line": 129, "column": 6, "nodeType": "763", "endLine": 129, "endColumn": 49, "suggestions": "877", "suppressions": "878"}, {"ruleId": "723", "severity": 1, "message": "879", "line": 33, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 33, "endColumn": 8}, {"ruleId": "723", "severity": 1, "message": "760", "line": 61, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 61, "endColumn": 14}, {"ruleId": "761", "severity": 1, "message": "880", "line": 123, "column": 6, "nodeType": "763", "endLine": 123, "endColumn": 8, "suggestions": "881"}, {"ruleId": "723", "severity": 1, "message": "882", "line": 211, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 211, "endColumn": 19}, {"ruleId": "883", "severity": 1, "message": "884", "line": 712, "column": 41, "nodeType": "885", "messageId": "886", "endLine": 712, "endColumn": 43}, {"ruleId": "883", "severity": 1, "message": "884", "line": 712, "column": 62, "nodeType": "885", "messageId": "886", "endLine": 712, "endColumn": 64}, {"ruleId": "883", "severity": 1, "message": "887", "line": 712, "column": 62, "nodeType": "885", "messageId": "886", "endLine": 712, "endColumn": 64}, {"ruleId": "883", "severity": 1, "message": "887", "line": 712, "column": 84, "nodeType": "885", "messageId": "886", "endLine": 712, "endColumn": 86}, {"ruleId": "883", "severity": 1, "message": "884", "line": 713, "column": 46, "nodeType": "885", "messageId": "886", "endLine": 713, "endColumn": 48}, {"ruleId": "883", "severity": 1, "message": "884", "line": 713, "column": 65, "nodeType": "885", "messageId": "886", "endLine": 713, "endColumn": 67}, {"ruleId": "883", "severity": 1, "message": "887", "line": 713, "column": 65, "nodeType": "885", "messageId": "886", "endLine": 713, "endColumn": 67}, {"ruleId": "883", "severity": 1, "message": "887", "line": 713, "column": 87, "nodeType": "885", "messageId": "886", "endLine": 713, "endColumn": 89}, {"ruleId": "723", "severity": 1, "message": "775", "line": 4, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 4, "endColumn": 7}, {"ruleId": "723", "severity": 1, "message": "776", "line": 5, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 5, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "760", "line": 49, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 49, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "888", "line": 371, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 371, "endColumn": 29}, {"ruleId": "723", "severity": 1, "message": "889", "line": 14, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 14, "endColumn": 16}, {"ruleId": "761", "severity": 1, "message": "890", "line": 49, "column": 6, "nodeType": "763", "endLine": 49, "endColumn": 24, "suggestions": "891"}, {"ruleId": "723", "severity": 1, "message": "765", "line": 16, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 16, "endColumn": 10}, {"ruleId": "723", "severity": 1, "message": "760", "line": 38, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 38, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "760", "line": 24, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 24, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "755", "line": 26, "column": 27, "nodeType": "725", "messageId": "726", "endLine": 26, "endColumn": 36}, {"ruleId": "723", "severity": 1, "message": "760", "line": 38, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 38, "endColumn": 14}, {"ruleId": "731", "severity": 1, "message": "732", "line": 152, "column": 1, "nodeType": "733", "endLine": 185, "endColumn": 3}, {"ruleId": "731", "severity": 1, "message": "732", "line": 345, "column": 1, "nodeType": "733", "endLine": 352, "endColumn": 3}, {"ruleId": "723", "severity": 1, "message": "892", "line": 22, "column": 16, "nodeType": "725", "messageId": "726", "endLine": 22, "endColumn": 29}, {"ruleId": "723", "severity": 1, "message": "814", "line": 23, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 23, "endColumn": 31}, {"ruleId": "723", "severity": 1, "message": "893", "line": 24, "column": 11, "nodeType": "725", "messageId": "726", "endLine": 24, "endColumn": 19}, {"ruleId": "723", "severity": 1, "message": "894", "line": 25, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 25, "endColumn": 23}, {"ruleId": "723", "severity": 1, "message": "743", "line": 28, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 28, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "895", "line": 29, "column": 15, "nodeType": "725", "messageId": "726", "endLine": 29, "endColumn": 27}, {"ruleId": "723", "severity": 1, "message": "896", "line": 163, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 163, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "765", "line": 18, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 18, "endColumn": 10}, {"ruleId": "723", "severity": 1, "message": "897", "line": 62, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 62, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "775", "line": 11, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 11, "endColumn": 7}, {"ruleId": "761", "severity": 1, "message": "898", "line": 78, "column": 6, "nodeType": "763", "endLine": 78, "endColumn": 69, "suggestions": "899"}, {"ruleId": "761", "severity": 1, "message": "900", "line": 54, "column": 6, "nodeType": "763", "endLine": 54, "endColumn": 10, "suggestions": "901"}, {"ruleId": "723", "severity": 1, "message": "775", "line": 34, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 34, "endColumn": 7}, {"ruleId": "723", "severity": 1, "message": "776", "line": 35, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 35, "endColumn": 14}, {"ruleId": "723", "severity": 1, "message": "823", "line": 37, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 37, "endColumn": 13}, {"ruleId": "723", "severity": 1, "message": "774", "line": 38, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 38, "endColumn": 10}, {"ruleId": "723", "severity": 1, "message": "743", "line": 44, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 44, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "902", "line": 45, "column": 13, "nodeType": "725", "messageId": "726", "endLine": 45, "endColumn": 23}, {"ruleId": "723", "severity": 1, "message": "903", "line": 54, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 54, "endColumn": 22}, {"ruleId": "761", "severity": 1, "message": "904", "line": 79, "column": 6, "nodeType": "763", "endLine": 79, "endColumn": 8, "suggestions": "905"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 31, "column": 17, "nodeType": "858", "messageId": "859", "endLine": 31, "endColumn": 18, "suggestions": "906"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 32, "column": 33, "nodeType": "858", "messageId": "859", "endLine": 32, "endColumn": 34, "suggestions": "907"}, {"ruleId": "856", "severity": 1, "message": "908", "line": 33, "column": 22, "nodeType": "858", "messageId": "859", "endLine": 33, "endColumn": 23, "suggestions": "909"}, {"ruleId": "856", "severity": 1, "message": "910", "line": 33, "column": 24, "nodeType": "858", "messageId": "859", "endLine": 33, "endColumn": 25, "suggestions": "911"}, {"ruleId": "856", "severity": 1, "message": "857", "line": 35, "column": 27, "nodeType": "858", "messageId": "859", "endLine": 35, "endColumn": 28, "suggestions": "912"}, {"ruleId": "856", "severity": 1, "message": "913", "line": 38, "column": 33, "nodeType": "858", "messageId": "859", "endLine": 38, "endColumn": 34, "suggestions": "914"}, {"ruleId": "856", "severity": 1, "message": "915", "line": 38, "column": 35, "nodeType": "858", "messageId": "859", "endLine": 38, "endColumn": 36, "suggestions": "916"}, {"ruleId": "856", "severity": 1, "message": "908", "line": 38, "column": 37, "nodeType": "858", "messageId": "859", "endLine": 38, "endColumn": 38, "suggestions": "917"}, {"ruleId": "856", "severity": 1, "message": "910", "line": 38, "column": 39, "nodeType": "858", "messageId": "859", "endLine": 38, "endColumn": 40, "suggestions": "918"}, {"ruleId": "856", "severity": 1, "message": "919", "line": 38, "column": 41, "nodeType": "858", "messageId": "859", "endLine": 38, "endColumn": 42, "suggestions": "920"}, {"ruleId": "856", "severity": 1, "message": "921", "line": 38, "column": 43, "nodeType": "858", "messageId": "859", "endLine": 38, "endColumn": 44, "suggestions": "922"}, {"ruleId": "856", "severity": 1, "message": "913", "line": 39, "column": 25, "nodeType": "858", "messageId": "859", "endLine": 39, "endColumn": 26, "suggestions": "923"}, {"ruleId": "856", "severity": 1, "message": "915", "line": 39, "column": 27, "nodeType": "858", "messageId": "859", "endLine": 39, "endColumn": 28, "suggestions": "924"}, {"ruleId": "856", "severity": 1, "message": "908", "line": 39, "column": 29, "nodeType": "858", "messageId": "859", "endLine": 39, "endColumn": 30, "suggestions": "925"}, {"ruleId": "856", "severity": 1, "message": "910", "line": 39, "column": 31, "nodeType": "858", "messageId": "859", "endLine": 39, "endColumn": 32, "suggestions": "926"}, {"ruleId": "856", "severity": 1, "message": "919", "line": 39, "column": 33, "nodeType": "858", "messageId": "859", "endLine": 39, "endColumn": 34, "suggestions": "927"}, {"ruleId": "856", "severity": 1, "message": "921", "line": 39, "column": 35, "nodeType": "858", "messageId": "859", "endLine": 39, "endColumn": 36, "suggestions": "928"}, {"ruleId": "856", "severity": 1, "message": "913", "line": 385, "column": 55, "nodeType": "858", "messageId": "859", "endLine": 385, "endColumn": 56, "suggestions": "929"}, {"ruleId": "856", "severity": 1, "message": "915", "line": 385, "column": 57, "nodeType": "858", "messageId": "859", "endLine": 385, "endColumn": 58, "suggestions": "930"}, {"ruleId": "856", "severity": 1, "message": "908", "line": 385, "column": 59, "nodeType": "858", "messageId": "859", "endLine": 385, "endColumn": 60, "suggestions": "931"}, {"ruleId": "856", "severity": 1, "message": "910", "line": 385, "column": 61, "nodeType": "858", "messageId": "859", "endLine": 385, "endColumn": 62, "suggestions": "932"}, {"ruleId": "856", "severity": 1, "message": "919", "line": 385, "column": 63, "nodeType": "858", "messageId": "859", "endLine": 385, "endColumn": 64, "suggestions": "933"}, {"ruleId": "856", "severity": 1, "message": "921", "line": 385, "column": 65, "nodeType": "858", "messageId": "859", "endLine": 385, "endColumn": 66, "suggestions": "934"}, {"ruleId": "723", "severity": 1, "message": "935", "line": 19, "column": 16, "nodeType": "725", "messageId": "726", "endLine": 19, "endColumn": 29}, {"ruleId": "723", "severity": 1, "message": "892", "line": 25, "column": 16, "nodeType": "725", "messageId": "726", "endLine": 25, "endColumn": 29}, {"ruleId": "723", "severity": 1, "message": "896", "line": 32, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 32, "endColumn": 17}, {"ruleId": "723", "severity": 1, "message": "936", "line": 1, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 1, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "936", "line": 1, "column": 10, "nodeType": "725", "messageId": "726", "endLine": 1, "endColumn": 18}, {"ruleId": "723", "severity": 1, "message": "822", "line": 9, "column": 3, "nodeType": "725", "messageId": "726", "endLine": 9, "endColumn": 8}, {"ruleId": "761", "severity": 1, "message": "937", "line": 60, "column": 6, "nodeType": "763", "endLine": 60, "endColumn": 18, "suggestions": "938"}, {"ruleId": "723", "severity": 1, "message": "864", "line": 41, "column": 17, "nodeType": "725", "messageId": "726", "endLine": 41, "endColumn": 25}, {"ruleId": "723", "severity": 1, "message": "896", "line": 49, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 49, "endColumn": 17}, {"ruleId": "761", "severity": 1, "message": "866", "line": 73, "column": 6, "nodeType": "763", "endLine": 73, "endColumn": 8, "suggestions": "939"}, {"ruleId": "723", "severity": 1, "message": "896", "line": 43, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 43, "endColumn": 17}, {"ruleId": "761", "severity": 1, "message": "940", "line": 59, "column": 6, "nodeType": "763", "endLine": 59, "endColumn": 8, "suggestions": "941"}, {"ruleId": "723", "severity": 1, "message": "896", "line": 49, "column": 9, "nodeType": "725", "messageId": "726", "endLine": 49, "endColumn": 17}, {"ruleId": "761", "severity": 1, "message": "866", "line": 70, "column": 6, "nodeType": "763", "endLine": 70, "endColumn": 8, "suggestions": "942"}, "no-unused-vars", "'StoreTypesList' is defined but never used.", "Identifier", "unusedVar", "'StoresList' is defined but never used.", "'ShelvesList' is defined but never used.", "'ItemMastersList' is defined but never used.", "'BatchItemsList' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'logData' is assigned a value but never used.", "'Collapse' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'CategoryIcon' is defined but never used.", "'AssignmentIcon' is defined but never used.", "'ItemTypeIcon' is defined but never used.", "'ItemCategoryIcon' is defined but never used.", "'GroupIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'ReceiptIcon' is defined but never used.", "'ExitToAppIcon' is defined but never used.", "'InputIcon' is defined but never used.", "'ExpandLessIcon' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ApprovalIcon' is defined but never used.", "'LocalOfferIcon' is defined but never used.", "'MaintenanceIcon' is defined but never used.", "'colors' is defined but never used.", "'shadows' is defined but never used.", "'gradients' is defined but never used.", "'ModernSidebar' is defined but never used.", "'SPACING' is defined but never used.", "'TRANSITIONS' is defined but never used.", "'currentUser' is assigned a value but never used.", "'theme' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchOrgTypes'. Either include it or remove the dependency array.", "ArrayExpression", ["943"], "'Divider' is defined but never used.", "'alpha' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchOrganizations'. Either include it or remove the dependency array.", ["944"], "React Hook useEffect has a missing dependency: 'fetchOffices'. Either include it or remove the dependency array.", ["945"], "'getOrganizations' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchOrganizationDetails'. Either include it or remove the dependency array.", ["946"], "'Tooltip' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'ModernCardGrid' is defined but never used.", "React Hook useEffect has a missing dependency: 'dashboardCards'. Either include it or remove the dependency array.", ["947"], "'cardItem' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSupplierTypes'. Either include it or remove the dependency array.", ["948"], "React Hook useEffect has a missing dependency: 'baseCards'. Either include it or remove the dependency array.", ["949"], "React Hook useEffect has a missing dependency: 'fetchItemTypes'. Either include it or remove the dependency array.", ["950"], "React Hook useEffect has a missing dependency: 'fetchEntryModes'. Either include it or remove the dependency array.", ["951"], "React Hook useEffect has a missing dependency: 'fetchClassifications'. Either include it or remove the dependency array.", ["952"], "React Hook useEffect has a missing dependency: 'fetchItemBrands'. Either include it or remove the dependency array.", ["953"], "React Hook useEffect has a missing dependency: 'fetchItemCategories'. Either include it or remove the dependency array.", ["954"], "React Hook useEffect has a missing dependency: 'fetchUnitsOfMeasure'. Either include it or remove the dependency array.", ["955"], "React Hook useEffect has a missing dependency: 'fetchItemQualities'. Either include it or remove the dependency array.", ["956"], "React Hook useEffect has a missing dependency: 'fetchItemSizes'. Either include it or remove the dependency array.", ["957"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["958"], "React Hook useEffect has a missing dependency: 'loadItemMasters'. Either include it or remove the dependency array.", ["959"], "'getStatusChip' is defined but never used.", "'formatDate' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchItemMasters'. Either include it or remove the dependency array.", ["960"], "'errorInfo' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchBatchItems'. Either include it or remove the dependency array.", ["961"], "'ShareIcon' is defined but never used.", "'TrendingUpIcon' is defined but never used.", "'SecurityIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'ActiveIcon' is defined but never used.", "'InactiveIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadItemMaster' and 'loadRelatedData'. Either include them or remove the dependency array.", ["962"], "'CardHeader' is defined but never used.", "'Alert' is defined but never used.", "'IconButton' is defined but never used.", "'showAdvanced' is assigned a value but never used.", "'setShowAdvanced' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadDropdownOptions'. Either include it or remove the dependency array.", ["963"], "React Hook useEffect has a missing dependency: 'loadItemMaster'. Either include it or remove the dependency array.", ["964"], "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "'FIELD_LABELS' is defined but never used.", "'PLACEHOLDERS' is defined but never used.", "'UI_CONSTANTS' is defined but never used.", "'COLOR_THEMES' is defined but never used.", "'BUTTON_TEXT' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadDropdowns'. Either include it or remove the dependency array.", ["965"], "React Hook useEffect has a missing dependency: 'loadBatchItem'. Either include it or remove the dependency array.", ["966"], "React Hook useEffect has a missing dependency: 'loadBatches'. Either include it or remove the dependency array.", ["967"], "'Rating' is defined but never used.", "'TimelineIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadBatchItem' and 'loadInventoryItems'. Either include them or remove the dependency array.", ["968"], "'getStepIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadDropdownOptions' and 'loadItemMaster'. Either include them or remove the dependency array.", ["969"], "'Chip' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadDropdownOptions' and 'loadInventoryItem'. Either include them or remove the dependency array.", ["970"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "no-useless-escape", "Unnecessary escape character: \\-.", "Literal", "unnecessaryEscape", ["971", "972"], "React Hook useEffect has a missing dependency: 'fetchInventoryItems'. Either include it or remove the dependency array.", ["973"], "'TextField' is defined but never used.", "'ViewIcon' is defined but never used.", "'categories' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["974"], "'useEffect' is defined but never used.", "'testData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'testAllEndpoints'. Either include it or remove the dependency array.", ["975"], "React Hook useEffect has a missing dependency: 'fetchInventoryItem'. Either include it or remove the dependency array.", ["976"], "'response' is assigned a value but never used.", "'Avatar' is defined but never used.", "React Hook useEffect has a missing dependency: 'formik'. Either include it or remove the dependency array.", ["977"], ["978"], "'Paper' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchItems'. Either include it or remove the dependency array.", ["979"], "'StatusCard' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "Unexpected mix of '||' and '&&'. Use parentheses to clarify the intended order of operations.", "'getSupplierTypeColor' is assigned a value but never used.", "'DialogActions' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSupplier'. Either include it or remove the dependency array.", ["980"], "'DashboardIcon' is defined but never used.", "'StarIcon' is defined but never used.", "'PublicIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'navigate' is assigned a value but never used.", "'amharicStyle' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchForms'. Either include it or remove the dependency array.", ["981"], "React Hook useEffect has a missing dependency: 'fetchForm'. Either include it or remove the dependency array.", ["982"], "'RemoveIcon' is defined but never used.", "'formatEthiopianDate' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchAvailableItems' and 'fetchStoresAndShelves'. Either include them or remove the dependency array.", ["983"], ["984", "985"], ["986", "987"], "Unnecessary escape character: \\(.", ["988", "989"], "Unnecessary escape character: \\).", ["990", "991"], ["992", "993"], "Unnecessary escape character: \\..", ["994", "995"], "Unnecessary escape character: \\,.", ["996", "997"], ["998", "999"], ["1000", "1001"], "Unnecessary escape character: \\/.", ["1002", "1003"], "Unnecessary escape character: \\&.", ["1004", "1005"], ["1006", "1007"], ["1008", "1009"], ["1010", "1011"], ["1012", "1013"], ["1014", "1015"], ["1016", "1017"], ["1018", "1019"], ["1020", "1021"], ["1022", "1023"], ["1024", "1025"], ["1026", "1027"], ["1028", "1029"], "'WarehouseIcon' is defined but never used.", "'useState' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadCategories' and 'loadVoucher'. Either include them or remove the dependency array.", ["1030"], ["1031"], "React Hook useEffect has a missing dependency: 'loadStoreTypes'. Either include it or remove the dependency array.", ["1032"], ["1033"], {"desc": "1034", "fix": "1035"}, {"desc": "1036", "fix": "1037"}, {"desc": "1038", "fix": "1039"}, {"desc": "1040", "fix": "1041"}, {"desc": "1042", "fix": "1043"}, {"desc": "1044", "fix": "1045"}, {"desc": "1046", "fix": "1047"}, {"desc": "1048", "fix": "1049"}, {"desc": "1050", "fix": "1051"}, {"desc": "1052", "fix": "1053"}, {"desc": "1054", "fix": "1055"}, {"desc": "1056", "fix": "1057"}, {"desc": "1058", "fix": "1059"}, {"desc": "1060", "fix": "1061"}, {"desc": "1062", "fix": "1063"}, {"desc": "1064", "fix": "1065"}, {"desc": "1066", "fix": "1067"}, {"desc": "1068", "fix": "1069"}, {"desc": "1070", "fix": "1071"}, {"desc": "1072", "fix": "1073"}, {"desc": "1074", "fix": "1075"}, {"desc": "1076", "fix": "1077"}, {"desc": "1078", "fix": "1079"}, {"desc": "1080", "fix": "1081"}, {"desc": "1082", "fix": "1083"}, {"desc": "1084", "fix": "1085"}, {"desc": "1086", "fix": "1087"}, {"desc": "1088", "fix": "1089"}, {"messageId": "1090", "fix": "1091", "desc": "1092"}, {"messageId": "1093", "fix": "1094", "desc": "1095"}, {"desc": "1096", "fix": "1097"}, {"desc": "1098", "fix": "1099"}, {"desc": "1100", "fix": "1101"}, {"desc": "1102", "fix": "1103"}, {"desc": "1104", "fix": "1105"}, {"kind": "1106", "justification": "1107"}, {"desc": "1108", "fix": "1109"}, {"desc": "1110", "fix": "1111"}, {"desc": "1112", "fix": "1113"}, {"desc": "1114", "fix": "1115"}, {"desc": "1116", "fix": "1117"}, {"messageId": "1090", "fix": "1118", "desc": "1092"}, {"messageId": "1093", "fix": "1119", "desc": "1095"}, {"messageId": "1090", "fix": "1120", "desc": "1092"}, {"messageId": "1093", "fix": "1121", "desc": "1095"}, {"messageId": "1090", "fix": "1122", "desc": "1092"}, {"messageId": "1093", "fix": "1123", "desc": "1095"}, {"messageId": "1090", "fix": "1124", "desc": "1092"}, {"messageId": "1093", "fix": "1125", "desc": "1095"}, {"messageId": "1090", "fix": "1126", "desc": "1092"}, {"messageId": "1093", "fix": "1127", "desc": "1095"}, {"messageId": "1090", "fix": "1128", "desc": "1092"}, {"messageId": "1093", "fix": "1129", "desc": "1095"}, {"messageId": "1090", "fix": "1130", "desc": "1092"}, {"messageId": "1093", "fix": "1131", "desc": "1095"}, {"messageId": "1090", "fix": "1132", "desc": "1092"}, {"messageId": "1093", "fix": "1133", "desc": "1095"}, {"messageId": "1090", "fix": "1134", "desc": "1092"}, {"messageId": "1093", "fix": "1135", "desc": "1095"}, {"messageId": "1090", "fix": "1136", "desc": "1092"}, {"messageId": "1093", "fix": "1137", "desc": "1095"}, {"messageId": "1090", "fix": "1138", "desc": "1092"}, {"messageId": "1093", "fix": "1139", "desc": "1095"}, {"messageId": "1090", "fix": "1140", "desc": "1092"}, {"messageId": "1093", "fix": "1141", "desc": "1095"}, {"messageId": "1090", "fix": "1142", "desc": "1092"}, {"messageId": "1093", "fix": "1143", "desc": "1095"}, {"messageId": "1090", "fix": "1144", "desc": "1092"}, {"messageId": "1093", "fix": "1145", "desc": "1095"}, {"messageId": "1090", "fix": "1146", "desc": "1092"}, {"messageId": "1093", "fix": "1147", "desc": "1095"}, {"messageId": "1090", "fix": "1148", "desc": "1092"}, {"messageId": "1093", "fix": "1149", "desc": "1095"}, {"messageId": "1090", "fix": "1150", "desc": "1092"}, {"messageId": "1093", "fix": "1151", "desc": "1095"}, {"messageId": "1090", "fix": "1152", "desc": "1092"}, {"messageId": "1093", "fix": "1153", "desc": "1095"}, {"messageId": "1090", "fix": "1154", "desc": "1092"}, {"messageId": "1093", "fix": "1155", "desc": "1095"}, {"messageId": "1090", "fix": "1156", "desc": "1092"}, {"messageId": "1093", "fix": "1157", "desc": "1095"}, {"messageId": "1090", "fix": "1158", "desc": "1092"}, {"messageId": "1093", "fix": "1159", "desc": "1095"}, {"messageId": "1090", "fix": "1160", "desc": "1092"}, {"messageId": "1093", "fix": "1161", "desc": "1095"}, {"messageId": "1090", "fix": "1162", "desc": "1092"}, {"messageId": "1093", "fix": "1163", "desc": "1095"}, {"desc": "1164", "fix": "1165"}, {"desc": "1098", "fix": "1166"}, {"desc": "1167", "fix": "1168"}, {"desc": "1098", "fix": "1169"}, "Update the dependencies array to be: [fetchOrgTypes]", {"range": "1170", "text": "1171"}, "Update the dependencies array to be: [fetchOrganizations]", {"range": "1172", "text": "1173"}, "Update the dependencies array to be: [fetchOffices]", {"range": "1174", "text": "1175"}, "Update the dependencies array to be: [fetchOrganizationDetails, id]", {"range": "1176", "text": "1177"}, "Update the dependencies array to be: [dashboardCards]", {"range": "1178", "text": "1179"}, "Update the dependencies array to be: [fetchSupplierTypes, page, rowsPerPage, searchTerm]", {"range": "1180", "text": "1181"}, "Update the dependencies array to be: [baseCards]", {"range": "1182", "text": "1183"}, "Update the dependencies array to be: [fetchItemTypes]", {"range": "1184", "text": "1185"}, "Update the dependencies array to be: [fetchEntryModes]", {"range": "1186", "text": "1187"}, "Update the dependencies array to be: [fetchClassifications]", {"range": "1188", "text": "1189"}, "Update the dependencies array to be: [fetchItemBrands]", {"range": "1190", "text": "1191"}, "Update the dependencies array to be: [fetchItemCategories]", {"range": "1192", "text": "1193"}, "Update the dependencies array to be: [fetchUnitsOfMeasure]", {"range": "1194", "text": "1195"}, "Update the dependencies array to be: [fetchItemQualities]", {"range": "1196", "text": "1197"}, "Update the dependencies array to be: [fetchItemSizes]", {"range": "1198", "text": "1199"}, "Update the dependencies array to be: [fetchData]", {"range": "1200", "text": "1201"}, "Update the dependencies array to be: [loadItemMasters, page, rowsPerPage, searchTerm]", {"range": "1202", "text": "1203"}, "Update the dependencies array to be: [page, rowsPerPage, searchTerm, typeFilter, categoryFilter, fetchItemMasters]", {"range": "1204", "text": "1205"}, "Update the dependencies array to be: [fetchBatchItems, page, rowsPerPage, searchTerm, statusFilter]", {"range": "1206", "text": "1207"}, "Update the dependencies array to be: [id, loadItemMaster, loadRelatedData]", {"range": "1208", "text": "1209"}, "Update the dependencies array to be: [loadDropdownOptions]", {"range": "1210", "text": "1211"}, "Update the dependencies array to be: [id, isEdit, loadItemMaster]", {"range": "1212", "text": "1213"}, "Update the dependencies array to be: [id, isEdit, loadDropdowns]", {"range": "1214", "text": "1215"}, "Update the dependencies array to be: [dropdownsLoaded, isEdit, id, loadBatchItem]", {"range": "1216", "text": "1217"}, "Update the dependencies array to be: [loadBatches, page, rowsPerPage, searchTerm]", {"range": "1218", "text": "1219"}, "Update the dependencies array to be: [id, loadBatchItem, loadInventoryItems]", {"range": "1220", "text": "1221"}, "Update the dependencies array to be: [id, isEdit, loadDropdownOptions, loadItemMaster]", {"range": "1222", "text": "1223"}, "Update the dependencies array to be: [id, isEdit, loadDropdownOptions, loadInventoryItem]", {"range": "1224", "text": "1225"}, "removeEscape", {"range": "1226", "text": "1107"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1227", "text": "1228"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [fetchInventoryItems, page, rowsPerPage, searchTerm, statusFilter]", {"range": "1229", "text": "1230"}, "Update the dependencies array to be: [loadData]", {"range": "1231", "text": "1232"}, "Update the dependencies array to be: [testAllEndpoints]", {"range": "1233", "text": "1234"}, "Update the dependencies array to be: [fetchInventoryItem, id]", {"range": "1235", "text": "1236"}, "Update the dependencies array to be: [formik, formik.values.organization, selectedOrgId]", {"range": "1237", "text": "1238"}, "directive", "", "Update the dependencies array to be: [fetchItems]", {"range": "1239", "text": "1240"}, "Update the dependencies array to be: [fetchSupplier, open, supplierId]", {"range": "1241", "text": "1242"}, "Update the dependencies array to be: [page, rowsPerPage, searchTerm, statusFilter, departmentFilter, fetchForms]", {"range": "1243", "text": "1244"}, "Update the dependencies array to be: [fetchForm, id]", {"range": "1245", "text": "1246"}, "Update the dependencies array to be: [fetchAvailableItems, fetchStoresAndShelves]", {"range": "1247", "text": "1248"}, {"range": "1249", "text": "1107"}, {"range": "1250", "text": "1228"}, {"range": "1251", "text": "1107"}, {"range": "1252", "text": "1228"}, {"range": "1253", "text": "1107"}, {"range": "1254", "text": "1228"}, {"range": "1255", "text": "1107"}, {"range": "1256", "text": "1228"}, {"range": "1257", "text": "1107"}, {"range": "1258", "text": "1228"}, {"range": "1259", "text": "1107"}, {"range": "1260", "text": "1228"}, {"range": "1261", "text": "1107"}, {"range": "1262", "text": "1228"}, {"range": "1263", "text": "1107"}, {"range": "1264", "text": "1228"}, {"range": "1265", "text": "1107"}, {"range": "1266", "text": "1228"}, {"range": "1267", "text": "1107"}, {"range": "1268", "text": "1228"}, {"range": "1269", "text": "1107"}, {"range": "1270", "text": "1228"}, {"range": "1271", "text": "1107"}, {"range": "1272", "text": "1228"}, {"range": "1273", "text": "1107"}, {"range": "1274", "text": "1228"}, {"range": "1275", "text": "1107"}, {"range": "1276", "text": "1228"}, {"range": "1277", "text": "1107"}, {"range": "1278", "text": "1228"}, {"range": "1279", "text": "1107"}, {"range": "1280", "text": "1228"}, {"range": "1281", "text": "1107"}, {"range": "1282", "text": "1228"}, {"range": "1283", "text": "1107"}, {"range": "1284", "text": "1228"}, {"range": "1285", "text": "1107"}, {"range": "1286", "text": "1228"}, {"range": "1287", "text": "1107"}, {"range": "1288", "text": "1228"}, {"range": "1289", "text": "1107"}, {"range": "1290", "text": "1228"}, {"range": "1291", "text": "1107"}, {"range": "1292", "text": "1228"}, {"range": "1293", "text": "1107"}, {"range": "1294", "text": "1228"}, "Update the dependencies array to be: [id, isEdit, loadCategories, loadVoucher]", {"range": "1295", "text": "1296"}, {"range": "1297", "text": "1232"}, "Update the dependencies array to be: [loadStoreTypes]", {"range": "1298", "text": "1299"}, {"range": "1300", "text": "1232"}, [1902, 1904], "[fetchOrgTypes]", [1929, 1931], "[fetchOrganizations]", [1811, 1813], "[fetchOffices]", [1504, 1508], "[fetchOrganizationDetails, id]", [7436, 7438], "[dashboardCards]", [1347, 1378], "[fetchSupplierTypes, page, rowsPerPage, searchTerm]", [7227, 7229], "[baseCards]", [1402, 1404], "[fetchItemTypes]", [1575, 1577], "[fetchEntryModes]", [1900, 1902], "[fetchClassifications]", [1447, 1449], "[fetchItemBrands]", [1439, 1441], "[fetchItemCategories]", [1448, 1450], "[fetchUnitsOfMeasure]", [1493, 1495], "[fetchItemQualities]", [1410, 1412], "[fetchItemSizes]", [2033, 2035], "[fetchData]", [1688, 1719], "[loadItemMasters, page, rowsPerPage, searchTerm]", [2079, 2138], "[page, rowsPerPage, searchTerm, typeFilter, categoryFilter, fetchItemMasters]", [2237, 2282], "[fetchBatchItems, page, rowsPerPage, searchTerm, statusFilter]", [2593, 2597], "[id, loadItemMaster, loadRelatedData]", [3963, 3965], "[loadDropdownOptions]", [4075, 4087], "[id, isEdit, loadItemMaster]", [12532, 12544], "[id, isEdit, loadDropdowns]", [12752, 12781], "[dropdownsLoaded, isEdit, id, loadBatchItem]", [2003, 2034], "[loadBatches, page, rowsPerPage, searchTerm]", [1638, 1642], "[id, loadBatchItem, loadInventoryItems]", [18848, 18860], "[id, isEdit, loadDropdownOptions, loadItemMaster]", [2957, 2969], "[id, isEdit, loadDropdownOptions, loadInventoryItem]", [9121, 9122], [9121, 9121], "\\", [1883, 1928], "[fetchInventoryItems, page, rowsPerPage, searchTerm, statusFilter]", [1338, 1340], "[loadData]", [2781, 2783], "[testAllEndpoints]", [1336, 1340], "[fetchInventoryItem, id]", [4428, 4471], "[formik, formik.values.organization, selectedOrgId]", [3014, 3016], "[fetchItems]", [1010, 1028], "[fetchSupplier, open, supplierId]", [1750, 1813], "[page, rowsPerPage, searchTerm, statusFilter, departmentFilter, fetchForms]", [1165, 1169], "[fetchForm, id]", [1757, 1759], "[fetchAvailableItems, fetchStoresAndShelves]", [651, 652], [651, 651], [696, 697], [696, 696], [730, 731], [730, 730], [732, 733], [732, 732], [800, 801], [800, 800], [937, 938], [937, 937], [939, 940], [939, 939], [941, 942], [941, 941], [943, 944], [943, 943], [945, 946], [945, 945], [947, 948], [947, 947], [979, 980], [979, 979], [981, 982], [981, 981], [983, 984], [983, 983], [985, 986], [985, 985], [987, 988], [987, 987], [989, 990], [989, 989], [10454, 10455], [10454, 10454], [10456, 10457], [10456, 10456], [10458, 10459], [10458, 10458], [10460, 10461], [10460, 10460], [10462, 10463], [10462, 10462], [10464, 10465], [10464, 10464], [1314, 1326], "[id, isEdit, loadCategories, loadVoucher]", [1618, 1620], [1350, 1352], "[loadStoreTypes]", [1510, 1512]]