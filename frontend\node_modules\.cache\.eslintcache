[{"D:\\Projects v2\\asset management\\frontend\\src\\index.js": "1", "D:\\Projects v2\\asset management\\frontend\\src\\reportWebVitals.js": "2", "D:\\Projects v2\\asset management\\frontend\\src\\App.js": "3", "D:\\Projects v2\\asset management\\frontend\\src\\theme\\designSystem.js": "4", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandler.js": "5", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ErrorBoundary.js": "6", "D:\\Projects v2\\asset management\\frontend\\src\\components\\Layout.js": "7", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeList.js": "8", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\OrganizationMenu.js": "9", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationList.js": "10", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeList.js": "11", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDetail.js": "12", "D:\\Projects v2\\asset management\\frontend\\src\\features\\suppliers\\SuppliersMenu.js": "13", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ApprovalStatusPage.js": "14", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupDashboard.js": "15", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierTypesList.js": "16", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemStatusPage.js": "17", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupMenu.js": "18", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTagPage.js": "19", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\PropertyStatusPage.js": "20", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTypePage.js": "21", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\EntryModePage.js": "22", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\MainClassificationPage.js": "23", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemBrandPage.js": "24", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemCategoryPage.js": "25", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\UnitOfMeasurePage.js": "26", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemQualityPage.js": "27", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemSizePage.js": "28", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\SubClassificationPage.js": "29", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\GenericInventoryPage.js": "30", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemsDashboard.js": "31", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMastersList.js": "32", "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\MainDashboardMenu.js": "33", "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\Dashboard.js": "34", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardItemMastersList.js": "35", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardBatchItemsList.js": "36", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMasterDetail.js": "37", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemManagementMenu.js": "38", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ProfessionalItemMasterForm.js": "39", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemForm.js": "40", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemsList.js": "41", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemDetail.js": "42", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ModernItemMasterForm.js": "43", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemForm.js": "44", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemsList.js": "45", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVouchersList.js": "46", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\EndToEndTest.js": "47", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\DebugInventoryAPI.js": "48", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemDetail.js": "49", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\MaintenanceSchedule.js": "50", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\index.js": "51", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\index.js": "52", "D:\\Projects v2\\asset management\\frontend\\src\\features\\debug\\APITest.js": "53", "D:\\Projects v2\\asset management\\frontend\\src\\features\\auth\\Login.js": "54", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\index.js": "55", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\axios.js": "56", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandling.js": "57", "D:\\Projects v2\\asset management\\frontend\\src\\theme\\professionalTheme.js": "58", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeDialog.js": "59", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeDialog.js": "60", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDialog.js": "61", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\permissions.js": "62", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\StatusManagement.js": "63", "D:\\Projects v2\\asset management\\frontend\\src\\components\\DashboardBanner.js": "64", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\GatesList.js": "65", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SuppliersList.js": "66", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDetailView.js": "67", "D:\\Projects v2\\asset management\\frontend\\src\\services\\auth.js": "68", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernSidebar.js": "69", "D:\\Projects v2\\asset management\\frontend\\src\\services\\organizations.js": "70", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernBreadcrumbs.js": "71", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernCardGrid.js": "72", "D:\\Projects v2\\asset management\\frontend\\src\\services\\suppliers.js": "73", "D:\\Projects v2\\asset management\\frontend\\src\\services\\inventorySetup.js": "74", "D:\\Projects v2\\asset management\\frontend\\src\\services\\items.js": "75", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDashboard.js": "76", "D:\\Projects v2\\asset management\\frontend\\src\\services\\supplier.js": "77", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierCategoriesList.js": "78", "D:\\Projects v2\\asset management\\frontend\\src\\config\\itemManagementDesign.js": "79", "D:\\Projects v2\\asset management\\frontend\\src\\config\\formConfig.js": "80", "D:\\Projects v2\\asset management\\frontend\\src\\config\\constants.js": "81", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormTemplate.js": "82", "D:\\Projects v2\\asset management\\frontend\\src\\services\\model19.js": "83", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormsList.js": "84", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19Menu.js": "85", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormDetail.js": "86", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormCreate.js": "87", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\filters.js": "88", "D:\\Projects v2\\asset management\\frontend\\src\\components\\forms\\FormValidation.js": "89", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\validation.js": "90", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StorageMenu.js": "91", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoragePage.js": "92", "D:\\Projects v2\\asset management\\frontend\\src\\features\\analytics\\AnalyticsMenu.js": "93", "D:\\Projects v2\\asset management\\frontend\\src\\features\\help\\HelpMenu.js": "94", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVoucherForm.js": "95", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoresList.js": "96", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoreTypesList.js": "97", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\ShelvesList.js": "98", "D:\\Projects v2\\asset management\\frontend\\src\\features\\suppliers\\SuppliersPage.js": "99", "D:\\Projects v2\\asset management\\frontend\\src\\features\\suppliers\\SupplierTypesList.js": "100", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemManagementDashboard.js": "101", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StorageManagementDashboard.js": "102"}, {"size": 618, "mtime": 1749121146000, "results": "103", "hashOfConfig": "104"}, {"size": 362, "mtime": 1744151842000, "results": "105", "hashOfConfig": "104"}, {"size": 37179, "mtime": 1751052364603, "results": "106", "hashOfConfig": "104"}, {"size": 7358, "mtime": 1749460684000, "results": "107", "hashOfConfig": "104"}, {"size": 14601, "mtime": 1750941396982, "results": "108", "hashOfConfig": "104"}, {"size": 7808, "mtime": 1750503868000, "results": "109", "hashOfConfig": "104"}, {"size": 18491, "mtime": 1750062608000, "results": "110", "hashOfConfig": "104"}, {"size": 7248, "mtime": 1750100376000, "results": "111", "hashOfConfig": "104"}, {"size": 6567, "mtime": 1750588146000, "results": "112", "hashOfConfig": "104"}, {"size": 7649, "mtime": 1750100362000, "results": "113", "hashOfConfig": "104"}, {"size": 7291, "mtime": 1750100348000, "results": "114", "hashOfConfig": "104"}, {"size": 20275, "mtime": 1750497690000, "results": "115", "hashOfConfig": "104"}, {"size": 5583, "mtime": 1751052016598, "results": "116", "hashOfConfig": "104"}, {"size": 1543, "mtime": 1749321316000, "results": "117", "hashOfConfig": "104"}, {"size": 17426, "mtime": 1750482586000, "results": "118", "hashOfConfig": "104"}, {"size": 9822, "mtime": 1750587406000, "results": "119", "hashOfConfig": "104"}, {"size": 1446, "mtime": 1749321274000, "results": "120", "hashOfConfig": "104"}, {"size": 14437, "mtime": 1750573144000, "results": "121", "hashOfConfig": "104"}, {"size": 1939, "mtime": 1749321336000, "results": "122", "hashOfConfig": "104"}, {"size": 1456, "mtime": 1749321296000, "results": "123", "hashOfConfig": "104"}, {"size": 12481, "mtime": 1750495760000, "results": "124", "hashOfConfig": "104"}, {"size": 14492, "mtime": 1750485988000, "results": "125", "hashOfConfig": "104"}, {"size": 16270, "mtime": 1750495734000, "results": "126", "hashOfConfig": "104"}, {"size": 13843, "mtime": 1750495814000, "results": "127", "hashOfConfig": "104"}, {"size": 12194, "mtime": 1750485914000, "results": "128", "hashOfConfig": "104"}, {"size": 12917, "mtime": 1750497900000, "results": "129", "hashOfConfig": "104"}, {"size": 13503, "mtime": 1750486292000, "results": "130", "hashOfConfig": "104"}, {"size": 11930, "mtime": 1750486214000, "results": "131", "hashOfConfig": "104"}, {"size": 16201, "mtime": 1750485170000, "results": "132", "hashOfConfig": "104"}, {"size": 6298, "mtime": 1749460684000, "results": "133", "hashOfConfig": "104"}, {"size": 14558, "mtime": 1749494266000, "results": "134", "hashOfConfig": "104"}, {"size": 13420, "mtime": 1750947652189, "results": "135", "hashOfConfig": "104"}, {"size": 6934, "mtime": 1750590990000, "results": "136", "hashOfConfig": "104"}, {"size": 11114, "mtime": 1749460684000, "results": "137", "hashOfConfig": "104"}, {"size": 21268, "mtime": 1750532258000, "results": "138", "hashOfConfig": "104"}, {"size": 24046, "mtime": 1750503298000, "results": "139", "hashOfConfig": "104"}, {"size": 38782, "mtime": 1749541954000, "results": "140", "hashOfConfig": "104"}, {"size": 12462, "mtime": 1750573110000, "results": "141", "hashOfConfig": "104"}, {"size": 45490, "mtime": 1750947122266, "results": "142", "hashOfConfig": "104"}, {"size": 37780, "mtime": 1750999336205, "results": "143", "hashOfConfig": "104"}, {"size": 19827, "mtime": 1749538128000, "results": "144", "hashOfConfig": "104"}, {"size": 25161, "mtime": 1749538944000, "results": "145", "hashOfConfig": "104"}, {"size": 39922, "mtime": 1750999036332, "results": "146", "hashOfConfig": "104"}, {"size": 36856, "mtime": 1750578874000, "results": "147", "hashOfConfig": "104"}, {"size": 19936, "mtime": 1750592184000, "results": "148", "hashOfConfig": "104"}, {"size": 11045, "mtime": 1751004656405, "results": "149", "hashOfConfig": "104"}, {"size": 12291, "mtime": 1749576472000, "results": "150", "hashOfConfig": "104"}, {"size": 6165, "mtime": 1749574028000, "results": "151", "hashOfConfig": "104"}, {"size": 18044, "mtime": 1750499802000, "results": "152", "hashOfConfig": "104"}, {"size": 1622, "mtime": 1749493532000, "results": "153", "hashOfConfig": "104"}, {"size": 323, "mtime": 1750588036000, "results": "154", "hashOfConfig": "104"}, {"size": 430, "mtime": 1750587482000, "results": "155", "hashOfConfig": "104"}, {"size": 6925, "mtime": 1750572170000, "results": "156", "hashOfConfig": "104"}, {"size": 15577, "mtime": 1749244308000, "results": "157", "hashOfConfig": "104"}, {"size": 415, "mtime": 1750592080000, "results": "158", "hashOfConfig": "104"}, {"size": 1263, "mtime": 1749243990000, "results": "159", "hashOfConfig": "104"}, {"size": 8607, "mtime": 1750503624000, "results": "160", "hashOfConfig": "104"}, {"size": 7700, "mtime": 1750061828000, "results": "161", "hashOfConfig": "104"}, {"size": 8779, "mtime": 1744341666000, "results": "162", "hashOfConfig": "104"}, {"size": 3425, "mtime": 1744162122000, "results": "163", "hashOfConfig": "104"}, {"size": 14617, "mtime": 1749196086000, "results": "164", "hashOfConfig": "104"}, {"size": 8029, "mtime": 1749493422000, "results": "165", "hashOfConfig": "104"}, {"size": 28449, "mtime": 1750482954000, "results": "166", "hashOfConfig": "104"}, {"size": 12377, "mtime": 1749244482000, "results": "167", "hashOfConfig": "104"}, {"size": 14628, "mtime": 1750497014000, "results": "168", "hashOfConfig": "104"}, {"size": 35316, "mtime": 1749460684000, "results": "169", "hashOfConfig": "104"}, {"size": 16392, "mtime": 1749259392000, "results": "170", "hashOfConfig": "104"}, {"size": 1272, "mtime": 1748021328000, "results": "171", "hashOfConfig": "104"}, {"size": 11325, "mtime": 1749460684000, "results": "172", "hashOfConfig": "104"}, {"size": 7867, "mtime": 1750670878000, "results": "173", "hashOfConfig": "104"}, {"size": 6155, "mtime": 1749460684000, "results": "174", "hashOfConfig": "104"}, {"size": 11465, "mtime": 1749460684000, "results": "175", "hashOfConfig": "104"}, {"size": 6947, "mtime": 1750587052000, "results": "176", "hashOfConfig": "104"}, {"size": 6776, "mtime": 1750943098693, "results": "177", "hashOfConfig": "104"}, {"size": 9892, "mtime": 1750573370000, "results": "178", "hashOfConfig": "104"}, {"size": 14652, "mtime": 1749486778000, "results": "179", "hashOfConfig": "104"}, {"size": 7916, "mtime": 1749460684000, "results": "180", "hashOfConfig": "104"}, {"size": 9563, "mtime": 1749258762000, "results": "181", "hashOfConfig": "104"}, {"size": 7273, "mtime": 1750502106000, "results": "182", "hashOfConfig": "104"}, {"size": 11010, "mtime": 1750957135442, "results": "183", "hashOfConfig": "104"}, {"size": 2951, "mtime": 1750590930000, "results": "184", "hashOfConfig": "104"}, {"size": 15459, "mtime": 1750672966000, "results": "185", "hashOfConfig": "104"}, {"size": 8010, "mtime": 1750626786000, "results": "186", "hashOfConfig": "104"}, {"size": 16382, "mtime": 1750591964000, "results": "187", "hashOfConfig": "104"}, {"size": 7365, "mtime": 1750591030000, "results": "188", "hashOfConfig": "104"}, {"size": 12742, "mtime": 1750591874000, "results": "189", "hashOfConfig": "104"}, {"size": 24262, "mtime": 1750755554000, "results": "190", "hashOfConfig": "104"}, {"size": 2271, "mtime": 1744341630000, "results": "191", "hashOfConfig": "104"}, {"size": 9322, "mtime": 1749321190000, "results": "192", "hashOfConfig": "104"}, {"size": 14293, "mtime": 1750940433844, "results": "193", "hashOfConfig": "104"}, {"size": 8154, "mtime": 1751051370464, "results": "194", "hashOfConfig": "104"}, {"size": 5411, "mtime": 1751051491333, "results": "195", "hashOfConfig": "104"}, {"size": 8739, "mtime": 1750999786596, "results": "196", "hashOfConfig": "104"}, {"size": 9579, "mtime": 1750999827928, "results": "197", "hashOfConfig": "104"}, {"size": 12709, "mtime": 1751004618727, "results": "198", "hashOfConfig": "104"}, {"size": 16413, "mtime": 1751004754752, "results": "199", "hashOfConfig": "104"}, {"size": 11351, "mtime": 1751004706550, "results": "200", "hashOfConfig": "104"}, {"size": 15638, "mtime": 1751051432766, "results": "201", "hashOfConfig": "104"}, {"size": 5775, "mtime": 1751051998716, "results": "202", "hashOfConfig": "104"}, {"size": 14776, "mtime": 1751051953382, "results": "203", "hashOfConfig": "104"}, {"size": 9665, "mtime": 1751052064010, "results": "204", "hashOfConfig": "104"}, {"size": 9784, "mtime": 1751052132402, "results": "205", "hashOfConfig": "104"}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gs8ssj", {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Projects v2\\asset management\\frontend\\src\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\App.js", ["512", "513", "514", "515", "516", "517", "518", "519", "520", "521"], [], "D:\\Projects v2\\asset management\\frontend\\src\\theme\\designSystem.js", ["522"], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandler.js", ["523"], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ErrorBoundary.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\Layout.js", ["524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeList.js", ["549", "550"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\OrganizationMenu.js", ["551"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationList.js", ["552", "553", "554"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeList.js", ["555", "556"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDetail.js", ["557", "558", "559", "560"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\suppliers\\SuppliersMenu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ApprovalStatusPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupDashboard.js", ["561", "562", "563", "564", "565", "566", "567"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierTypesList.js", ["568", "569"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemStatusPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupMenu.js", ["570"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTagPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\PropertyStatusPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTypePage.js", ["571"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\EntryModePage.js", ["572"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\MainClassificationPage.js", ["573", "574", "575"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemBrandPage.js", ["576"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemCategoryPage.js", ["577"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\UnitOfMeasurePage.js", ["578"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemQualityPage.js", ["579"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemSizePage.js", ["580"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\SubClassificationPage.js", ["581", "582", "583"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\GenericInventoryPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemsDashboard.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMastersList.js", ["584"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\MainDashboardMenu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\Dashboard.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardItemMastersList.js", ["585", "586", "587", "588", "589", "590"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardBatchItemsList.js", ["591", "592", "593", "594"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMasterDetail.js", ["595", "596", "597", "598", "599", "600", "601"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemManagementMenu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ProfessionalItemMasterForm.js", ["602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemForm.js", ["617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemsList.js", ["628", "629", "630"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemDetail.js", ["631", "632", "633"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ModernItemMasterForm.js", ["634", "635", "636", "637", "638", "639", "640", "641", "642"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemForm.js", ["643", "644", "645", "646", "647", "648", "649"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemsList.js", ["650", "651", "652"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVouchersList.js", ["653", "654", "655", "656", "657"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\EndToEndTest.js", ["658", "659"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\DebugInventoryAPI.js", ["660"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemDetail.js", ["661"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\MaintenanceSchedule.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\debug\\APITest.js", ["662"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\auth\\Login.js", ["663"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\axios.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandling.js", ["664"], [], "D:\\Projects v2\\asset management\\frontend\\src\\theme\\professionalTheme.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeDialog.js", [], ["665"], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeDialog.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDialog.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\permissions.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\StatusManagement.js", ["666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677"], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\DashboardBanner.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\GatesList.js", ["678", "679", "680"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SuppliersList.js", ["681"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDetailView.js", ["682", "683"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\auth.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernSidebar.js", ["684", "685"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\organizations.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernBreadcrumbs.js", ["686"], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernCardGrid.js", ["687", "688"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\suppliers.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\inventorySetup.js", ["689"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\items.js", ["690"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDashboard.js", ["691", "692", "693", "694", "695", "696", "697"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\supplier.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierCategoriesList.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\config\\itemManagementDesign.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\config\\formConfig.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\config\\constants.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormTemplate.js", ["698", "699"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\model19.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormsList.js", ["700", "701"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19Menu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormDetail.js", ["702"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormCreate.js", ["703", "704", "705", "706", "707", "708", "709", "710"], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\filters.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\forms\\FormValidation.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\validation.js", ["711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StorageMenu.js", ["734", "735", "736"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoragePage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\analytics\\AnalyticsMenu.js", ["737"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\help\\HelpMenu.js", ["738"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVoucherForm.js", ["739", "740"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoresList.js", ["741", "742", "743"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoreTypesList.js", ["744", "745"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\ShelvesList.js", ["746", "747"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\suppliers\\SuppliersPage.js", ["748", "749", "750"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\suppliers\\SupplierTypesList.js", ["751", "752"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemManagementDashboard.js", ["753"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StorageManagementDashboard.js", ["754"], [], {"ruleId": "755", "severity": 1, "message": "756", "line": 57, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 57, "endColumn": 19}, {"ruleId": "755", "severity": 1, "message": "759", "line": 60, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 60, "endColumn": 22}, {"ruleId": "755", "severity": 1, "message": "760", "line": 61, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 61, "endColumn": 18}, {"ruleId": "755", "severity": 1, "message": "761", "line": 62, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 62, "endColumn": 19}, {"ruleId": "755", "severity": 1, "message": "762", "line": 74, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 74, "endColumn": 23}, {"ruleId": "755", "severity": 1, "message": "763", "line": 77, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 77, "endColumn": 26}, {"ruleId": "755", "severity": 1, "message": "764", "line": 81, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 81, "endColumn": 22}, {"ruleId": "765", "severity": 2, "message": "766", "line": 734, "column": 24, "nodeType": "767", "messageId": "768", "endLine": 734, "endColumn": 37}, {"ruleId": "765", "severity": 2, "message": "769", "line": 744, "column": 24, "nodeType": "767", "messageId": "768", "endLine": 744, "endColumn": 41}, {"ruleId": "765", "severity": 2, "message": "770", "line": 768, "column": 24, "nodeType": "767", "messageId": "768", "endLine": 768, "endColumn": 46}, {"ruleId": "771", "severity": 1, "message": "772", "line": 305, "column": 1, "nodeType": "773", "endLine": 315, "endColumn": 3}, {"ruleId": "755", "severity": 1, "message": "774", "line": 284, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 284, "endColumn": 18}, {"ruleId": "755", "severity": 1, "message": "775", "line": 21, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 21, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "776", "line": 22, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 22, "endColumn": 7}, {"ruleId": "755", "severity": 1, "message": "777", "line": 23, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 23, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "778", "line": 34, "column": 15, "nodeType": "757", "messageId": "758", "endLine": 34, "endColumn": 27}, {"ruleId": "755", "severity": 1, "message": "779", "line": 35, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 35, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "780", "line": 41, "column": 15, "nodeType": "757", "messageId": "758", "endLine": 41, "endColumn": 27}, {"ruleId": "755", "severity": 1, "message": "781", "line": 42, "column": 12, "nodeType": "757", "messageId": "758", "endLine": 42, "endColumn": 28}, {"ruleId": "755", "severity": 1, "message": "782", "line": 43, "column": 12, "nodeType": "757", "messageId": "758", "endLine": 43, "endColumn": 21}, {"ruleId": "755", "severity": 1, "message": "783", "line": 44, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 44, "endColumn": 17}, {"ruleId": "755", "severity": 1, "message": "784", "line": 45, "column": 14, "nodeType": "757", "messageId": "758", "endLine": 45, "endColumn": 25}, {"ruleId": "755", "severity": 1, "message": "785", "line": 46, "column": 16, "nodeType": "757", "messageId": "758", "endLine": 46, "endColumn": 29}, {"ruleId": "755", "severity": 1, "message": "786", "line": 47, "column": 12, "nodeType": "757", "messageId": "758", "endLine": 47, "endColumn": 21}, {"ruleId": "755", "severity": 1, "message": "787", "line": 48, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 48, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "788", "line": 49, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 49, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "789", "line": 50, "column": 18, "nodeType": "757", "messageId": "758", "endLine": 50, "endColumn": 33}, {"ruleId": "755", "severity": 1, "message": "790", "line": 51, "column": 15, "nodeType": "757", "messageId": "758", "endLine": 51, "endColumn": 27}, {"ruleId": "755", "severity": 1, "message": "791", "line": 52, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 52, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "792", "line": 54, "column": 12, "nodeType": "757", "messageId": "758", "endLine": 54, "endColumn": 27}, {"ruleId": "755", "severity": 1, "message": "793", "line": 61, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 61, "endColumn": 16}, {"ruleId": "755", "severity": 1, "message": "794", "line": 61, "column": 18, "nodeType": "757", "messageId": "758", "endLine": 61, "endColumn": 25}, {"ruleId": "755", "severity": 1, "message": "795", "line": 61, "column": 27, "nodeType": "757", "messageId": "758", "endLine": 61, "endColumn": 36}, {"ruleId": "755", "severity": 1, "message": "796", "line": 62, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 62, "endColumn": 21}, {"ruleId": "755", "severity": 1, "message": "797", "line": 63, "column": 18, "nodeType": "757", "messageId": "758", "endLine": 63, "endColumn": 25}, {"ruleId": "755", "severity": 1, "message": "798", "line": 63, "column": 39, "nodeType": "757", "messageId": "758", "endLine": 63, "endColumn": 50}, {"ruleId": "755", "severity": 1, "message": "799", "line": 204, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 204, "endColumn": 21}, {"ruleId": "755", "severity": 1, "message": "800", "line": 36, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 36, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "802", "line": 67, "column": 6, "nodeType": "803", "endLine": 67, "endColumn": 8, "suggestions": "804"}, {"ruleId": "755", "severity": 1, "message": "805", "line": 16, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 16, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "806", "line": 21, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 21, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "800", "line": 40, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 40, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "807", "line": 73, "column": 6, "nodeType": "803", "endLine": 73, "endColumn": 8, "suggestions": "808"}, {"ruleId": "755", "severity": 1, "message": "800", "line": 36, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 36, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "809", "line": 67, "column": 6, "nodeType": "803", "endLine": 67, "endColumn": 8, "suggestions": "810"}, {"ruleId": "755", "severity": 1, "message": "805", "line": 23, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 23, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "811", "line": 40, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 40, "endColumn": 26}, {"ruleId": "755", "severity": 1, "message": "800", "line": 43, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 43, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "812", "line": 64, "column": 6, "nodeType": "803", "endLine": 64, "endColumn": 10, "suggestions": "813"}, {"ruleId": "755", "severity": 1, "message": "814", "line": 13, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 13, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "815", "line": 19, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 19, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "816", "line": 19, "column": 16, "nodeType": "757", "messageId": "758", "endLine": 19, "endColumn": 27}, {"ruleId": "755", "severity": 1, "message": "817", "line": 21, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 21, "endColumn": 22}, {"ruleId": "755", "severity": 1, "message": "800", "line": 64, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 64, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "818", "line": 277, "column": 6, "nodeType": "803", "endLine": 277, "endColumn": 8, "suggestions": "819"}, {"ruleId": "755", "severity": 1, "message": "820", "line": 284, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 284, "endColumn": 19}, {"ruleId": "755", "severity": 1, "message": "815", "line": 11, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 11, "endColumn": 7}, {"ruleId": "801", "severity": 1, "message": "821", "line": 58, "column": 6, "nodeType": "803", "endLine": 58, "endColumn": 37, "suggestions": "822"}, {"ruleId": "801", "severity": 1, "message": "823", "line": 232, "column": 6, "nodeType": "803", "endLine": 232, "endColumn": 8, "suggestions": "824"}, {"ruleId": "801", "severity": 1, "message": "825", "line": 66, "column": 6, "nodeType": "803", "endLine": 66, "endColumn": 8, "suggestions": "826"}, {"ruleId": "801", "severity": 1, "message": "827", "line": 75, "column": 6, "nodeType": "803", "endLine": 75, "endColumn": 8, "suggestions": "828"}, {"ruleId": "755", "severity": 1, "message": "815", "line": 10, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 10, "endColumn": 7}, {"ruleId": "755", "severity": 1, "message": "816", "line": 11, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 11, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "829", "line": 84, "column": 6, "nodeType": "803", "endLine": 84, "endColumn": 8, "suggestions": "830"}, {"ruleId": "801", "severity": 1, "message": "831", "line": 68, "column": 6, "nodeType": "803", "endLine": 68, "endColumn": 8, "suggestions": "832"}, {"ruleId": "801", "severity": 1, "message": "833", "line": 66, "column": 6, "nodeType": "803", "endLine": 66, "endColumn": 8, "suggestions": "834"}, {"ruleId": "801", "severity": 1, "message": "835", "line": 67, "column": 6, "nodeType": "803", "endLine": 67, "endColumn": 8, "suggestions": "836"}, {"ruleId": "801", "severity": 1, "message": "837", "line": 68, "column": 6, "nodeType": "803", "endLine": 68, "endColumn": 8, "suggestions": "838"}, {"ruleId": "801", "severity": 1, "message": "839", "line": 66, "column": 6, "nodeType": "803", "endLine": 66, "endColumn": 8, "suggestions": "840"}, {"ruleId": "755", "severity": 1, "message": "815", "line": 10, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 10, "endColumn": 7}, {"ruleId": "755", "severity": 1, "message": "816", "line": 11, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 11, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "841", "line": 89, "column": 6, "nodeType": "803", "endLine": 89, "endColumn": 8, "suggestions": "842"}, {"ruleId": "801", "severity": 1, "message": "843", "line": 70, "column": 6, "nodeType": "803", "endLine": 70, "endColumn": 37, "suggestions": "844"}, {"ruleId": "755", "severity": 1, "message": "806", "line": 38, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "845", "line": 57, "column": 25, "nodeType": "757", "messageId": "758", "endLine": 57, "endColumn": 38}, {"ruleId": "755", "severity": 1, "message": "846", "line": 57, "column": 56, "nodeType": "757", "messageId": "758", "endLine": 57, "endColumn": 66}, {"ruleId": "755", "severity": 1, "message": "800", "line": 62, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 62, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "847", "line": 85, "column": 6, "nodeType": "803", "endLine": 85, "endColumn": 65, "suggestions": "848"}, {"ruleId": "755", "severity": 1, "message": "849", "line": 103, "column": 13, "nodeType": "757", "messageId": "758", "endLine": 103, "endColumn": 22}, {"ruleId": "755", "severity": 1, "message": "806", "line": 38, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "850", "line": 63, "column": 40, "nodeType": "757", "messageId": "758", "endLine": 63, "endColumn": 54}, {"ruleId": "755", "severity": 1, "message": "800", "line": 67, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 67, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "851", "line": 92, "column": 6, "nodeType": "803", "endLine": 92, "endColumn": 51, "suggestions": "852"}, {"ruleId": "755", "severity": 1, "message": "853", "line": 50, "column": 12, "nodeType": "757", "messageId": "758", "endLine": 50, "endColumn": 21}, {"ruleId": "755", "severity": 1, "message": "854", "line": 54, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 54, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "855", "line": 59, "column": 15, "nodeType": "757", "messageId": "758", "endLine": 59, "endColumn": 27}, {"ruleId": "755", "severity": 1, "message": "856", "line": 61, "column": 14, "nodeType": "757", "messageId": "758", "endLine": 61, "endColumn": 25}, {"ruleId": "755", "severity": 1, "message": "857", "line": 62, "column": 18, "nodeType": "757", "messageId": "758", "endLine": 62, "endColumn": 28}, {"ruleId": "755", "severity": 1, "message": "858", "line": 63, "column": 13, "nodeType": "757", "messageId": "758", "endLine": 63, "endColumn": 25}, {"ruleId": "801", "severity": 1, "message": "859", "line": 105, "column": 6, "nodeType": "803", "endLine": 105, "endColumn": 10, "suggestions": "860"}, {"ruleId": "755", "severity": 1, "message": "861", "line": 29, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 29, "endColumn": 13}, {"ruleId": "755", "severity": 1, "message": "805", "line": 34, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 34, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "862", "line": 35, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 35, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "863", "line": 37, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 37, "endColumn": 13}, {"ruleId": "755", "severity": 1, "message": "814", "line": 38, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "775", "line": 39, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 39, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "856", "line": 57, "column": 14, "nodeType": "757", "messageId": "758", "endLine": 57, "endColumn": 25}, {"ruleId": "755", "severity": 1, "message": "778", "line": 58, "column": 15, "nodeType": "757", "messageId": "758", "endLine": 58, "endColumn": 27}, {"ruleId": "755", "severity": 1, "message": "788", "line": 62, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 62, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "787", "line": 63, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 63, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "864", "line": 122, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 122, "endColumn": 22}, {"ruleId": "755", "severity": 1, "message": "865", "line": 122, "column": 24, "nodeType": "757", "messageId": "758", "endLine": 122, "endColumn": 39}, {"ruleId": "801", "severity": 1, "message": "866", "line": 157, "column": 6, "nodeType": "803", "endLine": 157, "endColumn": 8, "suggestions": "867"}, {"ruleId": "801", "severity": 1, "message": "868", "line": 164, "column": 6, "nodeType": "803", "endLine": 164, "endColumn": 18, "suggestions": "869"}, {"ruleId": "801", "severity": 1, "message": "870", "line": 167, "column": 29, "nodeType": "757", "endLine": 167, "endColumn": 40}, {"ruleId": "755", "severity": 1, "message": "815", "line": 4, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 4, "endColumn": 7}, {"ruleId": "755", "severity": 1, "message": "816", "line": 5, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 5, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "775", "line": 28, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 28, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "779", "line": 39, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 39, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "871", "line": 51, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 51, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "872", "line": 52, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 52, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "873", "line": 54, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 54, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "874", "line": 55, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 55, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "875", "line": 56, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 56, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "876", "line": 452, "column": 6, "nodeType": "803", "endLine": 452, "endColumn": 18, "suggestions": "877"}, {"ruleId": "801", "severity": 1, "message": "878", "line": 460, "column": 6, "nodeType": "803", "endLine": 460, "endColumn": 35, "suggestions": "879"}, {"ruleId": "755", "severity": 1, "message": "815", "line": 23, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 23, "endColumn": 7}, {"ruleId": "755", "severity": 1, "message": "816", "line": 24, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 24, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "880", "line": 86, "column": 6, "nodeType": "803", "endLine": 86, "endColumn": 37, "suggestions": "881"}, {"ruleId": "755", "severity": 1, "message": "882", "line": 34, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 34, "endColumn": 9}, {"ruleId": "755", "severity": 1, "message": "883", "line": 43, "column": 15, "nodeType": "757", "messageId": "758", "endLine": 43, "endColumn": 27}, {"ruleId": "801", "severity": 1, "message": "884", "line": 73, "column": 6, "nodeType": "803", "endLine": 73, "endColumn": 10, "suggestions": "885"}, {"ruleId": "755", "severity": 1, "message": "815", "line": 5, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 5, "endColumn": 7}, {"ruleId": "755", "severity": 1, "message": "816", "line": 6, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 6, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "775", "line": 31, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 31, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "783", "line": 36, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 36, "endColumn": 17}, {"ruleId": "755", "severity": 1, "message": "779", "line": 45, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 45, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "874", "line": 60, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 60, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "875", "line": 61, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 61, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "886", "line": 66, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 66, "endColumn": 21}, {"ruleId": "801", "severity": 1, "message": "887", "line": 626, "column": 6, "nodeType": "803", "endLine": 626, "endColumn": 18, "suggestions": "888"}, {"ruleId": "755", "severity": 1, "message": "805", "line": 25, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 25, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "889", "line": 34, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 34, "endColumn": 7}, {"ruleId": "755", "severity": 1, "message": "806", "line": 36, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 36, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "800", "line": 63, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 63, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "890", "line": 131, "column": 6, "nodeType": "803", "endLine": 131, "endColumn": 18, "suggestions": "891"}, {"ruleId": "892", "severity": 1, "message": "893", "line": 308, "column": 5, "nodeType": "894", "messageId": "895", "endLine": 354, "endColumn": 6}, {"ruleId": "896", "severity": 1, "message": "897", "line": 326, "column": 45, "nodeType": "898", "messageId": "899", "endLine": 326, "endColumn": 46, "suggestions": "900"}, {"ruleId": "755", "severity": 1, "message": "806", "line": 38, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "800", "line": 61, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 61, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "901", "line": 77, "column": 6, "nodeType": "803", "endLine": 77, "endColumn": 51, "suggestions": "902"}, {"ruleId": "755", "severity": 1, "message": "862", "line": 21, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 21, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "903", "line": 29, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 29, "endColumn": 12}, {"ruleId": "755", "severity": 1, "message": "904", "line": 36, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 36, "endColumn": 25}, {"ruleId": "755", "severity": 1, "message": "905", "line": 51, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 51, "endColumn": 20}, {"ruleId": "801", "severity": 1, "message": "906", "line": 58, "column": 6, "nodeType": "803", "endLine": 58, "endColumn": 8, "suggestions": "907"}, {"ruleId": "755", "severity": 1, "message": "908", "line": 5, "column": 27, "nodeType": "757", "messageId": "758", "endLine": 5, "endColumn": 36}, {"ruleId": "755", "severity": 1, "message": "909", "line": 59, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 59, "endColumn": 18}, {"ruleId": "801", "severity": 1, "message": "910", "line": 106, "column": 6, "nodeType": "803", "endLine": 106, "endColumn": 8, "suggestions": "911"}, {"ruleId": "801", "severity": 1, "message": "912", "line": 61, "column": 6, "nodeType": "803", "endLine": 61, "endColumn": 10, "suggestions": "913"}, {"ruleId": "755", "severity": 1, "message": "914", "line": 69, "column": 13, "nodeType": "757", "messageId": "758", "endLine": 69, "endColumn": 21}, {"ruleId": "755", "severity": 1, "message": "915", "line": 12, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 12, "endColumn": 9}, {"ruleId": "771", "severity": 1, "message": "772", "line": 315, "column": 1, "nodeType": "773", "endLine": 327, "endColumn": 3}, {"ruleId": "801", "severity": 1, "message": "916", "line": 129, "column": 6, "nodeType": "803", "endLine": 129, "endColumn": 49, "suggestions": "917", "suppressions": "918"}, {"ruleId": "755", "severity": 1, "message": "919", "line": 33, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 33, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "800", "line": 61, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 61, "endColumn": 14}, {"ruleId": "801", "severity": 1, "message": "920", "line": 123, "column": 6, "nodeType": "803", "endLine": 123, "endColumn": 8, "suggestions": "921"}, {"ruleId": "755", "severity": 1, "message": "922", "line": 211, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 211, "endColumn": 19}, {"ruleId": "923", "severity": 1, "message": "924", "line": 712, "column": 41, "nodeType": "925", "messageId": "926", "endLine": 712, "endColumn": 43}, {"ruleId": "923", "severity": 1, "message": "924", "line": 712, "column": 62, "nodeType": "925", "messageId": "926", "endLine": 712, "endColumn": 64}, {"ruleId": "923", "severity": 1, "message": "927", "line": 712, "column": 62, "nodeType": "925", "messageId": "926", "endLine": 712, "endColumn": 64}, {"ruleId": "923", "severity": 1, "message": "927", "line": 712, "column": 84, "nodeType": "925", "messageId": "926", "endLine": 712, "endColumn": 86}, {"ruleId": "923", "severity": 1, "message": "924", "line": 713, "column": 46, "nodeType": "925", "messageId": "926", "endLine": 713, "endColumn": 48}, {"ruleId": "923", "severity": 1, "message": "924", "line": 713, "column": 65, "nodeType": "925", "messageId": "926", "endLine": 713, "endColumn": 67}, {"ruleId": "923", "severity": 1, "message": "927", "line": 713, "column": 65, "nodeType": "925", "messageId": "926", "endLine": 713, "endColumn": 67}, {"ruleId": "923", "severity": 1, "message": "927", "line": 713, "column": 87, "nodeType": "925", "messageId": "926", "endLine": 713, "endColumn": 89}, {"ruleId": "755", "severity": 1, "message": "815", "line": 4, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 4, "endColumn": 7}, {"ruleId": "755", "severity": 1, "message": "816", "line": 5, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 5, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "800", "line": 49, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 49, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "928", "line": 371, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 371, "endColumn": 29}, {"ruleId": "755", "severity": 1, "message": "929", "line": 14, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 14, "endColumn": 16}, {"ruleId": "801", "severity": 1, "message": "930", "line": 49, "column": 6, "nodeType": "803", "endLine": 49, "endColumn": 24, "suggestions": "931"}, {"ruleId": "755", "severity": 1, "message": "805", "line": 16, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 16, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "800", "line": 38, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "800", "line": 24, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 24, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "795", "line": 26, "column": 27, "nodeType": "757", "messageId": "758", "endLine": 26, "endColumn": 36}, {"ruleId": "755", "severity": 1, "message": "800", "line": 38, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 14}, {"ruleId": "771", "severity": 1, "message": "772", "line": 152, "column": 1, "nodeType": "773", "endLine": 185, "endColumn": 3}, {"ruleId": "771", "severity": 1, "message": "772", "line": 345, "column": 1, "nodeType": "773", "endLine": 352, "endColumn": 3}, {"ruleId": "755", "severity": 1, "message": "932", "line": 22, "column": 16, "nodeType": "757", "messageId": "758", "endLine": 22, "endColumn": 29}, {"ruleId": "755", "severity": 1, "message": "854", "line": 23, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 23, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "933", "line": 24, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 24, "endColumn": 19}, {"ruleId": "755", "severity": 1, "message": "934", "line": 25, "column": 13, "nodeType": "757", "messageId": "758", "endLine": 25, "endColumn": 23}, {"ruleId": "755", "severity": 1, "message": "783", "line": 28, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 28, "endColumn": 17}, {"ruleId": "755", "severity": 1, "message": "935", "line": 29, "column": 15, "nodeType": "757", "messageId": "758", "endLine": 29, "endColumn": 27}, {"ruleId": "755", "severity": 1, "message": "936", "line": 163, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 163, "endColumn": 17}, {"ruleId": "755", "severity": 1, "message": "805", "line": 18, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 18, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "937", "line": 62, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 62, "endColumn": 21}, {"ruleId": "755", "severity": 1, "message": "815", "line": 11, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 11, "endColumn": 7}, {"ruleId": "801", "severity": 1, "message": "938", "line": 78, "column": 6, "nodeType": "803", "endLine": 78, "endColumn": 69, "suggestions": "939"}, {"ruleId": "801", "severity": 1, "message": "940", "line": 54, "column": 6, "nodeType": "803", "endLine": 54, "endColumn": 10, "suggestions": "941"}, {"ruleId": "755", "severity": 1, "message": "815", "line": 34, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 34, "endColumn": 7}, {"ruleId": "755", "severity": 1, "message": "816", "line": 35, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 35, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "863", "line": 37, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 37, "endColumn": 13}, {"ruleId": "755", "severity": 1, "message": "814", "line": 38, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "783", "line": 44, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 44, "endColumn": 17}, {"ruleId": "755", "severity": 1, "message": "942", "line": 45, "column": 13, "nodeType": "757", "messageId": "758", "endLine": 45, "endColumn": 23}, {"ruleId": "755", "severity": 1, "message": "943", "line": 54, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 54, "endColumn": 22}, {"ruleId": "801", "severity": 1, "message": "944", "line": 79, "column": 6, "nodeType": "803", "endLine": 79, "endColumn": 8, "suggestions": "945"}, {"ruleId": "896", "severity": 1, "message": "897", "line": 31, "column": 17, "nodeType": "898", "messageId": "899", "endLine": 31, "endColumn": 18, "suggestions": "946"}, {"ruleId": "896", "severity": 1, "message": "897", "line": 32, "column": 33, "nodeType": "898", "messageId": "899", "endLine": 32, "endColumn": 34, "suggestions": "947"}, {"ruleId": "896", "severity": 1, "message": "948", "line": 33, "column": 22, "nodeType": "898", "messageId": "899", "endLine": 33, "endColumn": 23, "suggestions": "949"}, {"ruleId": "896", "severity": 1, "message": "950", "line": 33, "column": 24, "nodeType": "898", "messageId": "899", "endLine": 33, "endColumn": 25, "suggestions": "951"}, {"ruleId": "896", "severity": 1, "message": "897", "line": 35, "column": 27, "nodeType": "898", "messageId": "899", "endLine": 35, "endColumn": 28, "suggestions": "952"}, {"ruleId": "896", "severity": 1, "message": "953", "line": 38, "column": 33, "nodeType": "898", "messageId": "899", "endLine": 38, "endColumn": 34, "suggestions": "954"}, {"ruleId": "896", "severity": 1, "message": "955", "line": 38, "column": 35, "nodeType": "898", "messageId": "899", "endLine": 38, "endColumn": 36, "suggestions": "956"}, {"ruleId": "896", "severity": 1, "message": "948", "line": 38, "column": 37, "nodeType": "898", "messageId": "899", "endLine": 38, "endColumn": 38, "suggestions": "957"}, {"ruleId": "896", "severity": 1, "message": "950", "line": 38, "column": 39, "nodeType": "898", "messageId": "899", "endLine": 38, "endColumn": 40, "suggestions": "958"}, {"ruleId": "896", "severity": 1, "message": "959", "line": 38, "column": 41, "nodeType": "898", "messageId": "899", "endLine": 38, "endColumn": 42, "suggestions": "960"}, {"ruleId": "896", "severity": 1, "message": "961", "line": 38, "column": 43, "nodeType": "898", "messageId": "899", "endLine": 38, "endColumn": 44, "suggestions": "962"}, {"ruleId": "896", "severity": 1, "message": "953", "line": 39, "column": 25, "nodeType": "898", "messageId": "899", "endLine": 39, "endColumn": 26, "suggestions": "963"}, {"ruleId": "896", "severity": 1, "message": "955", "line": 39, "column": 27, "nodeType": "898", "messageId": "899", "endLine": 39, "endColumn": 28, "suggestions": "964"}, {"ruleId": "896", "severity": 1, "message": "948", "line": 39, "column": 29, "nodeType": "898", "messageId": "899", "endLine": 39, "endColumn": 30, "suggestions": "965"}, {"ruleId": "896", "severity": 1, "message": "950", "line": 39, "column": 31, "nodeType": "898", "messageId": "899", "endLine": 39, "endColumn": 32, "suggestions": "966"}, {"ruleId": "896", "severity": 1, "message": "959", "line": 39, "column": 33, "nodeType": "898", "messageId": "899", "endLine": 39, "endColumn": 34, "suggestions": "967"}, {"ruleId": "896", "severity": 1, "message": "961", "line": 39, "column": 35, "nodeType": "898", "messageId": "899", "endLine": 39, "endColumn": 36, "suggestions": "968"}, {"ruleId": "896", "severity": 1, "message": "953", "line": 385, "column": 55, "nodeType": "898", "messageId": "899", "endLine": 385, "endColumn": 56, "suggestions": "969"}, {"ruleId": "896", "severity": 1, "message": "955", "line": 385, "column": 57, "nodeType": "898", "messageId": "899", "endLine": 385, "endColumn": 58, "suggestions": "970"}, {"ruleId": "896", "severity": 1, "message": "948", "line": 385, "column": 59, "nodeType": "898", "messageId": "899", "endLine": 385, "endColumn": 60, "suggestions": "971"}, {"ruleId": "896", "severity": 1, "message": "950", "line": 385, "column": 61, "nodeType": "898", "messageId": "899", "endLine": 385, "endColumn": 62, "suggestions": "972"}, {"ruleId": "896", "severity": 1, "message": "959", "line": 385, "column": 63, "nodeType": "898", "messageId": "899", "endLine": 385, "endColumn": 64, "suggestions": "973"}, {"ruleId": "896", "severity": 1, "message": "961", "line": 385, "column": 65, "nodeType": "898", "messageId": "899", "endLine": 385, "endColumn": 66, "suggestions": "974"}, {"ruleId": "755", "severity": 1, "message": "975", "line": 19, "column": 16, "nodeType": "757", "messageId": "758", "endLine": 19, "endColumn": 29}, {"ruleId": "755", "severity": 1, "message": "932", "line": 25, "column": 16, "nodeType": "757", "messageId": "758", "endLine": 25, "endColumn": 29}, {"ruleId": "755", "severity": 1, "message": "936", "line": 32, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 32, "endColumn": 17}, {"ruleId": "755", "severity": 1, "message": "976", "line": 1, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 1, "endColumn": 18}, {"ruleId": "755", "severity": 1, "message": "976", "line": 1, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 1, "endColumn": 18}, {"ruleId": "755", "severity": 1, "message": "862", "line": 9, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 9, "endColumn": 8}, {"ruleId": "801", "severity": 1, "message": "977", "line": 60, "column": 6, "nodeType": "803", "endLine": 60, "endColumn": 18, "suggestions": "978"}, {"ruleId": "755", "severity": 1, "message": "904", "line": 41, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 41, "endColumn": 25}, {"ruleId": "755", "severity": 1, "message": "936", "line": 49, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 49, "endColumn": 17}, {"ruleId": "801", "severity": 1, "message": "906", "line": 73, "column": 6, "nodeType": "803", "endLine": 73, "endColumn": 8, "suggestions": "979"}, {"ruleId": "755", "severity": 1, "message": "936", "line": 43, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 43, "endColumn": 17}, {"ruleId": "801", "severity": 1, "message": "980", "line": 59, "column": 6, "nodeType": "803", "endLine": 59, "endColumn": 8, "suggestions": "981"}, {"ruleId": "755", "severity": 1, "message": "936", "line": 49, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 49, "endColumn": 17}, {"ruleId": "801", "severity": 1, "message": "906", "line": 70, "column": 6, "nodeType": "803", "endLine": 70, "endColumn": 8, "suggestions": "982"}, {"ruleId": "755", "severity": 1, "message": "983", "line": 10, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 10, "endColumn": 7}, {"ruleId": "755", "severity": 1, "message": "815", "line": 11, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 11, "endColumn": 7}, {"ruleId": "755", "severity": 1, "message": "816", "line": 12, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 12, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "936", "line": 46, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 46, "endColumn": 17}, {"ruleId": "801", "severity": 1, "message": "984", "line": 64, "column": 6, "nodeType": "803", "endLine": 64, "endColumn": 8, "suggestions": "985"}, {"ruleId": "755", "severity": 1, "message": "986", "line": 38, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 26}, {"ruleId": "755", "severity": 1, "message": "986", "line": 38, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 26}, "no-unused-vars", "'StorageMenu' is defined but never used.", "Identifier", "unusedVar", "'StoreTypesList' is defined but never used.", "'StoresList' is defined but never used.", "'ShelvesList' is defined but never used.", "'ItemMastersList' is defined but never used.", "'ItemManagementMenu' is defined but never used.", "'BatchItemsList' is defined but never used.", "react/jsx-no-undef", "'SuppliersList' is not defined.", "JSXIdentifier", "undefined", "'SupplierDashboard' is not defined.", "'SupplierCategoriesList' is not defined.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'logData' is assigned a value but never used.", "'Collapse' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'CategoryIcon' is defined but never used.", "'AssignmentIcon' is defined but never used.", "'ItemTypeIcon' is defined but never used.", "'ItemCategoryIcon' is defined but never used.", "'GroupIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'ReceiptIcon' is defined but never used.", "'ExitToAppIcon' is defined but never used.", "'InputIcon' is defined but never used.", "'ExpandLessIcon' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ApprovalIcon' is defined but never used.", "'LocalOfferIcon' is defined but never used.", "'MaintenanceIcon' is defined but never used.", "'colors' is defined but never used.", "'shadows' is defined but never used.", "'gradients' is defined but never used.", "'ModernSidebar' is defined but never used.", "'SPACING' is defined but never used.", "'TRANSITIONS' is defined but never used.", "'currentUser' is assigned a value but never used.", "'theme' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchOrgTypes'. Either include it or remove the dependency array.", "ArrayExpression", ["987"], "'Divider' is defined but never used.", "'alpha' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchOrganizations'. Either include it or remove the dependency array.", ["988"], "React Hook useEffect has a missing dependency: 'fetchOffices'. Either include it or remove the dependency array.", ["989"], "'getOrganizations' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchOrganizationDetails'. Either include it or remove the dependency array.", ["990"], "'Tooltip' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'ModernCardGrid' is defined but never used.", "React Hook useEffect has a missing dependency: 'dashboardCards'. Either include it or remove the dependency array.", ["991"], "'cardItem' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSupplierTypes'. Either include it or remove the dependency array.", ["992"], "React Hook useEffect has a missing dependency: 'baseCards'. Either include it or remove the dependency array.", ["993"], "React Hook useEffect has a missing dependency: 'fetchItemTypes'. Either include it or remove the dependency array.", ["994"], "React Hook useEffect has a missing dependency: 'fetchEntryModes'. Either include it or remove the dependency array.", ["995"], "React Hook useEffect has a missing dependency: 'fetchClassifications'. Either include it or remove the dependency array.", ["996"], "React Hook useEffect has a missing dependency: 'fetchItemBrands'. Either include it or remove the dependency array.", ["997"], "React Hook useEffect has a missing dependency: 'fetchItemCategories'. Either include it or remove the dependency array.", ["998"], "React Hook useEffect has a missing dependency: 'fetchUnitsOfMeasure'. Either include it or remove the dependency array.", ["999"], "React Hook useEffect has a missing dependency: 'fetchItemQualities'. Either include it or remove the dependency array.", ["1000"], "React Hook useEffect has a missing dependency: 'fetchItemSizes'. Either include it or remove the dependency array.", ["1001"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["1002"], "React Hook useEffect has a missing dependency: 'loadItemMasters'. Either include it or remove the dependency array.", ["1003"], "'getStatusChip' is defined but never used.", "'formatDate' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchItemMasters'. Either include it or remove the dependency array.", ["1004"], "'errorInfo' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchBatchItems'. Either include it or remove the dependency array.", ["1005"], "'ShareIcon' is defined but never used.", "'TrendingUpIcon' is defined but never used.", "'SecurityIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'ActiveIcon' is defined but never used.", "'InactiveIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadItemMaster' and 'loadRelatedData'. Either include them or remove the dependency array.", ["1006"], "'CardHeader' is defined but never used.", "'Alert' is defined but never used.", "'IconButton' is defined but never used.", "'showAdvanced' is assigned a value but never used.", "'setShowAdvanced' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadDropdownOptions'. Either include it or remove the dependency array.", ["1007"], "React Hook useEffect has a missing dependency: 'loadItemMaster'. Either include it or remove the dependency array.", ["1008"], "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "'FIELD_LABELS' is defined but never used.", "'PLACEHOLDERS' is defined but never used.", "'UI_CONSTANTS' is defined but never used.", "'COLOR_THEMES' is defined but never used.", "'BUTTON_TEXT' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadDropdowns'. Either include it or remove the dependency array.", ["1009"], "React Hook useEffect has a missing dependency: 'loadBatchItem'. Either include it or remove the dependency array.", ["1010"], "React Hook useEffect has a missing dependency: 'loadBatches'. Either include it or remove the dependency array.", ["1011"], "'Rating' is defined but never used.", "'TimelineIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadBatchItem' and 'loadInventoryItems'. Either include them or remove the dependency array.", ["1012"], "'getStepIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadDropdownOptions' and 'loadItemMaster'. Either include them or remove the dependency array.", ["1013"], "'Chip' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadDropdownOptions' and 'loadInventoryItem'. Either include them or remove the dependency array.", ["1014"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "no-useless-escape", "Unnecessary escape character: \\-.", "Literal", "unnecessaryEscape", ["1015", "1016"], "React Hook useEffect has a missing dependency: 'fetchInventoryItems'. Either include it or remove the dependency array.", ["1017"], "'TextField' is defined but never used.", "'ViewIcon' is defined but never used.", "'categories' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["1018"], "'useEffect' is defined but never used.", "'testData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'testAllEndpoints'. Either include it or remove the dependency array.", ["1019"], "React Hook useEffect has a missing dependency: 'fetchInventoryItem'. Either include it or remove the dependency array.", ["1020"], "'response' is assigned a value but never used.", "'Avatar' is defined but never used.", "React Hook useEffect has a missing dependency: 'formik'. Either include it or remove the dependency array.", ["1021"], ["1022"], "'Paper' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchItems'. Either include it or remove the dependency array.", ["1023"], "'StatusCard' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "Unexpected mix of '||' and '&&'. Use parentheses to clarify the intended order of operations.", "'getSupplierTypeColor' is assigned a value but never used.", "'DialogActions' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSupplier'. Either include it or remove the dependency array.", ["1024"], "'DashboardIcon' is defined but never used.", "'StarIcon' is defined but never used.", "'PublicIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'navigate' is assigned a value but never used.", "'amharicStyle' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchForms'. Either include it or remove the dependency array.", ["1025"], "React Hook useEffect has a missing dependency: 'fetchForm'. Either include it or remove the dependency array.", ["1026"], "'RemoveIcon' is defined but never used.", "'formatEthiopianDate' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchAvailableItems' and 'fetchStoresAndShelves'. Either include them or remove the dependency array.", ["1027"], ["1028", "1029"], ["1030", "1031"], "Unnecessary escape character: \\(.", ["1032", "1033"], "Unnecessary escape character: \\).", ["1034", "1035"], ["1036", "1037"], "Unnecessary escape character: \\..", ["1038", "1039"], "Unnecessary escape character: \\,.", ["1040", "1041"], ["1042", "1043"], ["1044", "1045"], "Unnecessary escape character: \\/.", ["1046", "1047"], "Unnecessary escape character: \\&.", ["1048", "1049"], ["1050", "1051"], ["1052", "1053"], ["1054", "1055"], ["1056", "1057"], ["1058", "1059"], ["1060", "1061"], ["1062", "1063"], ["1064", "1065"], ["1066", "1067"], ["1068", "1069"], ["1070", "1071"], ["1072", "1073"], "'WarehouseIcon' is defined but never used.", "'useState' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadCategories' and 'loadVoucher'. Either include them or remove the dependency array.", ["1074"], ["1075"], "React Hook useEffect has a missing dependency: 'loadStoreTypes'. Either include it or remove the dependency array.", ["1076"], ["1077"], "'Grid' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadSupplierTypes'. Either include it or remove the dependency array.", ["1078"], "'enqueueSnackbar' is assigned a value but never used.", {"desc": "1079", "fix": "1080"}, {"desc": "1081", "fix": "1082"}, {"desc": "1083", "fix": "1084"}, {"desc": "1085", "fix": "1086"}, {"desc": "1087", "fix": "1088"}, {"desc": "1089", "fix": "1090"}, {"desc": "1091", "fix": "1092"}, {"desc": "1093", "fix": "1094"}, {"desc": "1095", "fix": "1096"}, {"desc": "1097", "fix": "1098"}, {"desc": "1099", "fix": "1100"}, {"desc": "1101", "fix": "1102"}, {"desc": "1103", "fix": "1104"}, {"desc": "1105", "fix": "1106"}, {"desc": "1107", "fix": "1108"}, {"desc": "1109", "fix": "1110"}, {"desc": "1111", "fix": "1112"}, {"desc": "1113", "fix": "1114"}, {"desc": "1115", "fix": "1116"}, {"desc": "1117", "fix": "1118"}, {"desc": "1119", "fix": "1120"}, {"desc": "1121", "fix": "1122"}, {"desc": "1123", "fix": "1124"}, {"desc": "1125", "fix": "1126"}, {"desc": "1127", "fix": "1128"}, {"desc": "1129", "fix": "1130"}, {"desc": "1131", "fix": "1132"}, {"desc": "1133", "fix": "1134"}, {"messageId": "1135", "fix": "1136", "desc": "1137"}, {"messageId": "1138", "fix": "1139", "desc": "1140"}, {"desc": "1141", "fix": "1142"}, {"desc": "1143", "fix": "1144"}, {"desc": "1145", "fix": "1146"}, {"desc": "1147", "fix": "1148"}, {"desc": "1149", "fix": "1150"}, {"kind": "1151", "justification": "1152"}, {"desc": "1153", "fix": "1154"}, {"desc": "1155", "fix": "1156"}, {"desc": "1157", "fix": "1158"}, {"desc": "1159", "fix": "1160"}, {"desc": "1161", "fix": "1162"}, {"messageId": "1135", "fix": "1163", "desc": "1137"}, {"messageId": "1138", "fix": "1164", "desc": "1140"}, {"messageId": "1135", "fix": "1165", "desc": "1137"}, {"messageId": "1138", "fix": "1166", "desc": "1140"}, {"messageId": "1135", "fix": "1167", "desc": "1137"}, {"messageId": "1138", "fix": "1168", "desc": "1140"}, {"messageId": "1135", "fix": "1169", "desc": "1137"}, {"messageId": "1138", "fix": "1170", "desc": "1140"}, {"messageId": "1135", "fix": "1171", "desc": "1137"}, {"messageId": "1138", "fix": "1172", "desc": "1140"}, {"messageId": "1135", "fix": "1173", "desc": "1137"}, {"messageId": "1138", "fix": "1174", "desc": "1140"}, {"messageId": "1135", "fix": "1175", "desc": "1137"}, {"messageId": "1138", "fix": "1176", "desc": "1140"}, {"messageId": "1135", "fix": "1177", "desc": "1137"}, {"messageId": "1138", "fix": "1178", "desc": "1140"}, {"messageId": "1135", "fix": "1179", "desc": "1137"}, {"messageId": "1138", "fix": "1180", "desc": "1140"}, {"messageId": "1135", "fix": "1181", "desc": "1137"}, {"messageId": "1138", "fix": "1182", "desc": "1140"}, {"messageId": "1135", "fix": "1183", "desc": "1137"}, {"messageId": "1138", "fix": "1184", "desc": "1140"}, {"messageId": "1135", "fix": "1185", "desc": "1137"}, {"messageId": "1138", "fix": "1186", "desc": "1140"}, {"messageId": "1135", "fix": "1187", "desc": "1137"}, {"messageId": "1138", "fix": "1188", "desc": "1140"}, {"messageId": "1135", "fix": "1189", "desc": "1137"}, {"messageId": "1138", "fix": "1190", "desc": "1140"}, {"messageId": "1135", "fix": "1191", "desc": "1137"}, {"messageId": "1138", "fix": "1192", "desc": "1140"}, {"messageId": "1135", "fix": "1193", "desc": "1137"}, {"messageId": "1138", "fix": "1194", "desc": "1140"}, {"messageId": "1135", "fix": "1195", "desc": "1137"}, {"messageId": "1138", "fix": "1196", "desc": "1140"}, {"messageId": "1135", "fix": "1197", "desc": "1137"}, {"messageId": "1138", "fix": "1198", "desc": "1140"}, {"messageId": "1135", "fix": "1199", "desc": "1137"}, {"messageId": "1138", "fix": "1200", "desc": "1140"}, {"messageId": "1135", "fix": "1201", "desc": "1137"}, {"messageId": "1138", "fix": "1202", "desc": "1140"}, {"messageId": "1135", "fix": "1203", "desc": "1137"}, {"messageId": "1138", "fix": "1204", "desc": "1140"}, {"messageId": "1135", "fix": "1205", "desc": "1137"}, {"messageId": "1138", "fix": "1206", "desc": "1140"}, {"messageId": "1135", "fix": "1207", "desc": "1137"}, {"messageId": "1138", "fix": "1208", "desc": "1140"}, {"desc": "1209", "fix": "1210"}, {"desc": "1143", "fix": "1211"}, {"desc": "1212", "fix": "1213"}, {"desc": "1143", "fix": "1214"}, {"desc": "1215", "fix": "1216"}, "Update the dependencies array to be: [fetchOrgTypes]", {"range": "1217", "text": "1218"}, "Update the dependencies array to be: [fetchOrganizations]", {"range": "1219", "text": "1220"}, "Update the dependencies array to be: [fetchOffices]", {"range": "1221", "text": "1222"}, "Update the dependencies array to be: [fetchOrganizationDetails, id]", {"range": "1223", "text": "1224"}, "Update the dependencies array to be: [dashboardCards]", {"range": "1225", "text": "1226"}, "Update the dependencies array to be: [fetchSupplierTypes, page, rowsPerPage, searchTerm]", {"range": "1227", "text": "1228"}, "Update the dependencies array to be: [baseCards]", {"range": "1229", "text": "1230"}, "Update the dependencies array to be: [fetchItemTypes]", {"range": "1231", "text": "1232"}, "Update the dependencies array to be: [fetchEntryModes]", {"range": "1233", "text": "1234"}, "Update the dependencies array to be: [fetchClassifications]", {"range": "1235", "text": "1236"}, "Update the dependencies array to be: [fetchItemBrands]", {"range": "1237", "text": "1238"}, "Update the dependencies array to be: [fetchItemCategories]", {"range": "1239", "text": "1240"}, "Update the dependencies array to be: [fetchUnitsOfMeasure]", {"range": "1241", "text": "1242"}, "Update the dependencies array to be: [fetchItemQualities]", {"range": "1243", "text": "1244"}, "Update the dependencies array to be: [fetchItemSizes]", {"range": "1245", "text": "1246"}, "Update the dependencies array to be: [fetchData]", {"range": "1247", "text": "1248"}, "Update the dependencies array to be: [loadItemMasters, page, rowsPerPage, searchTerm]", {"range": "1249", "text": "1250"}, "Update the dependencies array to be: [page, rowsPerPage, searchTerm, typeFilter, categoryFilter, fetchItemMasters]", {"range": "1251", "text": "1252"}, "Update the dependencies array to be: [fetchBatchItems, page, rowsPerPage, searchTerm, statusFilter]", {"range": "1253", "text": "1254"}, "Update the dependencies array to be: [id, loadItemMaster, loadRelatedData]", {"range": "1255", "text": "1256"}, "Update the dependencies array to be: [loadDropdownOptions]", {"range": "1257", "text": "1258"}, "Update the dependencies array to be: [id, isEdit, loadItemMaster]", {"range": "1259", "text": "1260"}, "Update the dependencies array to be: [id, isEdit, loadDropdowns]", {"range": "1261", "text": "1262"}, "Update the dependencies array to be: [dropdownsLoaded, isEdit, id, loadBatchItem]", {"range": "1263", "text": "1264"}, "Update the dependencies array to be: [loadBatches, page, rowsPerPage, searchTerm]", {"range": "1265", "text": "1266"}, "Update the dependencies array to be: [id, loadBatchItem, loadInventoryItems]", {"range": "1267", "text": "1268"}, "Update the dependencies array to be: [id, isEdit, loadDropdownOptions, loadItemMaster]", {"range": "1269", "text": "1270"}, "Update the dependencies array to be: [id, isEdit, loadDropdownOptions, loadInventoryItem]", {"range": "1271", "text": "1272"}, "removeEscape", {"range": "1273", "text": "1152"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1274", "text": "1275"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [fetchInventoryItems, page, rowsPerPage, searchTerm, statusFilter]", {"range": "1276", "text": "1277"}, "Update the dependencies array to be: [loadData]", {"range": "1278", "text": "1279"}, "Update the dependencies array to be: [testAllEndpoints]", {"range": "1280", "text": "1281"}, "Update the dependencies array to be: [fetchInventoryItem, id]", {"range": "1282", "text": "1283"}, "Update the dependencies array to be: [formik, formik.values.organization, selectedOrgId]", {"range": "1284", "text": "1285"}, "directive", "", "Update the dependencies array to be: [fetchItems]", {"range": "1286", "text": "1287"}, "Update the dependencies array to be: [fetchSupplier, open, supplierId]", {"range": "1288", "text": "1289"}, "Update the dependencies array to be: [page, rowsPerPage, searchTerm, statusFilter, departmentFilter, fetchForms]", {"range": "1290", "text": "1291"}, "Update the dependencies array to be: [fetchForm, id]", {"range": "1292", "text": "1293"}, "Update the dependencies array to be: [fetchAvailableItems, fetchStoresAndShelves]", {"range": "1294", "text": "1295"}, {"range": "1296", "text": "1152"}, {"range": "1297", "text": "1275"}, {"range": "1298", "text": "1152"}, {"range": "1299", "text": "1275"}, {"range": "1300", "text": "1152"}, {"range": "1301", "text": "1275"}, {"range": "1302", "text": "1152"}, {"range": "1303", "text": "1275"}, {"range": "1304", "text": "1152"}, {"range": "1305", "text": "1275"}, {"range": "1306", "text": "1152"}, {"range": "1307", "text": "1275"}, {"range": "1308", "text": "1152"}, {"range": "1309", "text": "1275"}, {"range": "1310", "text": "1152"}, {"range": "1311", "text": "1275"}, {"range": "1312", "text": "1152"}, {"range": "1313", "text": "1275"}, {"range": "1314", "text": "1152"}, {"range": "1315", "text": "1275"}, {"range": "1316", "text": "1152"}, {"range": "1317", "text": "1275"}, {"range": "1318", "text": "1152"}, {"range": "1319", "text": "1275"}, {"range": "1320", "text": "1152"}, {"range": "1321", "text": "1275"}, {"range": "1322", "text": "1152"}, {"range": "1323", "text": "1275"}, {"range": "1324", "text": "1152"}, {"range": "1325", "text": "1275"}, {"range": "1326", "text": "1152"}, {"range": "1327", "text": "1275"}, {"range": "1328", "text": "1152"}, {"range": "1329", "text": "1275"}, {"range": "1330", "text": "1152"}, {"range": "1331", "text": "1275"}, {"range": "1332", "text": "1152"}, {"range": "1333", "text": "1275"}, {"range": "1334", "text": "1152"}, {"range": "1335", "text": "1275"}, {"range": "1336", "text": "1152"}, {"range": "1337", "text": "1275"}, {"range": "1338", "text": "1152"}, {"range": "1339", "text": "1275"}, {"range": "1340", "text": "1152"}, {"range": "1341", "text": "1275"}, "Update the dependencies array to be: [id, isEdit, loadCategories, loadVoucher]", {"range": "1342", "text": "1343"}, {"range": "1344", "text": "1279"}, "Update the dependencies array to be: [loadStoreTypes]", {"range": "1345", "text": "1346"}, {"range": "1347", "text": "1279"}, "Update the dependencies array to be: [loadSupplierTypes]", {"range": "1348", "text": "1349"}, [1902, 1904], "[fetchOrgTypes]", [1929, 1931], "[fetchOrganizations]", [1811, 1813], "[fetchOffices]", [1504, 1508], "[fetchOrganizationDetails, id]", [7436, 7438], "[dashboardCards]", [1347, 1378], "[fetchSupplierTypes, page, rowsPerPage, searchTerm]", [7227, 7229], "[baseCards]", [1402, 1404], "[fetchItemTypes]", [1575, 1577], "[fetchEntryModes]", [1900, 1902], "[fetchClassifications]", [1447, 1449], "[fetchItemBrands]", [1439, 1441], "[fetchItemCategories]", [1448, 1450], "[fetchUnitsOfMeasure]", [1493, 1495], "[fetchItemQualities]", [1410, 1412], "[fetchItemSizes]", [2033, 2035], "[fetchData]", [1688, 1719], "[loadItemMasters, page, rowsPerPage, searchTerm]", [2079, 2138], "[page, rowsPerPage, searchTerm, typeFilter, categoryFilter, fetchItemMasters]", [2237, 2282], "[fetchBatchItems, page, rowsPerPage, searchTerm, statusFilter]", [2593, 2597], "[id, loadItemMaster, loadRelatedData]", [3963, 3965], "[loadDropdownOptions]", [4075, 4087], "[id, isEdit, loadItemMaster]", [12532, 12544], "[id, isEdit, loadDropdowns]", [12752, 12781], "[dropdownsLoaded, isEdit, id, loadBatchItem]", [2003, 2034], "[loadBatches, page, rowsPerPage, searchTerm]", [1638, 1642], "[id, loadBatchItem, loadInventoryItems]", [18848, 18860], "[id, isEdit, loadDropdownOptions, loadItemMaster]", [2957, 2969], "[id, isEdit, loadDropdownOptions, loadInventoryItem]", [9121, 9122], [9121, 9121], "\\", [1883, 1928], "[fetchInventoryItems, page, rowsPerPage, searchTerm, statusFilter]", [1338, 1340], "[loadData]", [2781, 2783], "[testAllEndpoints]", [1336, 1340], "[fetchInventoryItem, id]", [4428, 4471], "[formik, formik.values.organization, selectedOrgId]", [3014, 3016], "[fetchItems]", [1010, 1028], "[fetchSupplier, open, supplierId]", [1750, 1813], "[page, rowsPerPage, searchTerm, statusFilter, departmentFilter, fetchForms]", [1165, 1169], "[fetchForm, id]", [1757, 1759], "[fetchAvailableItems, fetchStoresAndShelves]", [651, 652], [651, 651], [696, 697], [696, 696], [730, 731], [730, 730], [732, 733], [732, 732], [800, 801], [800, 800], [937, 938], [937, 937], [939, 940], [939, 939], [941, 942], [941, 941], [943, 944], [943, 943], [945, 946], [945, 945], [947, 948], [947, 947], [979, 980], [979, 979], [981, 982], [981, 981], [983, 984], [983, 983], [985, 986], [985, 985], [987, 988], [987, 987], [989, 990], [989, 989], [10454, 10455], [10454, 10454], [10456, 10457], [10456, 10456], [10458, 10459], [10458, 10458], [10460, 10461], [10460, 10460], [10462, 10463], [10462, 10462], [10464, 10465], [10464, 10464], [1314, 1326], "[id, isEdit, loadCategories, loadVoucher]", [1618, 1620], [1350, 1352], "[loadStoreTypes]", [1510, 1512], [1455, 1457], "[loadSupplierTypes]"]