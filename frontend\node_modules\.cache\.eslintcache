[{"D:\\Projects v2\\asset management\\frontend\\src\\index.js": "1", "D:\\Projects v2\\asset management\\frontend\\src\\reportWebVitals.js": "2", "D:\\Projects v2\\asset management\\frontend\\src\\App.js": "3", "D:\\Projects v2\\asset management\\frontend\\src\\theme\\designSystem.js": "4", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandler.js": "5", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ErrorBoundary.js": "6", "D:\\Projects v2\\asset management\\frontend\\src\\components\\Layout.js": "7", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeList.js": "8", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\OrganizationMenu.js": "9", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationList.js": "10", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeList.js": "11", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDetail.js": "12", "D:\\Projects v2\\asset management\\frontend\\src\\features\\suppliers\\SuppliersMenu.js": "13", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ApprovalStatusPage.js": "14", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupDashboard.js": "15", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierTypesList.js": "16", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemStatusPage.js": "17", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupMenu.js": "18", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTagPage.js": "19", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\PropertyStatusPage.js": "20", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTypePage.js": "21", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\EntryModePage.js": "22", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\MainClassificationPage.js": "23", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemBrandPage.js": "24", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemCategoryPage.js": "25", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\UnitOfMeasurePage.js": "26", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemQualityPage.js": "27", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemSizePage.js": "28", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\SubClassificationPage.js": "29", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\GenericInventoryPage.js": "30", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemsDashboard.js": "31", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMastersList.js": "32", "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\MainDashboardMenu.js": "33", "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\Dashboard.js": "34", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardItemMastersList.js": "35", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardBatchItemsList.js": "36", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMasterDetail.js": "37", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemManagementMenu.js": "38", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ProfessionalItemMasterForm.js": "39", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemForm.js": "40", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemsList.js": "41", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemDetail.js": "42", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ModernItemMasterForm.js": "43", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemForm.js": "44", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemsList.js": "45", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVouchersList.js": "46", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\EndToEndTest.js": "47", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\DebugInventoryAPI.js": "48", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemDetail.js": "49", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\MaintenanceSchedule.js": "50", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\index.js": "51", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\index.js": "52", "D:\\Projects v2\\asset management\\frontend\\src\\features\\debug\\APITest.js": "53", "D:\\Projects v2\\asset management\\frontend\\src\\features\\auth\\Login.js": "54", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\index.js": "55", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\axios.js": "56", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandling.js": "57", "D:\\Projects v2\\asset management\\frontend\\src\\theme\\professionalTheme.js": "58", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeDialog.js": "59", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeDialog.js": "60", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDialog.js": "61", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\permissions.js": "62", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\StatusManagement.js": "63", "D:\\Projects v2\\asset management\\frontend\\src\\components\\DashboardBanner.js": "64", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\GatesList.js": "65", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SuppliersList.js": "66", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDetailView.js": "67", "D:\\Projects v2\\asset management\\frontend\\src\\services\\auth.js": "68", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernSidebar.js": "69", "D:\\Projects v2\\asset management\\frontend\\src\\services\\organizations.js": "70", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernBreadcrumbs.js": "71", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernCardGrid.js": "72", "D:\\Projects v2\\asset management\\frontend\\src\\services\\suppliers.js": "73", "D:\\Projects v2\\asset management\\frontend\\src\\services\\inventorySetup.js": "74", "D:\\Projects v2\\asset management\\frontend\\src\\services\\items.js": "75", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDashboard.js": "76", "D:\\Projects v2\\asset management\\frontend\\src\\services\\supplier.js": "77", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierCategoriesList.js": "78", "D:\\Projects v2\\asset management\\frontend\\src\\config\\itemManagementDesign.js": "79", "D:\\Projects v2\\asset management\\frontend\\src\\config\\formConfig.js": "80", "D:\\Projects v2\\asset management\\frontend\\src\\config\\constants.js": "81", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormTemplate.js": "82", "D:\\Projects v2\\asset management\\frontend\\src\\services\\model19.js": "83", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormsList.js": "84", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19Menu.js": "85", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormDetail.js": "86", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormCreate.js": "87", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\filters.js": "88", "D:\\Projects v2\\asset management\\frontend\\src\\components\\forms\\FormValidation.js": "89", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\validation.js": "90", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StorageMenu.js": "91", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoragePage.js": "92", "D:\\Projects v2\\asset management\\frontend\\src\\features\\analytics\\AnalyticsMenu.js": "93", "D:\\Projects v2\\asset management\\frontend\\src\\features\\help\\HelpMenu.js": "94", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVoucherForm.js": "95", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoresList.js": "96", "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoreTypesList.js": "97"}, {"size": 618, "mtime": 1749121146000, "results": "98", "hashOfConfig": "99"}, {"size": 362, "mtime": 1744151842000, "results": "100", "hashOfConfig": "99"}, {"size": 36676, "mtime": 1751000703864, "results": "101", "hashOfConfig": "99"}, {"size": 7358, "mtime": 1749460684000, "results": "102", "hashOfConfig": "99"}, {"size": 14601, "mtime": 1750941396982, "results": "103", "hashOfConfig": "99"}, {"size": 7808, "mtime": 1750503868000, "results": "104", "hashOfConfig": "99"}, {"size": 18491, "mtime": 1750062608000, "results": "105", "hashOfConfig": "99"}, {"size": 7248, "mtime": 1750100376000, "results": "106", "hashOfConfig": "99"}, {"size": 6567, "mtime": 1750588146000, "results": "107", "hashOfConfig": "99"}, {"size": 7649, "mtime": 1750100362000, "results": "108", "hashOfConfig": "99"}, {"size": 7291, "mtime": 1750100348000, "results": "109", "hashOfConfig": "99"}, {"size": 20275, "mtime": 1750497690000, "results": "110", "hashOfConfig": "99"}, {"size": 5553, "mtime": 1750587110000, "results": "111", "hashOfConfig": "99"}, {"size": 1543, "mtime": 1749321316000, "results": "112", "hashOfConfig": "99"}, {"size": 17426, "mtime": 1750482586000, "results": "113", "hashOfConfig": "99"}, {"size": 9822, "mtime": 1750587406000, "results": "114", "hashOfConfig": "99"}, {"size": 1446, "mtime": 1749321274000, "results": "115", "hashOfConfig": "99"}, {"size": 14437, "mtime": 1750573144000, "results": "116", "hashOfConfig": "99"}, {"size": 1939, "mtime": 1749321336000, "results": "117", "hashOfConfig": "99"}, {"size": 1456, "mtime": 1749321296000, "results": "118", "hashOfConfig": "99"}, {"size": 12481, "mtime": 1750495760000, "results": "119", "hashOfConfig": "99"}, {"size": 14492, "mtime": 1750485988000, "results": "120", "hashOfConfig": "99"}, {"size": 16270, "mtime": 1750495734000, "results": "121", "hashOfConfig": "99"}, {"size": 13843, "mtime": 1750495814000, "results": "122", "hashOfConfig": "99"}, {"size": 12194, "mtime": 1750485914000, "results": "123", "hashOfConfig": "99"}, {"size": 12917, "mtime": 1750497900000, "results": "124", "hashOfConfig": "99"}, {"size": 13503, "mtime": 1750486292000, "results": "125", "hashOfConfig": "99"}, {"size": 11930, "mtime": 1750486214000, "results": "126", "hashOfConfig": "99"}, {"size": 16201, "mtime": 1750485170000, "results": "127", "hashOfConfig": "99"}, {"size": 6298, "mtime": 1749460684000, "results": "128", "hashOfConfig": "99"}, {"size": 14558, "mtime": 1749494266000, "results": "129", "hashOfConfig": "99"}, {"size": 13420, "mtime": 1750947652189, "results": "130", "hashOfConfig": "99"}, {"size": 6934, "mtime": 1750590990000, "results": "131", "hashOfConfig": "99"}, {"size": 11114, "mtime": 1749460684000, "results": "132", "hashOfConfig": "99"}, {"size": 21268, "mtime": 1750532258000, "results": "133", "hashOfConfig": "99"}, {"size": 24046, "mtime": 1750503298000, "results": "134", "hashOfConfig": "99"}, {"size": 38782, "mtime": 1749541954000, "results": "135", "hashOfConfig": "99"}, {"size": 12462, "mtime": 1750573110000, "results": "136", "hashOfConfig": "99"}, {"size": 45490, "mtime": 1750947122266, "results": "137", "hashOfConfig": "99"}, {"size": 37780, "mtime": 1750999336205, "results": "138", "hashOfConfig": "99"}, {"size": 19827, "mtime": 1749538128000, "results": "139", "hashOfConfig": "99"}, {"size": 25161, "mtime": 1749538944000, "results": "140", "hashOfConfig": "99"}, {"size": 39922, "mtime": 1750999036332, "results": "141", "hashOfConfig": "99"}, {"size": 36856, "mtime": 1750578874000, "results": "142", "hashOfConfig": "99"}, {"size": 19936, "mtime": 1750592184000, "results": "143", "hashOfConfig": "99"}, {"size": 11045, "mtime": 1751004656405, "results": "144", "hashOfConfig": "99"}, {"size": 12291, "mtime": 1749576472000, "results": "145", "hashOfConfig": "99"}, {"size": 6165, "mtime": 1749574028000, "results": "146", "hashOfConfig": "99"}, {"size": 18044, "mtime": 1750499802000, "results": "147", "hashOfConfig": "99"}, {"size": 1622, "mtime": 1749493532000, "results": "148", "hashOfConfig": "99"}, {"size": 323, "mtime": 1750588036000, "results": "149", "hashOfConfig": "99"}, {"size": 430, "mtime": 1750587482000, "results": "150", "hashOfConfig": "99"}, {"size": 6925, "mtime": 1750572170000, "results": "151", "hashOfConfig": "99"}, {"size": 15577, "mtime": 1749244308000, "results": "152", "hashOfConfig": "99"}, {"size": 415, "mtime": 1750592080000, "results": "153", "hashOfConfig": "99"}, {"size": 1263, "mtime": 1749243990000, "results": "154", "hashOfConfig": "99"}, {"size": 8607, "mtime": 1750503624000, "results": "155", "hashOfConfig": "99"}, {"size": 7700, "mtime": 1750061828000, "results": "156", "hashOfConfig": "99"}, {"size": 8779, "mtime": 1744341666000, "results": "157", "hashOfConfig": "99"}, {"size": 3425, "mtime": 1744162122000, "results": "158", "hashOfConfig": "99"}, {"size": 14617, "mtime": 1749196086000, "results": "159", "hashOfConfig": "99"}, {"size": 8029, "mtime": 1749493422000, "results": "160", "hashOfConfig": "99"}, {"size": 28449, "mtime": 1750482954000, "results": "161", "hashOfConfig": "99"}, {"size": 12377, "mtime": 1749244482000, "results": "162", "hashOfConfig": "99"}, {"size": 14628, "mtime": 1750497014000, "results": "163", "hashOfConfig": "99"}, {"size": 35316, "mtime": 1749460684000, "results": "164", "hashOfConfig": "99"}, {"size": 16392, "mtime": 1749259392000, "results": "165", "hashOfConfig": "99"}, {"size": 1272, "mtime": 1748021328000, "results": "166", "hashOfConfig": "99"}, {"size": 11325, "mtime": 1749460684000, "results": "167", "hashOfConfig": "99"}, {"size": 7867, "mtime": 1750670878000, "results": "168", "hashOfConfig": "99"}, {"size": 6155, "mtime": 1749460684000, "results": "169", "hashOfConfig": "99"}, {"size": 11465, "mtime": 1749460684000, "results": "170", "hashOfConfig": "99"}, {"size": 6947, "mtime": 1750587052000, "results": "171", "hashOfConfig": "99"}, {"size": 6776, "mtime": 1750943098693, "results": "172", "hashOfConfig": "99"}, {"size": 9892, "mtime": 1750573370000, "results": "173", "hashOfConfig": "99"}, {"size": 14652, "mtime": 1749486778000, "results": "174", "hashOfConfig": "99"}, {"size": 7916, "mtime": 1749460684000, "results": "175", "hashOfConfig": "99"}, {"size": 9563, "mtime": 1749258762000, "results": "176", "hashOfConfig": "99"}, {"size": 7273, "mtime": 1750502106000, "results": "177", "hashOfConfig": "99"}, {"size": 11010, "mtime": 1750957135442, "results": "178", "hashOfConfig": "99"}, {"size": 2951, "mtime": 1750590930000, "results": "179", "hashOfConfig": "99"}, {"size": 15459, "mtime": 1750672966000, "results": "180", "hashOfConfig": "99"}, {"size": 8010, "mtime": 1750626786000, "results": "181", "hashOfConfig": "99"}, {"size": 16382, "mtime": 1750591964000, "results": "182", "hashOfConfig": "99"}, {"size": 7365, "mtime": 1750591030000, "results": "183", "hashOfConfig": "99"}, {"size": 12742, "mtime": 1750591874000, "results": "184", "hashOfConfig": "99"}, {"size": 24262, "mtime": 1750755554000, "results": "185", "hashOfConfig": "99"}, {"size": 2271, "mtime": 1744341630000, "results": "186", "hashOfConfig": "99"}, {"size": 9322, "mtime": 1749321190000, "results": "187", "hashOfConfig": "99"}, {"size": 14293, "mtime": 1750940433844, "results": "188", "hashOfConfig": "99"}, {"size": 8462, "mtime": 1750999996908, "results": "189", "hashOfConfig": "99"}, {"size": 5732, "mtime": 1751000693418, "results": "190", "hashOfConfig": "99"}, {"size": 8739, "mtime": 1750999786596, "results": "191", "hashOfConfig": "99"}, {"size": 9579, "mtime": 1750999827928, "results": "192", "hashOfConfig": "99"}, {"size": 12709, "mtime": 1751004618727, "results": "193", "hashOfConfig": "99"}, {"size": 16413, "mtime": 1751004754752, "results": "194", "hashOfConfig": "99"}, {"size": 11351, "mtime": 1751004706550, "results": "195", "hashOfConfig": "99"}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gs8ssj", {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Projects v2\\asset management\\frontend\\src\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\App.js", ["487", "488", "489", "490"], [], "D:\\Projects v2\\asset management\\frontend\\src\\theme\\designSystem.js", ["491"], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandler.js", ["492"], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ErrorBoundary.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\Layout.js", ["493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeList.js", ["518", "519"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\OrganizationMenu.js", ["520"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationList.js", ["521", "522", "523"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeList.js", ["524", "525"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDetail.js", ["526", "527", "528", "529"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\suppliers\\SuppliersMenu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ApprovalStatusPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupDashboard.js", ["530", "531", "532", "533", "534", "535", "536"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierTypesList.js", ["537", "538"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemStatusPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupMenu.js", ["539"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTagPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\PropertyStatusPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTypePage.js", ["540"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\EntryModePage.js", ["541"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\MainClassificationPage.js", ["542", "543", "544"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemBrandPage.js", ["545"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemCategoryPage.js", ["546"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\UnitOfMeasurePage.js", ["547"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemQualityPage.js", ["548"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemSizePage.js", ["549"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\SubClassificationPage.js", ["550", "551", "552"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\GenericInventoryPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemsDashboard.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMastersList.js", ["553"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\MainDashboardMenu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\Dashboard.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardItemMastersList.js", ["554", "555", "556", "557", "558", "559"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardBatchItemsList.js", ["560", "561", "562", "563"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMasterDetail.js", ["564", "565", "566", "567", "568", "569", "570"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemManagementMenu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ProfessionalItemMasterForm.js", ["571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemForm.js", ["586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemsList.js", ["597", "598", "599"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemDetail.js", ["600", "601", "602"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ModernItemMasterForm.js", ["603", "604", "605", "606", "607", "608", "609", "610", "611"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemForm.js", ["612", "613", "614", "615", "616", "617", "618"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemsList.js", ["619", "620", "621"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVouchersList.js", ["622", "623", "624", "625", "626"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\EndToEndTest.js", ["627", "628"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\DebugInventoryAPI.js", ["629"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemDetail.js", ["630"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\MaintenanceSchedule.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\debug\\APITest.js", ["631"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\auth\\Login.js", ["632"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\axios.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandling.js", ["633"], [], "D:\\Projects v2\\asset management\\frontend\\src\\theme\\professionalTheme.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeDialog.js", [], ["634"], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeDialog.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDialog.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\permissions.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\StatusManagement.js", ["635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646"], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\DashboardBanner.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\GatesList.js", ["647", "648", "649"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SuppliersList.js", ["650"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDetailView.js", ["651", "652"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\auth.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernSidebar.js", ["653", "654"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\organizations.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernBreadcrumbs.js", ["655"], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernCardGrid.js", ["656", "657"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\suppliers.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\inventorySetup.js", ["658"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\items.js", ["659"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDashboard.js", ["660", "661", "662", "663", "664", "665", "666"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\supplier.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierCategoriesList.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\config\\itemManagementDesign.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\config\\formConfig.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\config\\constants.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormTemplate.js", ["667", "668"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\model19.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormsList.js", ["669", "670"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19Menu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormDetail.js", ["671"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormCreate.js", ["672", "673", "674", "675", "676", "677", "678", "679"], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\filters.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\forms\\FormValidation.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\validation.js", ["680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StorageMenu.js", ["703", "704"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoragePage.js", ["705"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\analytics\\AnalyticsMenu.js", ["706"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\help\\HelpMenu.js", ["707"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVoucherForm.js", ["708", "709"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoresList.js", ["710", "711", "712"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\storage\\StoreTypesList.js", ["713", "714"], [], {"ruleId": "715", "severity": 1, "message": "716", "line": 59, "column": 8, "nodeType": "717", "messageId": "718", "endLine": 59, "endColumn": 22}, {"ruleId": "715", "severity": 1, "message": "719", "line": 60, "column": 8, "nodeType": "717", "messageId": "718", "endLine": 60, "endColumn": 18}, {"ruleId": "715", "severity": 1, "message": "720", "line": 72, "column": 8, "nodeType": "717", "messageId": "718", "endLine": 72, "endColumn": 23}, {"ruleId": "715", "severity": 1, "message": "721", "line": 79, "column": 8, "nodeType": "717", "messageId": "718", "endLine": 79, "endColumn": 22}, {"ruleId": "722", "severity": 1, "message": "723", "line": 305, "column": 1, "nodeType": "724", "endLine": 315, "endColumn": 3}, {"ruleId": "715", "severity": 1, "message": "725", "line": 284, "column": 11, "nodeType": "717", "messageId": "718", "endLine": 284, "endColumn": 18}, {"ruleId": "715", "severity": 1, "message": "726", "line": 21, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 21, "endColumn": 11}, {"ruleId": "715", "severity": 1, "message": "727", "line": 22, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 22, "endColumn": 7}, {"ruleId": "715", "severity": 1, "message": "728", "line": 23, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 23, "endColumn": 11}, {"ruleId": "715", "severity": 1, "message": "729", "line": 34, "column": 15, "nodeType": "717", "messageId": "718", "endLine": 34, "endColumn": 27}, {"ruleId": "715", "severity": 1, "message": "730", "line": 35, "column": 17, "nodeType": "717", "messageId": "718", "endLine": 35, "endColumn": 31}, {"ruleId": "715", "severity": 1, "message": "731", "line": 41, "column": 15, "nodeType": "717", "messageId": "718", "endLine": 41, "endColumn": 27}, {"ruleId": "715", "severity": 1, "message": "732", "line": 42, "column": 12, "nodeType": "717", "messageId": "718", "endLine": 42, "endColumn": 28}, {"ruleId": "715", "severity": 1, "message": "733", "line": 43, "column": 12, "nodeType": "717", "messageId": "718", "endLine": 43, "endColumn": 21}, {"ruleId": "715", "severity": 1, "message": "734", "line": 44, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 44, "endColumn": 17}, {"ruleId": "715", "severity": 1, "message": "735", "line": 45, "column": 14, "nodeType": "717", "messageId": "718", "endLine": 45, "endColumn": 25}, {"ruleId": "715", "severity": 1, "message": "736", "line": 46, "column": 16, "nodeType": "717", "messageId": "718", "endLine": 46, "endColumn": 29}, {"ruleId": "715", "severity": 1, "message": "737", "line": 47, "column": 12, "nodeType": "717", "messageId": "718", "endLine": 47, "endColumn": 21}, {"ruleId": "715", "severity": 1, "message": "738", "line": 48, "column": 17, "nodeType": "717", "messageId": "718", "endLine": 48, "endColumn": 31}, {"ruleId": "715", "severity": 1, "message": "739", "line": 49, "column": 17, "nodeType": "717", "messageId": "718", "endLine": 49, "endColumn": 31}, {"ruleId": "715", "severity": 1, "message": "740", "line": 50, "column": 18, "nodeType": "717", "messageId": "718", "endLine": 50, "endColumn": 33}, {"ruleId": "715", "severity": 1, "message": "741", "line": 51, "column": 15, "nodeType": "717", "messageId": "718", "endLine": 51, "endColumn": 27}, {"ruleId": "715", "severity": 1, "message": "742", "line": 52, "column": 17, "nodeType": "717", "messageId": "718", "endLine": 52, "endColumn": 31}, {"ruleId": "715", "severity": 1, "message": "743", "line": 54, "column": 12, "nodeType": "717", "messageId": "718", "endLine": 54, "endColumn": 27}, {"ruleId": "715", "severity": 1, "message": "744", "line": 61, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 61, "endColumn": 16}, {"ruleId": "715", "severity": 1, "message": "745", "line": 61, "column": 18, "nodeType": "717", "messageId": "718", "endLine": 61, "endColumn": 25}, {"ruleId": "715", "severity": 1, "message": "746", "line": 61, "column": 27, "nodeType": "717", "messageId": "718", "endLine": 61, "endColumn": 36}, {"ruleId": "715", "severity": 1, "message": "747", "line": 62, "column": 8, "nodeType": "717", "messageId": "718", "endLine": 62, "endColumn": 21}, {"ruleId": "715", "severity": 1, "message": "748", "line": 63, "column": 18, "nodeType": "717", "messageId": "718", "endLine": 63, "endColumn": 25}, {"ruleId": "715", "severity": 1, "message": "749", "line": 63, "column": 39, "nodeType": "717", "messageId": "718", "endLine": 63, "endColumn": 50}, {"ruleId": "715", "severity": 1, "message": "750", "line": 204, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 204, "endColumn": 21}, {"ruleId": "715", "severity": 1, "message": "751", "line": 36, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 36, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "753", "line": 67, "column": 6, "nodeType": "754", "endLine": 67, "endColumn": 8, "suggestions": "755"}, {"ruleId": "715", "severity": 1, "message": "756", "line": 16, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 16, "endColumn": 10}, {"ruleId": "715", "severity": 1, "message": "757", "line": 21, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 21, "endColumn": 8}, {"ruleId": "715", "severity": 1, "message": "751", "line": 40, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 40, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "758", "line": 73, "column": 6, "nodeType": "754", "endLine": 73, "endColumn": 8, "suggestions": "759"}, {"ruleId": "715", "severity": 1, "message": "751", "line": 36, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 36, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "760", "line": 67, "column": 6, "nodeType": "754", "endLine": 67, "endColumn": 8, "suggestions": "761"}, {"ruleId": "715", "severity": 1, "message": "756", "line": 23, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 23, "endColumn": 10}, {"ruleId": "715", "severity": 1, "message": "762", "line": 40, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 40, "endColumn": 26}, {"ruleId": "715", "severity": 1, "message": "751", "line": 43, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 43, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "763", "line": 64, "column": 6, "nodeType": "754", "endLine": 64, "endColumn": 10, "suggestions": "764"}, {"ruleId": "715", "severity": 1, "message": "765", "line": 13, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 13, "endColumn": 10}, {"ruleId": "715", "severity": 1, "message": "766", "line": 19, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 19, "endColumn": 14}, {"ruleId": "715", "severity": 1, "message": "767", "line": 19, "column": 16, "nodeType": "717", "messageId": "718", "endLine": 19, "endColumn": 27}, {"ruleId": "715", "severity": 1, "message": "768", "line": 21, "column": 8, "nodeType": "717", "messageId": "718", "endLine": 21, "endColumn": 22}, {"ruleId": "715", "severity": 1, "message": "751", "line": 64, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 64, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "769", "line": 277, "column": 6, "nodeType": "754", "endLine": 277, "endColumn": 8, "suggestions": "770"}, {"ruleId": "715", "severity": 1, "message": "771", "line": 284, "column": 11, "nodeType": "717", "messageId": "718", "endLine": 284, "endColumn": 19}, {"ruleId": "715", "severity": 1, "message": "766", "line": 11, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 11, "endColumn": 7}, {"ruleId": "752", "severity": 1, "message": "772", "line": 58, "column": 6, "nodeType": "754", "endLine": 58, "endColumn": 37, "suggestions": "773"}, {"ruleId": "752", "severity": 1, "message": "774", "line": 232, "column": 6, "nodeType": "754", "endLine": 232, "endColumn": 8, "suggestions": "775"}, {"ruleId": "752", "severity": 1, "message": "776", "line": 66, "column": 6, "nodeType": "754", "endLine": 66, "endColumn": 8, "suggestions": "777"}, {"ruleId": "752", "severity": 1, "message": "778", "line": 75, "column": 6, "nodeType": "754", "endLine": 75, "endColumn": 8, "suggestions": "779"}, {"ruleId": "715", "severity": 1, "message": "766", "line": 10, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 10, "endColumn": 7}, {"ruleId": "715", "severity": 1, "message": "767", "line": 11, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 11, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "780", "line": 84, "column": 6, "nodeType": "754", "endLine": 84, "endColumn": 8, "suggestions": "781"}, {"ruleId": "752", "severity": 1, "message": "782", "line": 68, "column": 6, "nodeType": "754", "endLine": 68, "endColumn": 8, "suggestions": "783"}, {"ruleId": "752", "severity": 1, "message": "784", "line": 66, "column": 6, "nodeType": "754", "endLine": 66, "endColumn": 8, "suggestions": "785"}, {"ruleId": "752", "severity": 1, "message": "786", "line": 67, "column": 6, "nodeType": "754", "endLine": 67, "endColumn": 8, "suggestions": "787"}, {"ruleId": "752", "severity": 1, "message": "788", "line": 68, "column": 6, "nodeType": "754", "endLine": 68, "endColumn": 8, "suggestions": "789"}, {"ruleId": "752", "severity": 1, "message": "790", "line": 66, "column": 6, "nodeType": "754", "endLine": 66, "endColumn": 8, "suggestions": "791"}, {"ruleId": "715", "severity": 1, "message": "766", "line": 10, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 10, "endColumn": 7}, {"ruleId": "715", "severity": 1, "message": "767", "line": 11, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 11, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "792", "line": 89, "column": 6, "nodeType": "754", "endLine": 89, "endColumn": 8, "suggestions": "793"}, {"ruleId": "752", "severity": 1, "message": "794", "line": 70, "column": 6, "nodeType": "754", "endLine": 70, "endColumn": 37, "suggestions": "795"}, {"ruleId": "715", "severity": 1, "message": "757", "line": 38, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 38, "endColumn": 8}, {"ruleId": "715", "severity": 1, "message": "796", "line": 57, "column": 25, "nodeType": "717", "messageId": "718", "endLine": 57, "endColumn": 38}, {"ruleId": "715", "severity": 1, "message": "797", "line": 57, "column": 56, "nodeType": "717", "messageId": "718", "endLine": 57, "endColumn": 66}, {"ruleId": "715", "severity": 1, "message": "751", "line": 62, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 62, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "798", "line": 85, "column": 6, "nodeType": "754", "endLine": 85, "endColumn": 65, "suggestions": "799"}, {"ruleId": "715", "severity": 1, "message": "800", "line": 103, "column": 13, "nodeType": "717", "messageId": "718", "endLine": 103, "endColumn": 22}, {"ruleId": "715", "severity": 1, "message": "757", "line": 38, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 38, "endColumn": 8}, {"ruleId": "715", "severity": 1, "message": "801", "line": 63, "column": 40, "nodeType": "717", "messageId": "718", "endLine": 63, "endColumn": 54}, {"ruleId": "715", "severity": 1, "message": "751", "line": 67, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 67, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "802", "line": 92, "column": 6, "nodeType": "754", "endLine": 92, "endColumn": 51, "suggestions": "803"}, {"ruleId": "715", "severity": 1, "message": "804", "line": 50, "column": 12, "nodeType": "717", "messageId": "718", "endLine": 50, "endColumn": 21}, {"ruleId": "715", "severity": 1, "message": "805", "line": 54, "column": 17, "nodeType": "717", "messageId": "718", "endLine": 54, "endColumn": 31}, {"ruleId": "715", "severity": 1, "message": "806", "line": 59, "column": 15, "nodeType": "717", "messageId": "718", "endLine": 59, "endColumn": 27}, {"ruleId": "715", "severity": 1, "message": "807", "line": 61, "column": 14, "nodeType": "717", "messageId": "718", "endLine": 61, "endColumn": 25}, {"ruleId": "715", "severity": 1, "message": "808", "line": 62, "column": 18, "nodeType": "717", "messageId": "718", "endLine": 62, "endColumn": 28}, {"ruleId": "715", "severity": 1, "message": "809", "line": 63, "column": 13, "nodeType": "717", "messageId": "718", "endLine": 63, "endColumn": 25}, {"ruleId": "752", "severity": 1, "message": "810", "line": 105, "column": 6, "nodeType": "754", "endLine": 105, "endColumn": 10, "suggestions": "811"}, {"ruleId": "715", "severity": 1, "message": "812", "line": 29, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 29, "endColumn": 13}, {"ruleId": "715", "severity": 1, "message": "756", "line": 34, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 34, "endColumn": 10}, {"ruleId": "715", "severity": 1, "message": "813", "line": 35, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 35, "endColumn": 8}, {"ruleId": "715", "severity": 1, "message": "814", "line": 37, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 37, "endColumn": 13}, {"ruleId": "715", "severity": 1, "message": "765", "line": 38, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 38, "endColumn": 10}, {"ruleId": "715", "severity": 1, "message": "726", "line": 39, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 39, "endColumn": 11}, {"ruleId": "715", "severity": 1, "message": "807", "line": 57, "column": 14, "nodeType": "717", "messageId": "718", "endLine": 57, "endColumn": 25}, {"ruleId": "715", "severity": 1, "message": "729", "line": 58, "column": 15, "nodeType": "717", "messageId": "718", "endLine": 58, "endColumn": 27}, {"ruleId": "715", "severity": 1, "message": "739", "line": 62, "column": 17, "nodeType": "717", "messageId": "718", "endLine": 62, "endColumn": 31}, {"ruleId": "715", "severity": 1, "message": "738", "line": 63, "column": 17, "nodeType": "717", "messageId": "718", "endLine": 63, "endColumn": 31}, {"ruleId": "715", "severity": 1, "message": "815", "line": 122, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 122, "endColumn": 22}, {"ruleId": "715", "severity": 1, "message": "816", "line": 122, "column": 24, "nodeType": "717", "messageId": "718", "endLine": 122, "endColumn": 39}, {"ruleId": "752", "severity": 1, "message": "817", "line": 157, "column": 6, "nodeType": "754", "endLine": 157, "endColumn": 8, "suggestions": "818"}, {"ruleId": "752", "severity": 1, "message": "819", "line": 164, "column": 6, "nodeType": "754", "endLine": 164, "endColumn": 18, "suggestions": "820"}, {"ruleId": "752", "severity": 1, "message": "821", "line": 167, "column": 29, "nodeType": "717", "endLine": 167, "endColumn": 40}, {"ruleId": "715", "severity": 1, "message": "766", "line": 4, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 4, "endColumn": 7}, {"ruleId": "715", "severity": 1, "message": "767", "line": 5, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 5, "endColumn": 14}, {"ruleId": "715", "severity": 1, "message": "726", "line": 28, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 28, "endColumn": 11}, {"ruleId": "715", "severity": 1, "message": "730", "line": 39, "column": 17, "nodeType": "717", "messageId": "718", "endLine": 39, "endColumn": 31}, {"ruleId": "715", "severity": 1, "message": "822", "line": 51, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 51, "endColumn": 15}, {"ruleId": "715", "severity": 1, "message": "823", "line": 52, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 52, "endColumn": 15}, {"ruleId": "715", "severity": 1, "message": "824", "line": 54, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 54, "endColumn": 15}, {"ruleId": "715", "severity": 1, "message": "825", "line": 55, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 55, "endColumn": 15}, {"ruleId": "715", "severity": 1, "message": "826", "line": 56, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 56, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "827", "line": 452, "column": 6, "nodeType": "754", "endLine": 452, "endColumn": 18, "suggestions": "828"}, {"ruleId": "752", "severity": 1, "message": "829", "line": 460, "column": 6, "nodeType": "754", "endLine": 460, "endColumn": 35, "suggestions": "830"}, {"ruleId": "715", "severity": 1, "message": "766", "line": 23, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 23, "endColumn": 7}, {"ruleId": "715", "severity": 1, "message": "767", "line": 24, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 24, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "831", "line": 86, "column": 6, "nodeType": "754", "endLine": 86, "endColumn": 37, "suggestions": "832"}, {"ruleId": "715", "severity": 1, "message": "833", "line": 34, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 34, "endColumn": 9}, {"ruleId": "715", "severity": 1, "message": "834", "line": 43, "column": 15, "nodeType": "717", "messageId": "718", "endLine": 43, "endColumn": 27}, {"ruleId": "752", "severity": 1, "message": "835", "line": 73, "column": 6, "nodeType": "754", "endLine": 73, "endColumn": 10, "suggestions": "836"}, {"ruleId": "715", "severity": 1, "message": "766", "line": 5, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 5, "endColumn": 7}, {"ruleId": "715", "severity": 1, "message": "767", "line": 6, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 6, "endColumn": 14}, {"ruleId": "715", "severity": 1, "message": "726", "line": 31, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 31, "endColumn": 11}, {"ruleId": "715", "severity": 1, "message": "734", "line": 36, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 36, "endColumn": 17}, {"ruleId": "715", "severity": 1, "message": "730", "line": 45, "column": 17, "nodeType": "717", "messageId": "718", "endLine": 45, "endColumn": 31}, {"ruleId": "715", "severity": 1, "message": "825", "line": 60, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 60, "endColumn": 15}, {"ruleId": "715", "severity": 1, "message": "826", "line": 61, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 61, "endColumn": 14}, {"ruleId": "715", "severity": 1, "message": "837", "line": 66, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 66, "endColumn": 21}, {"ruleId": "752", "severity": 1, "message": "838", "line": 626, "column": 6, "nodeType": "754", "endLine": 626, "endColumn": 18, "suggestions": "839"}, {"ruleId": "715", "severity": 1, "message": "756", "line": 25, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 25, "endColumn": 10}, {"ruleId": "715", "severity": 1, "message": "840", "line": 34, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 34, "endColumn": 7}, {"ruleId": "715", "severity": 1, "message": "757", "line": 36, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 36, "endColumn": 8}, {"ruleId": "715", "severity": 1, "message": "751", "line": 63, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 63, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "841", "line": 131, "column": 6, "nodeType": "754", "endLine": 131, "endColumn": 18, "suggestions": "842"}, {"ruleId": "843", "severity": 1, "message": "844", "line": 308, "column": 5, "nodeType": "845", "messageId": "846", "endLine": 354, "endColumn": 6}, {"ruleId": "847", "severity": 1, "message": "848", "line": 326, "column": 45, "nodeType": "849", "messageId": "850", "endLine": 326, "endColumn": 46, "suggestions": "851"}, {"ruleId": "715", "severity": 1, "message": "757", "line": 38, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 38, "endColumn": 8}, {"ruleId": "715", "severity": 1, "message": "751", "line": 61, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 61, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "852", "line": 77, "column": 6, "nodeType": "754", "endLine": 77, "endColumn": 51, "suggestions": "853"}, {"ruleId": "715", "severity": 1, "message": "813", "line": 21, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 21, "endColumn": 8}, {"ruleId": "715", "severity": 1, "message": "854", "line": 29, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 29, "endColumn": 12}, {"ruleId": "715", "severity": 1, "message": "855", "line": 36, "column": 17, "nodeType": "717", "messageId": "718", "endLine": 36, "endColumn": 25}, {"ruleId": "715", "severity": 1, "message": "856", "line": 51, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 51, "endColumn": 20}, {"ruleId": "752", "severity": 1, "message": "857", "line": 58, "column": 6, "nodeType": "754", "endLine": 58, "endColumn": 8, "suggestions": "858"}, {"ruleId": "715", "severity": 1, "message": "859", "line": 5, "column": 27, "nodeType": "717", "messageId": "718", "endLine": 5, "endColumn": 36}, {"ruleId": "715", "severity": 1, "message": "860", "line": 59, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 59, "endColumn": 18}, {"ruleId": "752", "severity": 1, "message": "861", "line": 106, "column": 6, "nodeType": "754", "endLine": 106, "endColumn": 8, "suggestions": "862"}, {"ruleId": "752", "severity": 1, "message": "863", "line": 61, "column": 6, "nodeType": "754", "endLine": 61, "endColumn": 10, "suggestions": "864"}, {"ruleId": "715", "severity": 1, "message": "865", "line": 69, "column": 13, "nodeType": "717", "messageId": "718", "endLine": 69, "endColumn": 21}, {"ruleId": "715", "severity": 1, "message": "866", "line": 12, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 12, "endColumn": 9}, {"ruleId": "722", "severity": 1, "message": "723", "line": 315, "column": 1, "nodeType": "724", "endLine": 327, "endColumn": 3}, {"ruleId": "752", "severity": 1, "message": "867", "line": 129, "column": 6, "nodeType": "754", "endLine": 129, "endColumn": 49, "suggestions": "868", "suppressions": "869"}, {"ruleId": "715", "severity": 1, "message": "870", "line": 33, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 33, "endColumn": 8}, {"ruleId": "715", "severity": 1, "message": "751", "line": 61, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 61, "endColumn": 14}, {"ruleId": "752", "severity": 1, "message": "871", "line": 123, "column": 6, "nodeType": "754", "endLine": 123, "endColumn": 8, "suggestions": "872"}, {"ruleId": "715", "severity": 1, "message": "873", "line": 211, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 211, "endColumn": 19}, {"ruleId": "874", "severity": 1, "message": "875", "line": 712, "column": 41, "nodeType": "876", "messageId": "877", "endLine": 712, "endColumn": 43}, {"ruleId": "874", "severity": 1, "message": "875", "line": 712, "column": 62, "nodeType": "876", "messageId": "877", "endLine": 712, "endColumn": 64}, {"ruleId": "874", "severity": 1, "message": "878", "line": 712, "column": 62, "nodeType": "876", "messageId": "877", "endLine": 712, "endColumn": 64}, {"ruleId": "874", "severity": 1, "message": "878", "line": 712, "column": 84, "nodeType": "876", "messageId": "877", "endLine": 712, "endColumn": 86}, {"ruleId": "874", "severity": 1, "message": "875", "line": 713, "column": 46, "nodeType": "876", "messageId": "877", "endLine": 713, "endColumn": 48}, {"ruleId": "874", "severity": 1, "message": "875", "line": 713, "column": 65, "nodeType": "876", "messageId": "877", "endLine": 713, "endColumn": 67}, {"ruleId": "874", "severity": 1, "message": "878", "line": 713, "column": 65, "nodeType": "876", "messageId": "877", "endLine": 713, "endColumn": 67}, {"ruleId": "874", "severity": 1, "message": "878", "line": 713, "column": 87, "nodeType": "876", "messageId": "877", "endLine": 713, "endColumn": 89}, {"ruleId": "715", "severity": 1, "message": "766", "line": 4, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 4, "endColumn": 7}, {"ruleId": "715", "severity": 1, "message": "767", "line": 5, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 5, "endColumn": 14}, {"ruleId": "715", "severity": 1, "message": "751", "line": 49, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 49, "endColumn": 14}, {"ruleId": "715", "severity": 1, "message": "879", "line": 371, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 371, "endColumn": 29}, {"ruleId": "715", "severity": 1, "message": "880", "line": 14, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 14, "endColumn": 16}, {"ruleId": "752", "severity": 1, "message": "881", "line": 49, "column": 6, "nodeType": "754", "endLine": 49, "endColumn": 24, "suggestions": "882"}, {"ruleId": "715", "severity": 1, "message": "756", "line": 16, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 16, "endColumn": 10}, {"ruleId": "715", "severity": 1, "message": "751", "line": 38, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 38, "endColumn": 14}, {"ruleId": "715", "severity": 1, "message": "751", "line": 24, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 24, "endColumn": 14}, {"ruleId": "715", "severity": 1, "message": "746", "line": 26, "column": 27, "nodeType": "717", "messageId": "718", "endLine": 26, "endColumn": 36}, {"ruleId": "715", "severity": 1, "message": "751", "line": 38, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 38, "endColumn": 14}, {"ruleId": "722", "severity": 1, "message": "723", "line": 152, "column": 1, "nodeType": "724", "endLine": 185, "endColumn": 3}, {"ruleId": "722", "severity": 1, "message": "723", "line": 345, "column": 1, "nodeType": "724", "endLine": 352, "endColumn": 3}, {"ruleId": "715", "severity": 1, "message": "883", "line": 22, "column": 16, "nodeType": "717", "messageId": "718", "endLine": 22, "endColumn": 29}, {"ruleId": "715", "severity": 1, "message": "805", "line": 23, "column": 17, "nodeType": "717", "messageId": "718", "endLine": 23, "endColumn": 31}, {"ruleId": "715", "severity": 1, "message": "884", "line": 24, "column": 11, "nodeType": "717", "messageId": "718", "endLine": 24, "endColumn": 19}, {"ruleId": "715", "severity": 1, "message": "885", "line": 25, "column": 13, "nodeType": "717", "messageId": "718", "endLine": 25, "endColumn": 23}, {"ruleId": "715", "severity": 1, "message": "734", "line": 28, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 28, "endColumn": 17}, {"ruleId": "715", "severity": 1, "message": "886", "line": 29, "column": 15, "nodeType": "717", "messageId": "718", "endLine": 29, "endColumn": 27}, {"ruleId": "715", "severity": 1, "message": "887", "line": 163, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 163, "endColumn": 17}, {"ruleId": "715", "severity": 1, "message": "756", "line": 18, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 18, "endColumn": 10}, {"ruleId": "715", "severity": 1, "message": "888", "line": 62, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 62, "endColumn": 21}, {"ruleId": "715", "severity": 1, "message": "766", "line": 11, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 11, "endColumn": 7}, {"ruleId": "752", "severity": 1, "message": "889", "line": 78, "column": 6, "nodeType": "754", "endLine": 78, "endColumn": 69, "suggestions": "890"}, {"ruleId": "752", "severity": 1, "message": "891", "line": 54, "column": 6, "nodeType": "754", "endLine": 54, "endColumn": 10, "suggestions": "892"}, {"ruleId": "715", "severity": 1, "message": "766", "line": 34, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 34, "endColumn": 7}, {"ruleId": "715", "severity": 1, "message": "767", "line": 35, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 35, "endColumn": 14}, {"ruleId": "715", "severity": 1, "message": "814", "line": 37, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 37, "endColumn": 13}, {"ruleId": "715", "severity": 1, "message": "765", "line": 38, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 38, "endColumn": 10}, {"ruleId": "715", "severity": 1, "message": "734", "line": 44, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 44, "endColumn": 17}, {"ruleId": "715", "severity": 1, "message": "893", "line": 45, "column": 13, "nodeType": "717", "messageId": "718", "endLine": 45, "endColumn": 23}, {"ruleId": "715", "severity": 1, "message": "894", "line": 54, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 54, "endColumn": 22}, {"ruleId": "752", "severity": 1, "message": "895", "line": 79, "column": 6, "nodeType": "754", "endLine": 79, "endColumn": 8, "suggestions": "896"}, {"ruleId": "847", "severity": 1, "message": "848", "line": 31, "column": 17, "nodeType": "849", "messageId": "850", "endLine": 31, "endColumn": 18, "suggestions": "897"}, {"ruleId": "847", "severity": 1, "message": "848", "line": 32, "column": 33, "nodeType": "849", "messageId": "850", "endLine": 32, "endColumn": 34, "suggestions": "898"}, {"ruleId": "847", "severity": 1, "message": "899", "line": 33, "column": 22, "nodeType": "849", "messageId": "850", "endLine": 33, "endColumn": 23, "suggestions": "900"}, {"ruleId": "847", "severity": 1, "message": "901", "line": 33, "column": 24, "nodeType": "849", "messageId": "850", "endLine": 33, "endColumn": 25, "suggestions": "902"}, {"ruleId": "847", "severity": 1, "message": "848", "line": 35, "column": 27, "nodeType": "849", "messageId": "850", "endLine": 35, "endColumn": 28, "suggestions": "903"}, {"ruleId": "847", "severity": 1, "message": "904", "line": 38, "column": 33, "nodeType": "849", "messageId": "850", "endLine": 38, "endColumn": 34, "suggestions": "905"}, {"ruleId": "847", "severity": 1, "message": "906", "line": 38, "column": 35, "nodeType": "849", "messageId": "850", "endLine": 38, "endColumn": 36, "suggestions": "907"}, {"ruleId": "847", "severity": 1, "message": "899", "line": 38, "column": 37, "nodeType": "849", "messageId": "850", "endLine": 38, "endColumn": 38, "suggestions": "908"}, {"ruleId": "847", "severity": 1, "message": "901", "line": 38, "column": 39, "nodeType": "849", "messageId": "850", "endLine": 38, "endColumn": 40, "suggestions": "909"}, {"ruleId": "847", "severity": 1, "message": "910", "line": 38, "column": 41, "nodeType": "849", "messageId": "850", "endLine": 38, "endColumn": 42, "suggestions": "911"}, {"ruleId": "847", "severity": 1, "message": "912", "line": 38, "column": 43, "nodeType": "849", "messageId": "850", "endLine": 38, "endColumn": 44, "suggestions": "913"}, {"ruleId": "847", "severity": 1, "message": "904", "line": 39, "column": 25, "nodeType": "849", "messageId": "850", "endLine": 39, "endColumn": 26, "suggestions": "914"}, {"ruleId": "847", "severity": 1, "message": "906", "line": 39, "column": 27, "nodeType": "849", "messageId": "850", "endLine": 39, "endColumn": 28, "suggestions": "915"}, {"ruleId": "847", "severity": 1, "message": "899", "line": 39, "column": 29, "nodeType": "849", "messageId": "850", "endLine": 39, "endColumn": 30, "suggestions": "916"}, {"ruleId": "847", "severity": 1, "message": "901", "line": 39, "column": 31, "nodeType": "849", "messageId": "850", "endLine": 39, "endColumn": 32, "suggestions": "917"}, {"ruleId": "847", "severity": 1, "message": "910", "line": 39, "column": 33, "nodeType": "849", "messageId": "850", "endLine": 39, "endColumn": 34, "suggestions": "918"}, {"ruleId": "847", "severity": 1, "message": "912", "line": 39, "column": 35, "nodeType": "849", "messageId": "850", "endLine": 39, "endColumn": 36, "suggestions": "919"}, {"ruleId": "847", "severity": 1, "message": "904", "line": 385, "column": 55, "nodeType": "849", "messageId": "850", "endLine": 385, "endColumn": 56, "suggestions": "920"}, {"ruleId": "847", "severity": 1, "message": "906", "line": 385, "column": 57, "nodeType": "849", "messageId": "850", "endLine": 385, "endColumn": 58, "suggestions": "921"}, {"ruleId": "847", "severity": 1, "message": "899", "line": 385, "column": 59, "nodeType": "849", "messageId": "850", "endLine": 385, "endColumn": 60, "suggestions": "922"}, {"ruleId": "847", "severity": 1, "message": "901", "line": 385, "column": 61, "nodeType": "849", "messageId": "850", "endLine": 385, "endColumn": 62, "suggestions": "923"}, {"ruleId": "847", "severity": 1, "message": "910", "line": 385, "column": 63, "nodeType": "849", "messageId": "850", "endLine": 385, "endColumn": 64, "suggestions": "924"}, {"ruleId": "847", "severity": 1, "message": "912", "line": 385, "column": 65, "nodeType": "849", "messageId": "850", "endLine": 385, "endColumn": 66, "suggestions": "925"}, {"ruleId": "715", "severity": 1, "message": "883", "line": 25, "column": 16, "nodeType": "717", "messageId": "718", "endLine": 25, "endColumn": 29}, {"ruleId": "715", "severity": 1, "message": "887", "line": 32, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 32, "endColumn": 17}, {"ruleId": "715", "severity": 1, "message": "926", "line": 1, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 1, "endColumn": 18}, {"ruleId": "715", "severity": 1, "message": "926", "line": 1, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 1, "endColumn": 18}, {"ruleId": "715", "severity": 1, "message": "926", "line": 1, "column": 10, "nodeType": "717", "messageId": "718", "endLine": 1, "endColumn": 18}, {"ruleId": "715", "severity": 1, "message": "813", "line": 9, "column": 3, "nodeType": "717", "messageId": "718", "endLine": 9, "endColumn": 8}, {"ruleId": "752", "severity": 1, "message": "927", "line": 60, "column": 6, "nodeType": "754", "endLine": 60, "endColumn": 18, "suggestions": "928"}, {"ruleId": "715", "severity": 1, "message": "855", "line": 41, "column": 17, "nodeType": "717", "messageId": "718", "endLine": 41, "endColumn": 25}, {"ruleId": "715", "severity": 1, "message": "887", "line": 49, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 49, "endColumn": 17}, {"ruleId": "752", "severity": 1, "message": "857", "line": 73, "column": 6, "nodeType": "754", "endLine": 73, "endColumn": 8, "suggestions": "929"}, {"ruleId": "715", "severity": 1, "message": "887", "line": 43, "column": 9, "nodeType": "717", "messageId": "718", "endLine": 43, "endColumn": 17}, {"ruleId": "752", "severity": 1, "message": "930", "line": 59, "column": 6, "nodeType": "754", "endLine": 59, "endColumn": 8, "suggestions": "931"}, "no-unused-vars", "'StoreTypesList' is defined but never used.", "Identifier", "unusedVar", "'StoresList' is defined but never used.", "'ItemMastersList' is defined but never used.", "'BatchItemsList' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'logData' is assigned a value but never used.", "'Collapse' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'CategoryIcon' is defined but never used.", "'AssignmentIcon' is defined but never used.", "'ItemTypeIcon' is defined but never used.", "'ItemCategoryIcon' is defined but never used.", "'GroupIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'ReceiptIcon' is defined but never used.", "'ExitToAppIcon' is defined but never used.", "'InputIcon' is defined but never used.", "'ExpandLessIcon' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ApprovalIcon' is defined but never used.", "'LocalOfferIcon' is defined but never used.", "'MaintenanceIcon' is defined but never used.", "'colors' is defined but never used.", "'shadows' is defined but never used.", "'gradients' is defined but never used.", "'ModernSidebar' is defined but never used.", "'SPACING' is defined but never used.", "'TRANSITIONS' is defined but never used.", "'currentUser' is assigned a value but never used.", "'theme' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchOrgTypes'. Either include it or remove the dependency array.", "ArrayExpression", ["932"], "'Divider' is defined but never used.", "'alpha' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchOrganizations'. Either include it or remove the dependency array.", ["933"], "React Hook useEffect has a missing dependency: 'fetchOffices'. Either include it or remove the dependency array.", ["934"], "'getOrganizations' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchOrganizationDetails'. Either include it or remove the dependency array.", ["935"], "'Tooltip' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'ModernCardGrid' is defined but never used.", "React Hook useEffect has a missing dependency: 'dashboardCards'. Either include it or remove the dependency array.", ["936"], "'cardItem' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSupplierTypes'. Either include it or remove the dependency array.", ["937"], "React Hook useEffect has a missing dependency: 'baseCards'. Either include it or remove the dependency array.", ["938"], "React Hook useEffect has a missing dependency: 'fetchItemTypes'. Either include it or remove the dependency array.", ["939"], "React Hook useEffect has a missing dependency: 'fetchEntryModes'. Either include it or remove the dependency array.", ["940"], "React Hook useEffect has a missing dependency: 'fetchClassifications'. Either include it or remove the dependency array.", ["941"], "React Hook useEffect has a missing dependency: 'fetchItemBrands'. Either include it or remove the dependency array.", ["942"], "React Hook useEffect has a missing dependency: 'fetchItemCategories'. Either include it or remove the dependency array.", ["943"], "React Hook useEffect has a missing dependency: 'fetchUnitsOfMeasure'. Either include it or remove the dependency array.", ["944"], "React Hook useEffect has a missing dependency: 'fetchItemQualities'. Either include it or remove the dependency array.", ["945"], "React Hook useEffect has a missing dependency: 'fetchItemSizes'. Either include it or remove the dependency array.", ["946"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["947"], "React Hook useEffect has a missing dependency: 'loadItemMasters'. Either include it or remove the dependency array.", ["948"], "'getStatusChip' is defined but never used.", "'formatDate' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchItemMasters'. Either include it or remove the dependency array.", ["949"], "'errorInfo' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchBatchItems'. Either include it or remove the dependency array.", ["950"], "'ShareIcon' is defined but never used.", "'TrendingUpIcon' is defined but never used.", "'SecurityIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'ActiveIcon' is defined but never used.", "'InactiveIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadItemMaster' and 'loadRelatedData'. Either include them or remove the dependency array.", ["951"], "'CardHeader' is defined but never used.", "'Alert' is defined but never used.", "'IconButton' is defined but never used.", "'showAdvanced' is assigned a value but never used.", "'setShowAdvanced' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadDropdownOptions'. Either include it or remove the dependency array.", ["952"], "React Hook useEffect has a missing dependency: 'loadItemMaster'. Either include it or remove the dependency array.", ["953"], "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "'FIELD_LABELS' is defined but never used.", "'PLACEHOLDERS' is defined but never used.", "'UI_CONSTANTS' is defined but never used.", "'COLOR_THEMES' is defined but never used.", "'BUTTON_TEXT' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadDropdowns'. Either include it or remove the dependency array.", ["954"], "React Hook useEffect has a missing dependency: 'loadBatchItem'. Either include it or remove the dependency array.", ["955"], "React Hook useEffect has a missing dependency: 'loadBatches'. Either include it or remove the dependency array.", ["956"], "'Rating' is defined but never used.", "'TimelineIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadBatchItem' and 'loadInventoryItems'. Either include them or remove the dependency array.", ["957"], "'getStepIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadDropdownOptions' and 'loadItemMaster'. Either include them or remove the dependency array.", ["958"], "'Chip' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadDropdownOptions' and 'loadInventoryItem'. Either include them or remove the dependency array.", ["959"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "no-useless-escape", "Unnecessary escape character: \\-.", "Literal", "unnecessaryEscape", ["960", "961"], "React Hook useEffect has a missing dependency: 'fetchInventoryItems'. Either include it or remove the dependency array.", ["962"], "'TextField' is defined but never used.", "'ViewIcon' is defined but never used.", "'categories' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["963"], "'useEffect' is defined but never used.", "'testData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'testAllEndpoints'. Either include it or remove the dependency array.", ["964"], "React Hook useEffect has a missing dependency: 'fetchInventoryItem'. Either include it or remove the dependency array.", ["965"], "'response' is assigned a value but never used.", "'Avatar' is defined but never used.", "React Hook useEffect has a missing dependency: 'formik'. Either include it or remove the dependency array.", ["966"], ["967"], "'Paper' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchItems'. Either include it or remove the dependency array.", ["968"], "'StatusCard' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "Unexpected mix of '||' and '&&'. Use parentheses to clarify the intended order of operations.", "'getSupplierTypeColor' is assigned a value but never used.", "'DialogActions' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSupplier'. Either include it or remove the dependency array.", ["969"], "'DashboardIcon' is defined but never used.", "'StarIcon' is defined but never used.", "'PublicIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'navigate' is assigned a value but never used.", "'amharicStyle' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchForms'. Either include it or remove the dependency array.", ["970"], "React Hook useEffect has a missing dependency: 'fetchForm'. Either include it or remove the dependency array.", ["971"], "'RemoveIcon' is defined but never used.", "'formatEthiopianDate' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchAvailableItems' and 'fetchStoresAndShelves'. Either include them or remove the dependency array.", ["972"], ["973", "974"], ["975", "976"], "Unnecessary escape character: \\(.", ["977", "978"], "Unnecessary escape character: \\).", ["979", "980"], ["981", "982"], "Unnecessary escape character: \\..", ["983", "984"], "Unnecessary escape character: \\,.", ["985", "986"], ["987", "988"], ["989", "990"], "Unnecessary escape character: \\/.", ["991", "992"], "Unnecessary escape character: \\&.", ["993", "994"], ["995", "996"], ["997", "998"], ["999", "1000"], ["1001", "1002"], ["1003", "1004"], ["1005", "1006"], ["1007", "1008"], ["1009", "1010"], ["1011", "1012"], ["1013", "1014"], ["1015", "1016"], ["1017", "1018"], "'useState' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadCategories' and 'loadVoucher'. Either include them or remove the dependency array.", ["1019"], ["1020"], "React Hook useEffect has a missing dependency: 'loadStoreTypes'. Either include it or remove the dependency array.", ["1021"], {"desc": "1022", "fix": "1023"}, {"desc": "1024", "fix": "1025"}, {"desc": "1026", "fix": "1027"}, {"desc": "1028", "fix": "1029"}, {"desc": "1030", "fix": "1031"}, {"desc": "1032", "fix": "1033"}, {"desc": "1034", "fix": "1035"}, {"desc": "1036", "fix": "1037"}, {"desc": "1038", "fix": "1039"}, {"desc": "1040", "fix": "1041"}, {"desc": "1042", "fix": "1043"}, {"desc": "1044", "fix": "1045"}, {"desc": "1046", "fix": "1047"}, {"desc": "1048", "fix": "1049"}, {"desc": "1050", "fix": "1051"}, {"desc": "1052", "fix": "1053"}, {"desc": "1054", "fix": "1055"}, {"desc": "1056", "fix": "1057"}, {"desc": "1058", "fix": "1059"}, {"desc": "1060", "fix": "1061"}, {"desc": "1062", "fix": "1063"}, {"desc": "1064", "fix": "1065"}, {"desc": "1066", "fix": "1067"}, {"desc": "1068", "fix": "1069"}, {"desc": "1070", "fix": "1071"}, {"desc": "1072", "fix": "1073"}, {"desc": "1074", "fix": "1075"}, {"desc": "1076", "fix": "1077"}, {"messageId": "1078", "fix": "1079", "desc": "1080"}, {"messageId": "1081", "fix": "1082", "desc": "1083"}, {"desc": "1084", "fix": "1085"}, {"desc": "1086", "fix": "1087"}, {"desc": "1088", "fix": "1089"}, {"desc": "1090", "fix": "1091"}, {"desc": "1092", "fix": "1093"}, {"kind": "1094", "justification": "1095"}, {"desc": "1096", "fix": "1097"}, {"desc": "1098", "fix": "1099"}, {"desc": "1100", "fix": "1101"}, {"desc": "1102", "fix": "1103"}, {"desc": "1104", "fix": "1105"}, {"messageId": "1078", "fix": "1106", "desc": "1080"}, {"messageId": "1081", "fix": "1107", "desc": "1083"}, {"messageId": "1078", "fix": "1108", "desc": "1080"}, {"messageId": "1081", "fix": "1109", "desc": "1083"}, {"messageId": "1078", "fix": "1110", "desc": "1080"}, {"messageId": "1081", "fix": "1111", "desc": "1083"}, {"messageId": "1078", "fix": "1112", "desc": "1080"}, {"messageId": "1081", "fix": "1113", "desc": "1083"}, {"messageId": "1078", "fix": "1114", "desc": "1080"}, {"messageId": "1081", "fix": "1115", "desc": "1083"}, {"messageId": "1078", "fix": "1116", "desc": "1080"}, {"messageId": "1081", "fix": "1117", "desc": "1083"}, {"messageId": "1078", "fix": "1118", "desc": "1080"}, {"messageId": "1081", "fix": "1119", "desc": "1083"}, {"messageId": "1078", "fix": "1120", "desc": "1080"}, {"messageId": "1081", "fix": "1121", "desc": "1083"}, {"messageId": "1078", "fix": "1122", "desc": "1080"}, {"messageId": "1081", "fix": "1123", "desc": "1083"}, {"messageId": "1078", "fix": "1124", "desc": "1080"}, {"messageId": "1081", "fix": "1125", "desc": "1083"}, {"messageId": "1078", "fix": "1126", "desc": "1080"}, {"messageId": "1081", "fix": "1127", "desc": "1083"}, {"messageId": "1078", "fix": "1128", "desc": "1080"}, {"messageId": "1081", "fix": "1129", "desc": "1083"}, {"messageId": "1078", "fix": "1130", "desc": "1080"}, {"messageId": "1081", "fix": "1131", "desc": "1083"}, {"messageId": "1078", "fix": "1132", "desc": "1080"}, {"messageId": "1081", "fix": "1133", "desc": "1083"}, {"messageId": "1078", "fix": "1134", "desc": "1080"}, {"messageId": "1081", "fix": "1135", "desc": "1083"}, {"messageId": "1078", "fix": "1136", "desc": "1080"}, {"messageId": "1081", "fix": "1137", "desc": "1083"}, {"messageId": "1078", "fix": "1138", "desc": "1080"}, {"messageId": "1081", "fix": "1139", "desc": "1083"}, {"messageId": "1078", "fix": "1140", "desc": "1080"}, {"messageId": "1081", "fix": "1141", "desc": "1083"}, {"messageId": "1078", "fix": "1142", "desc": "1080"}, {"messageId": "1081", "fix": "1143", "desc": "1083"}, {"messageId": "1078", "fix": "1144", "desc": "1080"}, {"messageId": "1081", "fix": "1145", "desc": "1083"}, {"messageId": "1078", "fix": "1146", "desc": "1080"}, {"messageId": "1081", "fix": "1147", "desc": "1083"}, {"messageId": "1078", "fix": "1148", "desc": "1080"}, {"messageId": "1081", "fix": "1149", "desc": "1083"}, {"messageId": "1078", "fix": "1150", "desc": "1080"}, {"messageId": "1081", "fix": "1151", "desc": "1083"}, {"desc": "1152", "fix": "1153"}, {"desc": "1086", "fix": "1154"}, {"desc": "1155", "fix": "1156"}, "Update the dependencies array to be: [fetchOrgTypes]", {"range": "1157", "text": "1158"}, "Update the dependencies array to be: [fetchOrganizations]", {"range": "1159", "text": "1160"}, "Update the dependencies array to be: [fetchOffices]", {"range": "1161", "text": "1162"}, "Update the dependencies array to be: [fetchOrganizationDetails, id]", {"range": "1163", "text": "1164"}, "Update the dependencies array to be: [dashboardCards]", {"range": "1165", "text": "1166"}, "Update the dependencies array to be: [fetchSupplierTypes, page, rowsPerPage, searchTerm]", {"range": "1167", "text": "1168"}, "Update the dependencies array to be: [baseCards]", {"range": "1169", "text": "1170"}, "Update the dependencies array to be: [fetchItemTypes]", {"range": "1171", "text": "1172"}, "Update the dependencies array to be: [fetchEntryModes]", {"range": "1173", "text": "1174"}, "Update the dependencies array to be: [fetchClassifications]", {"range": "1175", "text": "1176"}, "Update the dependencies array to be: [fetchItemBrands]", {"range": "1177", "text": "1178"}, "Update the dependencies array to be: [fetchItemCategories]", {"range": "1179", "text": "1180"}, "Update the dependencies array to be: [fetchUnitsOfMeasure]", {"range": "1181", "text": "1182"}, "Update the dependencies array to be: [fetchItemQualities]", {"range": "1183", "text": "1184"}, "Update the dependencies array to be: [fetchItemSizes]", {"range": "1185", "text": "1186"}, "Update the dependencies array to be: [fetchData]", {"range": "1187", "text": "1188"}, "Update the dependencies array to be: [loadItemMasters, page, rowsPerPage, searchTerm]", {"range": "1189", "text": "1190"}, "Update the dependencies array to be: [page, rowsPerPage, searchTerm, typeFilter, categoryFilter, fetchItemMasters]", {"range": "1191", "text": "1192"}, "Update the dependencies array to be: [fetchBatchItems, page, rowsPerPage, searchTerm, statusFilter]", {"range": "1193", "text": "1194"}, "Update the dependencies array to be: [id, loadItemMaster, loadRelatedData]", {"range": "1195", "text": "1196"}, "Update the dependencies array to be: [loadDropdownOptions]", {"range": "1197", "text": "1198"}, "Update the dependencies array to be: [id, isEdit, loadItemMaster]", {"range": "1199", "text": "1200"}, "Update the dependencies array to be: [id, isEdit, loadDropdowns]", {"range": "1201", "text": "1202"}, "Update the dependencies array to be: [dropdownsLoaded, isEdit, id, loadBatchItem]", {"range": "1203", "text": "1204"}, "Update the dependencies array to be: [loadBatches, page, rowsPerPage, searchTerm]", {"range": "1205", "text": "1206"}, "Update the dependencies array to be: [id, loadBatchItem, loadInventoryItems]", {"range": "1207", "text": "1208"}, "Update the dependencies array to be: [id, isEdit, loadDropdownOptions, loadItemMaster]", {"range": "1209", "text": "1210"}, "Update the dependencies array to be: [id, isEdit, loadDropdownOptions, loadInventoryItem]", {"range": "1211", "text": "1212"}, "removeEscape", {"range": "1213", "text": "1095"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1214", "text": "1215"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [fetchInventoryItems, page, rowsPerPage, searchTerm, statusFilter]", {"range": "1216", "text": "1217"}, "Update the dependencies array to be: [loadData]", {"range": "1218", "text": "1219"}, "Update the dependencies array to be: [testAllEndpoints]", {"range": "1220", "text": "1221"}, "Update the dependencies array to be: [fetchInventoryItem, id]", {"range": "1222", "text": "1223"}, "Update the dependencies array to be: [formik, formik.values.organization, selectedOrgId]", {"range": "1224", "text": "1225"}, "directive", "", "Update the dependencies array to be: [fetchItems]", {"range": "1226", "text": "1227"}, "Update the dependencies array to be: [fetchSupplier, open, supplierId]", {"range": "1228", "text": "1229"}, "Update the dependencies array to be: [page, rowsPerPage, searchTerm, statusFilter, departmentFilter, fetchForms]", {"range": "1230", "text": "1231"}, "Update the dependencies array to be: [fetchForm, id]", {"range": "1232", "text": "1233"}, "Update the dependencies array to be: [fetchAvailableItems, fetchStoresAndShelves]", {"range": "1234", "text": "1235"}, {"range": "1236", "text": "1095"}, {"range": "1237", "text": "1215"}, {"range": "1238", "text": "1095"}, {"range": "1239", "text": "1215"}, {"range": "1240", "text": "1095"}, {"range": "1241", "text": "1215"}, {"range": "1242", "text": "1095"}, {"range": "1243", "text": "1215"}, {"range": "1244", "text": "1095"}, {"range": "1245", "text": "1215"}, {"range": "1246", "text": "1095"}, {"range": "1247", "text": "1215"}, {"range": "1248", "text": "1095"}, {"range": "1249", "text": "1215"}, {"range": "1250", "text": "1095"}, {"range": "1251", "text": "1215"}, {"range": "1252", "text": "1095"}, {"range": "1253", "text": "1215"}, {"range": "1254", "text": "1095"}, {"range": "1255", "text": "1215"}, {"range": "1256", "text": "1095"}, {"range": "1257", "text": "1215"}, {"range": "1258", "text": "1095"}, {"range": "1259", "text": "1215"}, {"range": "1260", "text": "1095"}, {"range": "1261", "text": "1215"}, {"range": "1262", "text": "1095"}, {"range": "1263", "text": "1215"}, {"range": "1264", "text": "1095"}, {"range": "1265", "text": "1215"}, {"range": "1266", "text": "1095"}, {"range": "1267", "text": "1215"}, {"range": "1268", "text": "1095"}, {"range": "1269", "text": "1215"}, {"range": "1270", "text": "1095"}, {"range": "1271", "text": "1215"}, {"range": "1272", "text": "1095"}, {"range": "1273", "text": "1215"}, {"range": "1274", "text": "1095"}, {"range": "1275", "text": "1215"}, {"range": "1276", "text": "1095"}, {"range": "1277", "text": "1215"}, {"range": "1278", "text": "1095"}, {"range": "1279", "text": "1215"}, {"range": "1280", "text": "1095"}, {"range": "1281", "text": "1215"}, "Update the dependencies array to be: [id, isEdit, loadCategories, loadVoucher]", {"range": "1282", "text": "1283"}, {"range": "1284", "text": "1219"}, "Update the dependencies array to be: [loadStoreTypes]", {"range": "1285", "text": "1286"}, [1902, 1904], "[fetchOrgTypes]", [1929, 1931], "[fetchOrganizations]", [1811, 1813], "[fetchOffices]", [1504, 1508], "[fetchOrganizationDetails, id]", [7436, 7438], "[dashboardCards]", [1347, 1378], "[fetchSupplierTypes, page, rowsPerPage, searchTerm]", [7227, 7229], "[baseCards]", [1402, 1404], "[fetchItemTypes]", [1575, 1577], "[fetchEntryModes]", [1900, 1902], "[fetchClassifications]", [1447, 1449], "[fetchItemBrands]", [1439, 1441], "[fetchItemCategories]", [1448, 1450], "[fetchUnitsOfMeasure]", [1493, 1495], "[fetchItemQualities]", [1410, 1412], "[fetchItemSizes]", [2033, 2035], "[fetchData]", [1688, 1719], "[loadItemMasters, page, rowsPerPage, searchTerm]", [2079, 2138], "[page, rowsPerPage, searchTerm, typeFilter, categoryFilter, fetchItemMasters]", [2237, 2282], "[fetchBatchItems, page, rowsPerPage, searchTerm, statusFilter]", [2593, 2597], "[id, loadItemMaster, loadRelatedData]", [3963, 3965], "[loadDropdownOptions]", [4075, 4087], "[id, isEdit, loadItemMaster]", [12532, 12544], "[id, isEdit, loadDropdowns]", [12752, 12781], "[dropdownsLoaded, isEdit, id, loadBatchItem]", [2003, 2034], "[loadBatches, page, rowsPerPage, searchTerm]", [1638, 1642], "[id, loadBatchItem, loadInventoryItems]", [18848, 18860], "[id, isEdit, loadDropdownOptions, loadItemMaster]", [2957, 2969], "[id, isEdit, loadDropdownOptions, loadInventoryItem]", [9121, 9122], [9121, 9121], "\\", [1883, 1928], "[fetchInventoryItems, page, rowsPerPage, searchTerm, statusFilter]", [1338, 1340], "[loadData]", [2781, 2783], "[testAllEndpoints]", [1336, 1340], "[fetchInventoryItem, id]", [4428, 4471], "[formik, formik.values.organization, selectedOrgId]", [3014, 3016], "[fetchItems]", [1010, 1028], "[fetchSupplier, open, supplierId]", [1750, 1813], "[page, rowsPerPage, searchTerm, statusFilter, departmentFilter, fetchForms]", [1165, 1169], "[fetchForm, id]", [1757, 1759], "[fetchAvailableItems, fetchStoresAndShelves]", [651, 652], [651, 651], [696, 697], [696, 696], [730, 731], [730, 730], [732, 733], [732, 732], [800, 801], [800, 800], [937, 938], [937, 937], [939, 940], [939, 939], [941, 942], [941, 941], [943, 944], [943, 943], [945, 946], [945, 945], [947, 948], [947, 947], [979, 980], [979, 979], [981, 982], [981, 981], [983, 984], [983, 983], [985, 986], [985, 985], [987, 988], [987, 987], [989, 990], [989, 989], [10454, 10455], [10454, 10454], [10456, 10457], [10456, 10456], [10458, 10459], [10458, 10458], [10460, 10461], [10460, 10460], [10462, 10463], [10462, 10462], [10464, 10465], [10464, 10464], [1314, 1326], "[id, isEdit, loadCategories, loadVoucher]", [1618, 1620], [1350, 1352], "[loadStoreTypes]"]