[{"D:\\Projects v2\\asset management\\frontend\\src\\index.js": "1", "D:\\Projects v2\\asset management\\frontend\\src\\reportWebVitals.js": "2", "D:\\Projects v2\\asset management\\frontend\\src\\App.js": "3", "D:\\Projects v2\\asset management\\frontend\\src\\theme\\designSystem.js": "4", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandler.js": "5", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ErrorBoundary.js": "6", "D:\\Projects v2\\asset management\\frontend\\src\\components\\Layout.js": "7", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeList.js": "8", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\OrganizationMenu.js": "9", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationList.js": "10", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeList.js": "11", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDetail.js": "12", "D:\\Projects v2\\asset management\\frontend\\src\\features\\suppliers\\SuppliersMenu.js": "13", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ApprovalStatusPage.js": "14", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupDashboard.js": "15", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierTypesList.js": "16", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemStatusPage.js": "17", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupMenu.js": "18", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTagPage.js": "19", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\PropertyStatusPage.js": "20", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTypePage.js": "21", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\EntryModePage.js": "22", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\MainClassificationPage.js": "23", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemBrandPage.js": "24", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemCategoryPage.js": "25", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\UnitOfMeasurePage.js": "26", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemQualityPage.js": "27", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemSizePage.js": "28", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\SubClassificationPage.js": "29", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\GenericInventoryPage.js": "30", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemsDashboard.js": "31", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMastersList.js": "32", "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\MainDashboardMenu.js": "33", "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\Dashboard.js": "34", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardItemMastersList.js": "35", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardBatchItemsList.js": "36", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMasterDetail.js": "37", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemManagementMenu.js": "38", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ProfessionalItemMasterForm.js": "39", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemForm.js": "40", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemsList.js": "41", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemDetail.js": "42", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ModernItemMasterForm.js": "43", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemForm.js": "44", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemsList.js": "45", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVouchersList.js": "46", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\EndToEndTest.js": "47", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\DebugInventoryAPI.js": "48", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemDetail.js": "49", "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\MaintenanceSchedule.js": "50", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\index.js": "51", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\index.js": "52", "D:\\Projects v2\\asset management\\frontend\\src\\features\\debug\\APITest.js": "53", "D:\\Projects v2\\asset management\\frontend\\src\\features\\auth\\Login.js": "54", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\index.js": "55", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\axios.js": "56", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandling.js": "57", "D:\\Projects v2\\asset management\\frontend\\src\\theme\\professionalTheme.js": "58", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeDialog.js": "59", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeDialog.js": "60", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDialog.js": "61", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\permissions.js": "62", "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\StatusManagement.js": "63", "D:\\Projects v2\\asset management\\frontend\\src\\components\\DashboardBanner.js": "64", "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\GatesList.js": "65", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SuppliersList.js": "66", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDetailView.js": "67", "D:\\Projects v2\\asset management\\frontend\\src\\services\\auth.js": "68", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernSidebar.js": "69", "D:\\Projects v2\\asset management\\frontend\\src\\services\\organizations.js": "70", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernBreadcrumbs.js": "71", "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernCardGrid.js": "72", "D:\\Projects v2\\asset management\\frontend\\src\\services\\suppliers.js": "73", "D:\\Projects v2\\asset management\\frontend\\src\\services\\inventorySetup.js": "74", "D:\\Projects v2\\asset management\\frontend\\src\\services\\items.js": "75", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDashboard.js": "76", "D:\\Projects v2\\asset management\\frontend\\src\\services\\supplier.js": "77", "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierCategoriesList.js": "78", "D:\\Projects v2\\asset management\\frontend\\src\\config\\itemManagementDesign.js": "79", "D:\\Projects v2\\asset management\\frontend\\src\\config\\formConfig.js": "80", "D:\\Projects v2\\asset management\\frontend\\src\\config\\constants.js": "81", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormTemplate.js": "82", "D:\\Projects v2\\asset management\\frontend\\src\\services\\model19.js": "83", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormsList.js": "84", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19Menu.js": "85", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormDetail.js": "86", "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormCreate.js": "87", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\filters.js": "88", "D:\\Projects v2\\asset management\\frontend\\src\\components\\forms\\FormValidation.js": "89", "D:\\Projects v2\\asset management\\frontend\\src\\utils\\validation.js": "90"}, {"size": 618, "mtime": 1749121146000, "results": "91", "hashOfConfig": "92"}, {"size": 362, "mtime": 1744151842000, "results": "93", "hashOfConfig": "92"}, {"size": 34451, "mtime": 1750946934284, "results": "94", "hashOfConfig": "92"}, {"size": 7358, "mtime": 1749460684000, "results": "95", "hashOfConfig": "92"}, {"size": 14601, "mtime": 1750941396982, "results": "96", "hashOfConfig": "92"}, {"size": 7808, "mtime": 1750503868000, "results": "97", "hashOfConfig": "92"}, {"size": 18491, "mtime": 1750062608000, "results": "98", "hashOfConfig": "92"}, {"size": 7248, "mtime": 1750100376000, "results": "99", "hashOfConfig": "92"}, {"size": 6567, "mtime": 1750588146000, "results": "100", "hashOfConfig": "92"}, {"size": 7649, "mtime": 1750100362000, "results": "101", "hashOfConfig": "92"}, {"size": 7291, "mtime": 1750100348000, "results": "102", "hashOfConfig": "92"}, {"size": 20275, "mtime": 1750497690000, "results": "103", "hashOfConfig": "92"}, {"size": 5553, "mtime": 1750587110000, "results": "104", "hashOfConfig": "92"}, {"size": 1543, "mtime": 1749321316000, "results": "105", "hashOfConfig": "92"}, {"size": 17426, "mtime": 1750482586000, "results": "106", "hashOfConfig": "92"}, {"size": 9822, "mtime": 1750587406000, "results": "107", "hashOfConfig": "92"}, {"size": 1446, "mtime": 1749321274000, "results": "108", "hashOfConfig": "92"}, {"size": 14437, "mtime": 1750573144000, "results": "109", "hashOfConfig": "92"}, {"size": 1939, "mtime": 1749321336000, "results": "110", "hashOfConfig": "92"}, {"size": 1456, "mtime": 1749321296000, "results": "111", "hashOfConfig": "92"}, {"size": 12481, "mtime": 1750495760000, "results": "112", "hashOfConfig": "92"}, {"size": 14492, "mtime": 1750485988000, "results": "113", "hashOfConfig": "92"}, {"size": 16270, "mtime": 1750495734000, "results": "114", "hashOfConfig": "92"}, {"size": 13843, "mtime": 1750495814000, "results": "115", "hashOfConfig": "92"}, {"size": 12194, "mtime": 1750485914000, "results": "116", "hashOfConfig": "92"}, {"size": 12917, "mtime": 1750497900000, "results": "117", "hashOfConfig": "92"}, {"size": 13503, "mtime": 1750486292000, "results": "118", "hashOfConfig": "92"}, {"size": 11930, "mtime": 1750486214000, "results": "119", "hashOfConfig": "92"}, {"size": 16201, "mtime": 1750485170000, "results": "120", "hashOfConfig": "92"}, {"size": 6298, "mtime": 1749460684000, "results": "121", "hashOfConfig": "92"}, {"size": 14558, "mtime": 1749494266000, "results": "122", "hashOfConfig": "92"}, {"size": 13420, "mtime": 1750947652189, "results": "123", "hashOfConfig": "92"}, {"size": 6934, "mtime": 1750590990000, "results": "124", "hashOfConfig": "92"}, {"size": 11114, "mtime": 1749460684000, "results": "125", "hashOfConfig": "92"}, {"size": 21268, "mtime": 1750532258000, "results": "126", "hashOfConfig": "92"}, {"size": 24046, "mtime": 1750503298000, "results": "127", "hashOfConfig": "92"}, {"size": 38782, "mtime": 1749541954000, "results": "128", "hashOfConfig": "92"}, {"size": 12462, "mtime": 1750573110000, "results": "129", "hashOfConfig": "92"}, {"size": 45490, "mtime": 1750947122266, "results": "130", "hashOfConfig": "92"}, {"size": 37554, "mtime": 1750059786000, "results": "131", "hashOfConfig": "92"}, {"size": 19827, "mtime": 1749538128000, "results": "132", "hashOfConfig": "92"}, {"size": 25161, "mtime": 1749538944000, "results": "133", "hashOfConfig": "92"}, {"size": 39922, "mtime": 1750999036332, "results": "134", "hashOfConfig": "92"}, {"size": 36856, "mtime": 1750578874000, "results": "135", "hashOfConfig": "92"}, {"size": 19936, "mtime": 1750592184000, "results": "136", "hashOfConfig": "92"}, {"size": 2363, "mtime": 1750502816000, "results": "137", "hashOfConfig": "92"}, {"size": 12291, "mtime": 1749576472000, "results": "138", "hashOfConfig": "92"}, {"size": 6165, "mtime": 1749574028000, "results": "139", "hashOfConfig": "92"}, {"size": 18044, "mtime": 1750499802000, "results": "140", "hashOfConfig": "92"}, {"size": 1622, "mtime": 1749493532000, "results": "141", "hashOfConfig": "92"}, {"size": 323, "mtime": 1750588036000, "results": "142", "hashOfConfig": "92"}, {"size": 430, "mtime": 1750587482000, "results": "143", "hashOfConfig": "92"}, {"size": 6925, "mtime": 1750572170000, "results": "144", "hashOfConfig": "92"}, {"size": 15577, "mtime": 1749244308000, "results": "145", "hashOfConfig": "92"}, {"size": 415, "mtime": 1750592080000, "results": "146", "hashOfConfig": "92"}, {"size": 1263, "mtime": 1749243990000, "results": "147", "hashOfConfig": "92"}, {"size": 8607, "mtime": 1750503624000, "results": "148", "hashOfConfig": "92"}, {"size": 7700, "mtime": 1750061828000, "results": "149", "hashOfConfig": "92"}, {"size": 8779, "mtime": 1744341666000, "results": "150", "hashOfConfig": "92"}, {"size": 3425, "mtime": 1744162122000, "results": "151", "hashOfConfig": "92"}, {"size": 14617, "mtime": 1749196086000, "results": "152", "hashOfConfig": "92"}, {"size": 8029, "mtime": 1749493422000, "results": "153", "hashOfConfig": "92"}, {"size": 28449, "mtime": 1750482954000, "results": "154", "hashOfConfig": "92"}, {"size": 12377, "mtime": 1749244482000, "results": "155", "hashOfConfig": "92"}, {"size": 14628, "mtime": 1750497014000, "results": "156", "hashOfConfig": "92"}, {"size": 35316, "mtime": 1749460684000, "results": "157", "hashOfConfig": "92"}, {"size": 16392, "mtime": 1749259392000, "results": "158", "hashOfConfig": "92"}, {"size": 1272, "mtime": 1748021328000, "results": "159", "hashOfConfig": "92"}, {"size": 11325, "mtime": 1749460684000, "results": "160", "hashOfConfig": "92"}, {"size": 7867, "mtime": 1750670878000, "results": "161", "hashOfConfig": "92"}, {"size": 6155, "mtime": 1749460684000, "results": "162", "hashOfConfig": "92"}, {"size": 11465, "mtime": 1749460684000, "results": "163", "hashOfConfig": "92"}, {"size": 6947, "mtime": 1750587052000, "results": "164", "hashOfConfig": "92"}, {"size": 6776, "mtime": 1750943098693, "results": "165", "hashOfConfig": "92"}, {"size": 9892, "mtime": 1750573370000, "results": "166", "hashOfConfig": "92"}, {"size": 14652, "mtime": 1749486778000, "results": "167", "hashOfConfig": "92"}, {"size": 7916, "mtime": 1749460684000, "results": "168", "hashOfConfig": "92"}, {"size": 9563, "mtime": 1749258762000, "results": "169", "hashOfConfig": "92"}, {"size": 7273, "mtime": 1750502106000, "results": "170", "hashOfConfig": "92"}, {"size": 11010, "mtime": 1750957135442, "results": "171", "hashOfConfig": "92"}, {"size": 2951, "mtime": 1750590930000, "results": "172", "hashOfConfig": "92"}, {"size": 15459, "mtime": 1750672966000, "results": "173", "hashOfConfig": "92"}, {"size": 8010, "mtime": 1750626786000, "results": "174", "hashOfConfig": "92"}, {"size": 16382, "mtime": 1750591964000, "results": "175", "hashOfConfig": "92"}, {"size": 7365, "mtime": 1750591030000, "results": "176", "hashOfConfig": "92"}, {"size": 12742, "mtime": 1750591874000, "results": "177", "hashOfConfig": "92"}, {"size": 24262, "mtime": 1750755554000, "results": "178", "hashOfConfig": "92"}, {"size": 2271, "mtime": 1744341630000, "results": "179", "hashOfConfig": "92"}, {"size": 9322, "mtime": 1749321190000, "results": "180", "hashOfConfig": "92"}, {"size": 14293, "mtime": 1750940433844, "results": "181", "hashOfConfig": "92"}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gs8ssj", {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Projects v2\\asset management\\frontend\\src\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\App.js", ["452", "453"], [], "D:\\Projects v2\\asset management\\frontend\\src\\theme\\designSystem.js", ["454"], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandler.js", ["455"], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ErrorBoundary.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\Layout.js", ["456", "457", "458", "459", "460", "461", "462", "463", "464", "465", "466", "467", "468", "469", "470", "471", "472", "473", "474", "475", "476", "477", "478", "479", "480"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeList.js", ["481", "482"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\OrganizationMenu.js", ["483"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationList.js", ["484", "485", "486"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeList.js", ["487", "488"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDetail.js", ["489", "490", "491", "492"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\suppliers\\SuppliersMenu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ApprovalStatusPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupDashboard.js", ["493", "494", "495", "496", "497", "498", "499"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierTypesList.js", ["500", "501"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemStatusPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\InventorySetupMenu.js", ["502"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTagPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\PropertyStatusPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemTypePage.js", ["503"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\EntryModePage.js", ["504"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\MainClassificationPage.js", ["505", "506", "507"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemBrandPage.js", ["508"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemCategoryPage.js", ["509"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\UnitOfMeasurePage.js", ["510"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemQualityPage.js", ["511"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\ItemSizePage.js", ["512"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\SubClassificationPage.js", ["513", "514", "515"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\GenericInventoryPage.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemsDashboard.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMastersList.js", ["516"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\MainDashboardMenu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\dashboard\\Dashboard.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardItemMastersList.js", ["517", "518", "519", "520", "521", "522"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\StandardBatchItemsList.js", ["523", "524", "525", "526"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemMasterDetail.js", ["527", "528", "529", "530", "531", "532", "533"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ItemManagementMenu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ProfessionalItemMasterForm.js", ["534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemForm.js", ["549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemsList.js", ["561", "562", "563"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\BatchItemDetail.js", ["564", "565", "566"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\ModernItemMasterForm.js", ["567", "568", "569", "570", "571", "572", "573", "574", "575"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemForm.js", ["576", "577", "578", "579", "580", "581", "582"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemsList.js", ["583", "584", "585"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\SerialVouchersList.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\EndToEndTest.js", ["586", "587"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\DebugInventoryAPI.js", ["588"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\InventoryItemDetail.js", ["589"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\items\\MaintenanceSchedule.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\debug\\APITest.js", ["590"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\auth\\Login.js", ["591"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\index.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\axios.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\errorHandling.js", ["592"], [], "D:\\Projects v2\\asset management\\frontend\\src\\theme\\professionalTheme.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OfficeDialog.js", [], ["593"], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationTypeDialog.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organizations\\OrganizationDialog.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\permissions.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\inventorySetup\\StatusManagement.js", ["594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605"], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\DashboardBanner.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\organization\\GatesList.js", ["606", "607", "608"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SuppliersList.js", ["609"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDetailView.js", ["610", "611"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\auth.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernSidebar.js", ["612", "613"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\organizations.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernBreadcrumbs.js", ["614"], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\ui\\ModernCardGrid.js", ["615", "616"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\suppliers.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\inventorySetup.js", ["617"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\items.js", ["618"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierDashboard.js", ["619", "620", "621", "622", "623", "624", "625"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\supplier.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\supplier\\SupplierCategoriesList.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\config\\itemManagementDesign.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\config\\formConfig.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\config\\constants.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormTemplate.js", ["626", "627"], [], "D:\\Projects v2\\asset management\\frontend\\src\\services\\model19.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormsList.js", ["628", "629"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19Menu.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormDetail.js", ["630"], [], "D:\\Projects v2\\asset management\\frontend\\src\\features\\model19\\Model19FormCreate.js", ["631", "632", "633", "634", "635", "636", "637", "638"], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\filters.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\components\\forms\\FormValidation.js", [], [], "D:\\Projects v2\\asset management\\frontend\\src\\utils\\validation.js", ["639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661"], [], {"ruleId": "662", "severity": 1, "message": "663", "line": 60, "column": 8, "nodeType": "664", "messageId": "665", "endLine": 60, "endColumn": 23}, {"ruleId": "662", "severity": 1, "message": "666", "line": 67, "column": 8, "nodeType": "664", "messageId": "665", "endLine": 67, "endColumn": 22}, {"ruleId": "667", "severity": 1, "message": "668", "line": 305, "column": 1, "nodeType": "669", "endLine": 315, "endColumn": 3}, {"ruleId": "662", "severity": 1, "message": "670", "line": 284, "column": 11, "nodeType": "664", "messageId": "665", "endLine": 284, "endColumn": 18}, {"ruleId": "662", "severity": 1, "message": "671", "line": 21, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 21, "endColumn": 11}, {"ruleId": "662", "severity": 1, "message": "672", "line": 22, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 22, "endColumn": 7}, {"ruleId": "662", "severity": 1, "message": "673", "line": 23, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 23, "endColumn": 11}, {"ruleId": "662", "severity": 1, "message": "674", "line": 34, "column": 15, "nodeType": "664", "messageId": "665", "endLine": 34, "endColumn": 27}, {"ruleId": "662", "severity": 1, "message": "675", "line": 35, "column": 17, "nodeType": "664", "messageId": "665", "endLine": 35, "endColumn": 31}, {"ruleId": "662", "severity": 1, "message": "676", "line": 41, "column": 15, "nodeType": "664", "messageId": "665", "endLine": 41, "endColumn": 27}, {"ruleId": "662", "severity": 1, "message": "677", "line": 42, "column": 12, "nodeType": "664", "messageId": "665", "endLine": 42, "endColumn": 28}, {"ruleId": "662", "severity": 1, "message": "678", "line": 43, "column": 12, "nodeType": "664", "messageId": "665", "endLine": 43, "endColumn": 21}, {"ruleId": "662", "severity": 1, "message": "679", "line": 44, "column": 10, "nodeType": "664", "messageId": "665", "endLine": 44, "endColumn": 17}, {"ruleId": "662", "severity": 1, "message": "680", "line": 45, "column": 14, "nodeType": "664", "messageId": "665", "endLine": 45, "endColumn": 25}, {"ruleId": "662", "severity": 1, "message": "681", "line": 46, "column": 16, "nodeType": "664", "messageId": "665", "endLine": 46, "endColumn": 29}, {"ruleId": "662", "severity": 1, "message": "682", "line": 47, "column": 12, "nodeType": "664", "messageId": "665", "endLine": 47, "endColumn": 21}, {"ruleId": "662", "severity": 1, "message": "683", "line": 48, "column": 17, "nodeType": "664", "messageId": "665", "endLine": 48, "endColumn": 31}, {"ruleId": "662", "severity": 1, "message": "684", "line": 49, "column": 17, "nodeType": "664", "messageId": "665", "endLine": 49, "endColumn": 31}, {"ruleId": "662", "severity": 1, "message": "685", "line": 50, "column": 18, "nodeType": "664", "messageId": "665", "endLine": 50, "endColumn": 33}, {"ruleId": "662", "severity": 1, "message": "686", "line": 51, "column": 15, "nodeType": "664", "messageId": "665", "endLine": 51, "endColumn": 27}, {"ruleId": "662", "severity": 1, "message": "687", "line": 52, "column": 17, "nodeType": "664", "messageId": "665", "endLine": 52, "endColumn": 31}, {"ruleId": "662", "severity": 1, "message": "688", "line": 54, "column": 12, "nodeType": "664", "messageId": "665", "endLine": 54, "endColumn": 27}, {"ruleId": "662", "severity": 1, "message": "689", "line": 61, "column": 10, "nodeType": "664", "messageId": "665", "endLine": 61, "endColumn": 16}, {"ruleId": "662", "severity": 1, "message": "690", "line": 61, "column": 18, "nodeType": "664", "messageId": "665", "endLine": 61, "endColumn": 25}, {"ruleId": "662", "severity": 1, "message": "691", "line": 61, "column": 27, "nodeType": "664", "messageId": "665", "endLine": 61, "endColumn": 36}, {"ruleId": "662", "severity": 1, "message": "692", "line": 62, "column": 8, "nodeType": "664", "messageId": "665", "endLine": 62, "endColumn": 21}, {"ruleId": "662", "severity": 1, "message": "693", "line": 63, "column": 18, "nodeType": "664", "messageId": "665", "endLine": 63, "endColumn": 25}, {"ruleId": "662", "severity": 1, "message": "694", "line": 63, "column": 39, "nodeType": "664", "messageId": "665", "endLine": 63, "endColumn": 50}, {"ruleId": "662", "severity": 1, "message": "695", "line": 204, "column": 10, "nodeType": "664", "messageId": "665", "endLine": 204, "endColumn": 21}, {"ruleId": "662", "severity": 1, "message": "696", "line": 36, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 36, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "698", "line": 67, "column": 6, "nodeType": "699", "endLine": 67, "endColumn": 8, "suggestions": "700"}, {"ruleId": "662", "severity": 1, "message": "701", "line": 16, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 16, "endColumn": 10}, {"ruleId": "662", "severity": 1, "message": "702", "line": 21, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 21, "endColumn": 8}, {"ruleId": "662", "severity": 1, "message": "696", "line": 40, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 40, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "703", "line": 73, "column": 6, "nodeType": "699", "endLine": 73, "endColumn": 8, "suggestions": "704"}, {"ruleId": "662", "severity": 1, "message": "696", "line": 36, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 36, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "705", "line": 67, "column": 6, "nodeType": "699", "endLine": 67, "endColumn": 8, "suggestions": "706"}, {"ruleId": "662", "severity": 1, "message": "701", "line": 23, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 23, "endColumn": 10}, {"ruleId": "662", "severity": 1, "message": "707", "line": 40, "column": 10, "nodeType": "664", "messageId": "665", "endLine": 40, "endColumn": 26}, {"ruleId": "662", "severity": 1, "message": "696", "line": 43, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 43, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "708", "line": 64, "column": 6, "nodeType": "699", "endLine": 64, "endColumn": 10, "suggestions": "709"}, {"ruleId": "662", "severity": 1, "message": "710", "line": 13, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 13, "endColumn": 10}, {"ruleId": "662", "severity": 1, "message": "711", "line": 19, "column": 10, "nodeType": "664", "messageId": "665", "endLine": 19, "endColumn": 14}, {"ruleId": "662", "severity": 1, "message": "712", "line": 19, "column": 16, "nodeType": "664", "messageId": "665", "endLine": 19, "endColumn": 27}, {"ruleId": "662", "severity": 1, "message": "713", "line": 21, "column": 8, "nodeType": "664", "messageId": "665", "endLine": 21, "endColumn": 22}, {"ruleId": "662", "severity": 1, "message": "696", "line": 64, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 64, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "714", "line": 277, "column": 6, "nodeType": "699", "endLine": 277, "endColumn": 8, "suggestions": "715"}, {"ruleId": "662", "severity": 1, "message": "716", "line": 284, "column": 11, "nodeType": "664", "messageId": "665", "endLine": 284, "endColumn": 19}, {"ruleId": "662", "severity": 1, "message": "711", "line": 11, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 11, "endColumn": 7}, {"ruleId": "697", "severity": 1, "message": "717", "line": 58, "column": 6, "nodeType": "699", "endLine": 58, "endColumn": 37, "suggestions": "718"}, {"ruleId": "697", "severity": 1, "message": "719", "line": 232, "column": 6, "nodeType": "699", "endLine": 232, "endColumn": 8, "suggestions": "720"}, {"ruleId": "697", "severity": 1, "message": "721", "line": 66, "column": 6, "nodeType": "699", "endLine": 66, "endColumn": 8, "suggestions": "722"}, {"ruleId": "697", "severity": 1, "message": "723", "line": 75, "column": 6, "nodeType": "699", "endLine": 75, "endColumn": 8, "suggestions": "724"}, {"ruleId": "662", "severity": 1, "message": "711", "line": 10, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 10, "endColumn": 7}, {"ruleId": "662", "severity": 1, "message": "712", "line": 11, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 11, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "725", "line": 84, "column": 6, "nodeType": "699", "endLine": 84, "endColumn": 8, "suggestions": "726"}, {"ruleId": "697", "severity": 1, "message": "727", "line": 68, "column": 6, "nodeType": "699", "endLine": 68, "endColumn": 8, "suggestions": "728"}, {"ruleId": "697", "severity": 1, "message": "729", "line": 66, "column": 6, "nodeType": "699", "endLine": 66, "endColumn": 8, "suggestions": "730"}, {"ruleId": "697", "severity": 1, "message": "731", "line": 67, "column": 6, "nodeType": "699", "endLine": 67, "endColumn": 8, "suggestions": "732"}, {"ruleId": "697", "severity": 1, "message": "733", "line": 68, "column": 6, "nodeType": "699", "endLine": 68, "endColumn": 8, "suggestions": "734"}, {"ruleId": "697", "severity": 1, "message": "735", "line": 66, "column": 6, "nodeType": "699", "endLine": 66, "endColumn": 8, "suggestions": "736"}, {"ruleId": "662", "severity": 1, "message": "711", "line": 10, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 10, "endColumn": 7}, {"ruleId": "662", "severity": 1, "message": "712", "line": 11, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 11, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "737", "line": 89, "column": 6, "nodeType": "699", "endLine": 89, "endColumn": 8, "suggestions": "738"}, {"ruleId": "697", "severity": 1, "message": "739", "line": 70, "column": 6, "nodeType": "699", "endLine": 70, "endColumn": 37, "suggestions": "740"}, {"ruleId": "662", "severity": 1, "message": "702", "line": 38, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 38, "endColumn": 8}, {"ruleId": "662", "severity": 1, "message": "741", "line": 57, "column": 25, "nodeType": "664", "messageId": "665", "endLine": 57, "endColumn": 38}, {"ruleId": "662", "severity": 1, "message": "742", "line": 57, "column": 56, "nodeType": "664", "messageId": "665", "endLine": 57, "endColumn": 66}, {"ruleId": "662", "severity": 1, "message": "696", "line": 62, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 62, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "743", "line": 85, "column": 6, "nodeType": "699", "endLine": 85, "endColumn": 65, "suggestions": "744"}, {"ruleId": "662", "severity": 1, "message": "745", "line": 103, "column": 13, "nodeType": "664", "messageId": "665", "endLine": 103, "endColumn": 22}, {"ruleId": "662", "severity": 1, "message": "702", "line": 38, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 38, "endColumn": 8}, {"ruleId": "662", "severity": 1, "message": "746", "line": 63, "column": 40, "nodeType": "664", "messageId": "665", "endLine": 63, "endColumn": 54}, {"ruleId": "662", "severity": 1, "message": "696", "line": 67, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 67, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "747", "line": 92, "column": 6, "nodeType": "699", "endLine": 92, "endColumn": 51, "suggestions": "748"}, {"ruleId": "662", "severity": 1, "message": "749", "line": 50, "column": 12, "nodeType": "664", "messageId": "665", "endLine": 50, "endColumn": 21}, {"ruleId": "662", "severity": 1, "message": "750", "line": 54, "column": 17, "nodeType": "664", "messageId": "665", "endLine": 54, "endColumn": 31}, {"ruleId": "662", "severity": 1, "message": "751", "line": 59, "column": 15, "nodeType": "664", "messageId": "665", "endLine": 59, "endColumn": 27}, {"ruleId": "662", "severity": 1, "message": "752", "line": 61, "column": 14, "nodeType": "664", "messageId": "665", "endLine": 61, "endColumn": 25}, {"ruleId": "662", "severity": 1, "message": "753", "line": 62, "column": 18, "nodeType": "664", "messageId": "665", "endLine": 62, "endColumn": 28}, {"ruleId": "662", "severity": 1, "message": "754", "line": 63, "column": 13, "nodeType": "664", "messageId": "665", "endLine": 63, "endColumn": 25}, {"ruleId": "697", "severity": 1, "message": "755", "line": 105, "column": 6, "nodeType": "699", "endLine": 105, "endColumn": 10, "suggestions": "756"}, {"ruleId": "662", "severity": 1, "message": "757", "line": 29, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 29, "endColumn": 13}, {"ruleId": "662", "severity": 1, "message": "701", "line": 34, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 34, "endColumn": 10}, {"ruleId": "662", "severity": 1, "message": "758", "line": 35, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 35, "endColumn": 8}, {"ruleId": "662", "severity": 1, "message": "759", "line": 37, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 37, "endColumn": 13}, {"ruleId": "662", "severity": 1, "message": "710", "line": 38, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 38, "endColumn": 10}, {"ruleId": "662", "severity": 1, "message": "671", "line": 39, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 39, "endColumn": 11}, {"ruleId": "662", "severity": 1, "message": "752", "line": 57, "column": 14, "nodeType": "664", "messageId": "665", "endLine": 57, "endColumn": 25}, {"ruleId": "662", "severity": 1, "message": "674", "line": 58, "column": 15, "nodeType": "664", "messageId": "665", "endLine": 58, "endColumn": 27}, {"ruleId": "662", "severity": 1, "message": "684", "line": 62, "column": 17, "nodeType": "664", "messageId": "665", "endLine": 62, "endColumn": 31}, {"ruleId": "662", "severity": 1, "message": "683", "line": 63, "column": 17, "nodeType": "664", "messageId": "665", "endLine": 63, "endColumn": 31}, {"ruleId": "662", "severity": 1, "message": "760", "line": 122, "column": 10, "nodeType": "664", "messageId": "665", "endLine": 122, "endColumn": 22}, {"ruleId": "662", "severity": 1, "message": "761", "line": 122, "column": 24, "nodeType": "664", "messageId": "665", "endLine": 122, "endColumn": 39}, {"ruleId": "697", "severity": 1, "message": "762", "line": 157, "column": 6, "nodeType": "699", "endLine": 157, "endColumn": 8, "suggestions": "763"}, {"ruleId": "697", "severity": 1, "message": "764", "line": 164, "column": 6, "nodeType": "699", "endLine": 164, "endColumn": 18, "suggestions": "765"}, {"ruleId": "697", "severity": 1, "message": "766", "line": 167, "column": 29, "nodeType": "664", "endLine": 167, "endColumn": 40}, {"ruleId": "662", "severity": 1, "message": "711", "line": 4, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 4, "endColumn": 7}, {"ruleId": "662", "severity": 1, "message": "712", "line": 5, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 5, "endColumn": 14}, {"ruleId": "662", "severity": 1, "message": "671", "line": 28, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 28, "endColumn": 11}, {"ruleId": "662", "severity": 1, "message": "675", "line": 39, "column": 17, "nodeType": "664", "messageId": "665", "endLine": 39, "endColumn": 31}, {"ruleId": "662", "severity": 1, "message": "767", "line": 51, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 51, "endColumn": 15}, {"ruleId": "662", "severity": 1, "message": "768", "line": 52, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 52, "endColumn": 15}, {"ruleId": "662", "severity": 1, "message": "769", "line": 54, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 54, "endColumn": 15}, {"ruleId": "662", "severity": 1, "message": "770", "line": 55, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 55, "endColumn": 15}, {"ruleId": "662", "severity": 1, "message": "771", "line": 56, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 56, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "772", "line": 142, "column": 6, "nodeType": "699", "endLine": 142, "endColumn": 18, "suggestions": "773"}, {"ruleId": "697", "severity": 1, "message": "774", "line": 150, "column": 6, "nodeType": "699", "endLine": 150, "endColumn": 35, "suggestions": "775"}, {"ruleId": "662", "severity": 1, "message": "776", "line": 493, "column": 11, "nodeType": "664", "messageId": "665", "endLine": 493, "endColumn": 28}, {"ruleId": "662", "severity": 1, "message": "711", "line": 23, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 23, "endColumn": 7}, {"ruleId": "662", "severity": 1, "message": "712", "line": 24, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 24, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "777", "line": 86, "column": 6, "nodeType": "699", "endLine": 86, "endColumn": 37, "suggestions": "778"}, {"ruleId": "662", "severity": 1, "message": "779", "line": 34, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 34, "endColumn": 9}, {"ruleId": "662", "severity": 1, "message": "780", "line": 43, "column": 15, "nodeType": "664", "messageId": "665", "endLine": 43, "endColumn": 27}, {"ruleId": "697", "severity": 1, "message": "781", "line": 73, "column": 6, "nodeType": "699", "endLine": 73, "endColumn": 10, "suggestions": "782"}, {"ruleId": "662", "severity": 1, "message": "711", "line": 5, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 5, "endColumn": 7}, {"ruleId": "662", "severity": 1, "message": "712", "line": 6, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 6, "endColumn": 14}, {"ruleId": "662", "severity": 1, "message": "671", "line": 31, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 31, "endColumn": 11}, {"ruleId": "662", "severity": 1, "message": "679", "line": 36, "column": 10, "nodeType": "664", "messageId": "665", "endLine": 36, "endColumn": 17}, {"ruleId": "662", "severity": 1, "message": "675", "line": 45, "column": 17, "nodeType": "664", "messageId": "665", "endLine": 45, "endColumn": 31}, {"ruleId": "662", "severity": 1, "message": "770", "line": 60, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 60, "endColumn": 15}, {"ruleId": "662", "severity": 1, "message": "771", "line": 61, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 61, "endColumn": 14}, {"ruleId": "662", "severity": 1, "message": "783", "line": 66, "column": 10, "nodeType": "664", "messageId": "665", "endLine": 66, "endColumn": 21}, {"ruleId": "697", "severity": 1, "message": "784", "line": 626, "column": 6, "nodeType": "699", "endLine": 626, "endColumn": 18, "suggestions": "785"}, {"ruleId": "662", "severity": 1, "message": "701", "line": 25, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 25, "endColumn": 10}, {"ruleId": "662", "severity": 1, "message": "786", "line": 34, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 34, "endColumn": 7}, {"ruleId": "662", "severity": 1, "message": "702", "line": 36, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 36, "endColumn": 8}, {"ruleId": "662", "severity": 1, "message": "696", "line": 63, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 63, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "787", "line": 131, "column": 6, "nodeType": "699", "endLine": 131, "endColumn": 18, "suggestions": "788"}, {"ruleId": "789", "severity": 1, "message": "790", "line": 308, "column": 5, "nodeType": "791", "messageId": "792", "endLine": 354, "endColumn": 6}, {"ruleId": "793", "severity": 1, "message": "794", "line": 326, "column": 45, "nodeType": "795", "messageId": "796", "endLine": 326, "endColumn": 46, "suggestions": "797"}, {"ruleId": "662", "severity": 1, "message": "702", "line": 38, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 38, "endColumn": 8}, {"ruleId": "662", "severity": 1, "message": "696", "line": 61, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 61, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "798", "line": 77, "column": 6, "nodeType": "699", "endLine": 77, "endColumn": 51, "suggestions": "799"}, {"ruleId": "662", "severity": 1, "message": "800", "line": 5, "column": 27, "nodeType": "664", "messageId": "665", "endLine": 5, "endColumn": 36}, {"ruleId": "662", "severity": 1, "message": "801", "line": 59, "column": 10, "nodeType": "664", "messageId": "665", "endLine": 59, "endColumn": 18}, {"ruleId": "697", "severity": 1, "message": "802", "line": 106, "column": 6, "nodeType": "699", "endLine": 106, "endColumn": 8, "suggestions": "803"}, {"ruleId": "697", "severity": 1, "message": "804", "line": 61, "column": 6, "nodeType": "699", "endLine": 61, "endColumn": 10, "suggestions": "805"}, {"ruleId": "662", "severity": 1, "message": "806", "line": 69, "column": 13, "nodeType": "664", "messageId": "665", "endLine": 69, "endColumn": 21}, {"ruleId": "662", "severity": 1, "message": "807", "line": 12, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 12, "endColumn": 9}, {"ruleId": "667", "severity": 1, "message": "668", "line": 315, "column": 1, "nodeType": "669", "endLine": 327, "endColumn": 3}, {"ruleId": "697", "severity": 1, "message": "808", "line": 129, "column": 6, "nodeType": "699", "endLine": 129, "endColumn": 49, "suggestions": "809", "suppressions": "810"}, {"ruleId": "662", "severity": 1, "message": "811", "line": 33, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 33, "endColumn": 8}, {"ruleId": "662", "severity": 1, "message": "696", "line": 61, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 61, "endColumn": 14}, {"ruleId": "697", "severity": 1, "message": "812", "line": 123, "column": 6, "nodeType": "699", "endLine": 123, "endColumn": 8, "suggestions": "813"}, {"ruleId": "662", "severity": 1, "message": "814", "line": 211, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 211, "endColumn": 19}, {"ruleId": "815", "severity": 1, "message": "816", "line": 712, "column": 41, "nodeType": "817", "messageId": "818", "endLine": 712, "endColumn": 43}, {"ruleId": "815", "severity": 1, "message": "816", "line": 712, "column": 62, "nodeType": "817", "messageId": "818", "endLine": 712, "endColumn": 64}, {"ruleId": "815", "severity": 1, "message": "819", "line": 712, "column": 62, "nodeType": "817", "messageId": "818", "endLine": 712, "endColumn": 64}, {"ruleId": "815", "severity": 1, "message": "819", "line": 712, "column": 84, "nodeType": "817", "messageId": "818", "endLine": 712, "endColumn": 86}, {"ruleId": "815", "severity": 1, "message": "816", "line": 713, "column": 46, "nodeType": "817", "messageId": "818", "endLine": 713, "endColumn": 48}, {"ruleId": "815", "severity": 1, "message": "816", "line": 713, "column": 65, "nodeType": "817", "messageId": "818", "endLine": 713, "endColumn": 67}, {"ruleId": "815", "severity": 1, "message": "819", "line": 713, "column": 65, "nodeType": "817", "messageId": "818", "endLine": 713, "endColumn": 67}, {"ruleId": "815", "severity": 1, "message": "819", "line": 713, "column": 87, "nodeType": "817", "messageId": "818", "endLine": 713, "endColumn": 89}, {"ruleId": "662", "severity": 1, "message": "711", "line": 4, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 4, "endColumn": 7}, {"ruleId": "662", "severity": 1, "message": "712", "line": 5, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 5, "endColumn": 14}, {"ruleId": "662", "severity": 1, "message": "696", "line": 49, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 49, "endColumn": 14}, {"ruleId": "662", "severity": 1, "message": "820", "line": 371, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 371, "endColumn": 29}, {"ruleId": "662", "severity": 1, "message": "821", "line": 14, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 14, "endColumn": 16}, {"ruleId": "697", "severity": 1, "message": "822", "line": 49, "column": 6, "nodeType": "699", "endLine": 49, "endColumn": 24, "suggestions": "823"}, {"ruleId": "662", "severity": 1, "message": "701", "line": 16, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 16, "endColumn": 10}, {"ruleId": "662", "severity": 1, "message": "696", "line": 38, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 38, "endColumn": 14}, {"ruleId": "662", "severity": 1, "message": "696", "line": 24, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 24, "endColumn": 14}, {"ruleId": "662", "severity": 1, "message": "691", "line": 26, "column": 27, "nodeType": "664", "messageId": "665", "endLine": 26, "endColumn": 36}, {"ruleId": "662", "severity": 1, "message": "696", "line": 38, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 38, "endColumn": 14}, {"ruleId": "667", "severity": 1, "message": "668", "line": 152, "column": 1, "nodeType": "669", "endLine": 185, "endColumn": 3}, {"ruleId": "667", "severity": 1, "message": "668", "line": 345, "column": 1, "nodeType": "669", "endLine": 352, "endColumn": 3}, {"ruleId": "662", "severity": 1, "message": "824", "line": 22, "column": 16, "nodeType": "664", "messageId": "665", "endLine": 22, "endColumn": 29}, {"ruleId": "662", "severity": 1, "message": "750", "line": 23, "column": 17, "nodeType": "664", "messageId": "665", "endLine": 23, "endColumn": 31}, {"ruleId": "662", "severity": 1, "message": "825", "line": 24, "column": 11, "nodeType": "664", "messageId": "665", "endLine": 24, "endColumn": 19}, {"ruleId": "662", "severity": 1, "message": "826", "line": 25, "column": 13, "nodeType": "664", "messageId": "665", "endLine": 25, "endColumn": 23}, {"ruleId": "662", "severity": 1, "message": "679", "line": 28, "column": 10, "nodeType": "664", "messageId": "665", "endLine": 28, "endColumn": 17}, {"ruleId": "662", "severity": 1, "message": "827", "line": 29, "column": 15, "nodeType": "664", "messageId": "665", "endLine": 29, "endColumn": 27}, {"ruleId": "662", "severity": 1, "message": "828", "line": 163, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 163, "endColumn": 17}, {"ruleId": "662", "severity": 1, "message": "701", "line": 18, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 18, "endColumn": 10}, {"ruleId": "662", "severity": 1, "message": "829", "line": 62, "column": 9, "nodeType": "664", "messageId": "665", "endLine": 62, "endColumn": 21}, {"ruleId": "662", "severity": 1, "message": "711", "line": 11, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 11, "endColumn": 7}, {"ruleId": "697", "severity": 1, "message": "830", "line": 78, "column": 6, "nodeType": "699", "endLine": 78, "endColumn": 69, "suggestions": "831"}, {"ruleId": "697", "severity": 1, "message": "832", "line": 54, "column": 6, "nodeType": "699", "endLine": 54, "endColumn": 10, "suggestions": "833"}, {"ruleId": "662", "severity": 1, "message": "711", "line": 34, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 34, "endColumn": 7}, {"ruleId": "662", "severity": 1, "message": "712", "line": 35, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 35, "endColumn": 14}, {"ruleId": "662", "severity": 1, "message": "759", "line": 37, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 37, "endColumn": 13}, {"ruleId": "662", "severity": 1, "message": "710", "line": 38, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 38, "endColumn": 10}, {"ruleId": "662", "severity": 1, "message": "679", "line": 44, "column": 10, "nodeType": "664", "messageId": "665", "endLine": 44, "endColumn": 17}, {"ruleId": "662", "severity": 1, "message": "834", "line": 45, "column": 13, "nodeType": "664", "messageId": "665", "endLine": 45, "endColumn": 23}, {"ruleId": "662", "severity": 1, "message": "835", "line": 54, "column": 3, "nodeType": "664", "messageId": "665", "endLine": 54, "endColumn": 22}, {"ruleId": "697", "severity": 1, "message": "836", "line": 79, "column": 6, "nodeType": "699", "endLine": 79, "endColumn": 8, "suggestions": "837"}, {"ruleId": "793", "severity": 1, "message": "794", "line": 31, "column": 17, "nodeType": "795", "messageId": "796", "endLine": 31, "endColumn": 18, "suggestions": "838"}, {"ruleId": "793", "severity": 1, "message": "794", "line": 32, "column": 33, "nodeType": "795", "messageId": "796", "endLine": 32, "endColumn": 34, "suggestions": "839"}, {"ruleId": "793", "severity": 1, "message": "840", "line": 33, "column": 22, "nodeType": "795", "messageId": "796", "endLine": 33, "endColumn": 23, "suggestions": "841"}, {"ruleId": "793", "severity": 1, "message": "842", "line": 33, "column": 24, "nodeType": "795", "messageId": "796", "endLine": 33, "endColumn": 25, "suggestions": "843"}, {"ruleId": "793", "severity": 1, "message": "794", "line": 35, "column": 27, "nodeType": "795", "messageId": "796", "endLine": 35, "endColumn": 28, "suggestions": "844"}, {"ruleId": "793", "severity": 1, "message": "845", "line": 38, "column": 33, "nodeType": "795", "messageId": "796", "endLine": 38, "endColumn": 34, "suggestions": "846"}, {"ruleId": "793", "severity": 1, "message": "847", "line": 38, "column": 35, "nodeType": "795", "messageId": "796", "endLine": 38, "endColumn": 36, "suggestions": "848"}, {"ruleId": "793", "severity": 1, "message": "840", "line": 38, "column": 37, "nodeType": "795", "messageId": "796", "endLine": 38, "endColumn": 38, "suggestions": "849"}, {"ruleId": "793", "severity": 1, "message": "842", "line": 38, "column": 39, "nodeType": "795", "messageId": "796", "endLine": 38, "endColumn": 40, "suggestions": "850"}, {"ruleId": "793", "severity": 1, "message": "851", "line": 38, "column": 41, "nodeType": "795", "messageId": "796", "endLine": 38, "endColumn": 42, "suggestions": "852"}, {"ruleId": "793", "severity": 1, "message": "853", "line": 38, "column": 43, "nodeType": "795", "messageId": "796", "endLine": 38, "endColumn": 44, "suggestions": "854"}, {"ruleId": "793", "severity": 1, "message": "845", "line": 39, "column": 25, "nodeType": "795", "messageId": "796", "endLine": 39, "endColumn": 26, "suggestions": "855"}, {"ruleId": "793", "severity": 1, "message": "847", "line": 39, "column": 27, "nodeType": "795", "messageId": "796", "endLine": 39, "endColumn": 28, "suggestions": "856"}, {"ruleId": "793", "severity": 1, "message": "840", "line": 39, "column": 29, "nodeType": "795", "messageId": "796", "endLine": 39, "endColumn": 30, "suggestions": "857"}, {"ruleId": "793", "severity": 1, "message": "842", "line": 39, "column": 31, "nodeType": "795", "messageId": "796", "endLine": 39, "endColumn": 32, "suggestions": "858"}, {"ruleId": "793", "severity": 1, "message": "851", "line": 39, "column": 33, "nodeType": "795", "messageId": "796", "endLine": 39, "endColumn": 34, "suggestions": "859"}, {"ruleId": "793", "severity": 1, "message": "853", "line": 39, "column": 35, "nodeType": "795", "messageId": "796", "endLine": 39, "endColumn": 36, "suggestions": "860"}, {"ruleId": "793", "severity": 1, "message": "845", "line": 385, "column": 55, "nodeType": "795", "messageId": "796", "endLine": 385, "endColumn": 56, "suggestions": "861"}, {"ruleId": "793", "severity": 1, "message": "847", "line": 385, "column": 57, "nodeType": "795", "messageId": "796", "endLine": 385, "endColumn": 58, "suggestions": "862"}, {"ruleId": "793", "severity": 1, "message": "840", "line": 385, "column": 59, "nodeType": "795", "messageId": "796", "endLine": 385, "endColumn": 60, "suggestions": "863"}, {"ruleId": "793", "severity": 1, "message": "842", "line": 385, "column": 61, "nodeType": "795", "messageId": "796", "endLine": 385, "endColumn": 62, "suggestions": "864"}, {"ruleId": "793", "severity": 1, "message": "851", "line": 385, "column": 63, "nodeType": "795", "messageId": "796", "endLine": 385, "endColumn": 64, "suggestions": "865"}, {"ruleId": "793", "severity": 1, "message": "853", "line": 385, "column": 65, "nodeType": "795", "messageId": "796", "endLine": 385, "endColumn": 66, "suggestions": "866"}, "no-unused-vars", "'ItemMastersList' is defined but never used.", "Identifier", "unusedVar", "'BatchItemsList' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'logData' is assigned a value but never used.", "'Collapse' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'CategoryIcon' is defined but never used.", "'AssignmentIcon' is defined but never used.", "'ItemTypeIcon' is defined but never used.", "'ItemCategoryIcon' is defined but never used.", "'GroupIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'ReceiptIcon' is defined but never used.", "'ExitToAppIcon' is defined but never used.", "'InputIcon' is defined but never used.", "'ExpandLessIcon' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ApprovalIcon' is defined but never used.", "'LocalOfferIcon' is defined but never used.", "'MaintenanceIcon' is defined but never used.", "'colors' is defined but never used.", "'shadows' is defined but never used.", "'gradients' is defined but never used.", "'ModernSidebar' is defined but never used.", "'SPACING' is defined but never used.", "'TRANSITIONS' is defined but never used.", "'currentUser' is assigned a value but never used.", "'theme' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchOrgTypes'. Either include it or remove the dependency array.", "ArrayExpression", ["867"], "'Divider' is defined but never used.", "'alpha' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchOrganizations'. Either include it or remove the dependency array.", ["868"], "React Hook useEffect has a missing dependency: 'fetchOffices'. Either include it or remove the dependency array.", ["869"], "'getOrganizations' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchOrganizationDetails'. Either include it or remove the dependency array.", ["870"], "'Tooltip' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'ModernCardGrid' is defined but never used.", "React Hook useEffect has a missing dependency: 'dashboardCards'. Either include it or remove the dependency array.", ["871"], "'cardItem' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSupplierTypes'. Either include it or remove the dependency array.", ["872"], "React Hook useEffect has a missing dependency: 'baseCards'. Either include it or remove the dependency array.", ["873"], "React Hook useEffect has a missing dependency: 'fetchItemTypes'. Either include it or remove the dependency array.", ["874"], "React Hook useEffect has a missing dependency: 'fetchEntryModes'. Either include it or remove the dependency array.", ["875"], "React Hook useEffect has a missing dependency: 'fetchClassifications'. Either include it or remove the dependency array.", ["876"], "React Hook useEffect has a missing dependency: 'fetchItemBrands'. Either include it or remove the dependency array.", ["877"], "React Hook useEffect has a missing dependency: 'fetchItemCategories'. Either include it or remove the dependency array.", ["878"], "React Hook useEffect has a missing dependency: 'fetchUnitsOfMeasure'. Either include it or remove the dependency array.", ["879"], "React Hook useEffect has a missing dependency: 'fetchItemQualities'. Either include it or remove the dependency array.", ["880"], "React Hook useEffect has a missing dependency: 'fetchItemSizes'. Either include it or remove the dependency array.", ["881"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["882"], "React Hook useEffect has a missing dependency: 'loadItemMasters'. Either include it or remove the dependency array.", ["883"], "'getStatusChip' is defined but never used.", "'formatDate' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchItemMasters'. Either include it or remove the dependency array.", ["884"], "'errorInfo' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchBatchItems'. Either include it or remove the dependency array.", ["885"], "'ShareIcon' is defined but never used.", "'TrendingUpIcon' is defined but never used.", "'SecurityIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'ActiveIcon' is defined but never used.", "'InactiveIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadItemMaster' and 'loadRelatedData'. Either include them or remove the dependency array.", ["886"], "'CardHeader' is defined but never used.", "'Alert' is defined but never used.", "'IconButton' is defined but never used.", "'showAdvanced' is assigned a value but never used.", "'setShowAdvanced' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadDropdownOptions'. Either include it or remove the dependency array.", ["887"], "React Hook useEffect has a missing dependency: 'loadItemMaster'. Either include it or remove the dependency array.", ["888"], "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "'FIELD_LABELS' is defined but never used.", "'PLACEHOLDERS' is defined but never used.", "'UI_CONSTANTS' is defined but never used.", "'COLOR_THEMES' is defined but never used.", "'BUTTON_TEXT' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadDropdowns'. Either include it or remove the dependency array.", ["889"], "React Hook useEffect has a missing dependency: 'loadBatchItem'. Either include it or remove the dependency array.", ["890"], "'currentStepFields' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadBatches'. Either include it or remove the dependency array.", ["891"], "'Rating' is defined but never used.", "'TimelineIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadBatchItem' and 'loadInventoryItems'. Either include them or remove the dependency array.", ["892"], "'getStepIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadDropdownOptions' and 'loadItemMaster'. Either include them or remove the dependency array.", ["893"], "'Chip' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadDropdownOptions' and 'loadInventoryItem'. Either include them or remove the dependency array.", ["894"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "no-useless-escape", "Unnecessary escape character: \\-.", "Literal", "unnecessaryEscape", ["895", "896"], "React Hook useEffect has a missing dependency: 'fetchInventoryItems'. Either include it or remove the dependency array.", ["897"], "'useEffect' is defined but never used.", "'testData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'testAllEndpoints'. Either include it or remove the dependency array.", ["898"], "React Hook useEffect has a missing dependency: 'fetchInventoryItem'. Either include it or remove the dependency array.", ["899"], "'response' is assigned a value but never used.", "'Avatar' is defined but never used.", "React Hook useEffect has a missing dependency: 'formik'. Either include it or remove the dependency array.", ["900"], ["901"], "'Paper' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchItems'. Either include it or remove the dependency array.", ["902"], "'StatusCard' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "Unexpected mix of '||' and '&&'. Use parentheses to clarify the intended order of operations.", "'getSupplierTypeColor' is assigned a value but never used.", "'DialogActions' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSupplier'. Either include it or remove the dependency array.", ["903"], "'DashboardIcon' is defined but never used.", "'StarIcon' is defined but never used.", "'PublicIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'navigate' is assigned a value but never used.", "'amharicStyle' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchForms'. Either include it or remove the dependency array.", ["904"], "React Hook useEffect has a missing dependency: 'fetchForm'. Either include it or remove the dependency array.", ["905"], "'RemoveIcon' is defined but never used.", "'formatEthiopianDate' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchAvailableItems' and 'fetchStoresAndShelves'. Either include them or remove the dependency array.", ["906"], ["907", "908"], ["909", "910"], "Unnecessary escape character: \\(.", ["911", "912"], "Unnecessary escape character: \\).", ["913", "914"], ["915", "916"], "Unnecessary escape character: \\..", ["917", "918"], "Unnecessary escape character: \\,.", ["919", "920"], ["921", "922"], ["923", "924"], "Unnecessary escape character: \\/.", ["925", "926"], "Unnecessary escape character: \\&.", ["927", "928"], ["929", "930"], ["931", "932"], ["933", "934"], ["935", "936"], ["937", "938"], ["939", "940"], ["941", "942"], ["943", "944"], ["945", "946"], ["947", "948"], ["949", "950"], ["951", "952"], {"desc": "953", "fix": "954"}, {"desc": "955", "fix": "956"}, {"desc": "957", "fix": "958"}, {"desc": "959", "fix": "960"}, {"desc": "961", "fix": "962"}, {"desc": "963", "fix": "964"}, {"desc": "965", "fix": "966"}, {"desc": "967", "fix": "968"}, {"desc": "969", "fix": "970"}, {"desc": "971", "fix": "972"}, {"desc": "973", "fix": "974"}, {"desc": "975", "fix": "976"}, {"desc": "977", "fix": "978"}, {"desc": "979", "fix": "980"}, {"desc": "981", "fix": "982"}, {"desc": "983", "fix": "984"}, {"desc": "985", "fix": "986"}, {"desc": "987", "fix": "988"}, {"desc": "989", "fix": "990"}, {"desc": "991", "fix": "992"}, {"desc": "993", "fix": "994"}, {"desc": "995", "fix": "996"}, {"desc": "997", "fix": "998"}, {"desc": "999", "fix": "1000"}, {"desc": "1001", "fix": "1002"}, {"desc": "1003", "fix": "1004"}, {"desc": "1005", "fix": "1006"}, {"desc": "1007", "fix": "1008"}, {"messageId": "1009", "fix": "1010", "desc": "1011"}, {"messageId": "1012", "fix": "1013", "desc": "1014"}, {"desc": "1015", "fix": "1016"}, {"desc": "1017", "fix": "1018"}, {"desc": "1019", "fix": "1020"}, {"desc": "1021", "fix": "1022"}, {"kind": "1023", "justification": "1024"}, {"desc": "1025", "fix": "1026"}, {"desc": "1027", "fix": "1028"}, {"desc": "1029", "fix": "1030"}, {"desc": "1031", "fix": "1032"}, {"desc": "1033", "fix": "1034"}, {"messageId": "1009", "fix": "1035", "desc": "1011"}, {"messageId": "1012", "fix": "1036", "desc": "1014"}, {"messageId": "1009", "fix": "1037", "desc": "1011"}, {"messageId": "1012", "fix": "1038", "desc": "1014"}, {"messageId": "1009", "fix": "1039", "desc": "1011"}, {"messageId": "1012", "fix": "1040", "desc": "1014"}, {"messageId": "1009", "fix": "1041", "desc": "1011"}, {"messageId": "1012", "fix": "1042", "desc": "1014"}, {"messageId": "1009", "fix": "1043", "desc": "1011"}, {"messageId": "1012", "fix": "1044", "desc": "1014"}, {"messageId": "1009", "fix": "1045", "desc": "1011"}, {"messageId": "1012", "fix": "1046", "desc": "1014"}, {"messageId": "1009", "fix": "1047", "desc": "1011"}, {"messageId": "1012", "fix": "1048", "desc": "1014"}, {"messageId": "1009", "fix": "1049", "desc": "1011"}, {"messageId": "1012", "fix": "1050", "desc": "1014"}, {"messageId": "1009", "fix": "1051", "desc": "1011"}, {"messageId": "1012", "fix": "1052", "desc": "1014"}, {"messageId": "1009", "fix": "1053", "desc": "1011"}, {"messageId": "1012", "fix": "1054", "desc": "1014"}, {"messageId": "1009", "fix": "1055", "desc": "1011"}, {"messageId": "1012", "fix": "1056", "desc": "1014"}, {"messageId": "1009", "fix": "1057", "desc": "1011"}, {"messageId": "1012", "fix": "1058", "desc": "1014"}, {"messageId": "1009", "fix": "1059", "desc": "1011"}, {"messageId": "1012", "fix": "1060", "desc": "1014"}, {"messageId": "1009", "fix": "1061", "desc": "1011"}, {"messageId": "1012", "fix": "1062", "desc": "1014"}, {"messageId": "1009", "fix": "1063", "desc": "1011"}, {"messageId": "1012", "fix": "1064", "desc": "1014"}, {"messageId": "1009", "fix": "1065", "desc": "1011"}, {"messageId": "1012", "fix": "1066", "desc": "1014"}, {"messageId": "1009", "fix": "1067", "desc": "1011"}, {"messageId": "1012", "fix": "1068", "desc": "1014"}, {"messageId": "1009", "fix": "1069", "desc": "1011"}, {"messageId": "1012", "fix": "1070", "desc": "1014"}, {"messageId": "1009", "fix": "1071", "desc": "1011"}, {"messageId": "1012", "fix": "1072", "desc": "1014"}, {"messageId": "1009", "fix": "1073", "desc": "1011"}, {"messageId": "1012", "fix": "1074", "desc": "1014"}, {"messageId": "1009", "fix": "1075", "desc": "1011"}, {"messageId": "1012", "fix": "1076", "desc": "1014"}, {"messageId": "1009", "fix": "1077", "desc": "1011"}, {"messageId": "1012", "fix": "1078", "desc": "1014"}, {"messageId": "1009", "fix": "1079", "desc": "1011"}, {"messageId": "1012", "fix": "1080", "desc": "1014"}, "Update the dependencies array to be: [fetchOrgTypes]", {"range": "1081", "text": "1082"}, "Update the dependencies array to be: [fetchOrganizations]", {"range": "1083", "text": "1084"}, "Update the dependencies array to be: [fetchOffices]", {"range": "1085", "text": "1086"}, "Update the dependencies array to be: [fetchOrganizationDetails, id]", {"range": "1087", "text": "1088"}, "Update the dependencies array to be: [dashboardCards]", {"range": "1089", "text": "1090"}, "Update the dependencies array to be: [fetchSupplierTypes, page, rowsPerPage, searchTerm]", {"range": "1091", "text": "1092"}, "Update the dependencies array to be: [baseCards]", {"range": "1093", "text": "1094"}, "Update the dependencies array to be: [fetchItemTypes]", {"range": "1095", "text": "1096"}, "Update the dependencies array to be: [fetchEntryModes]", {"range": "1097", "text": "1098"}, "Update the dependencies array to be: [fetchClassifications]", {"range": "1099", "text": "1100"}, "Update the dependencies array to be: [fetchItemBrands]", {"range": "1101", "text": "1102"}, "Update the dependencies array to be: [fetchItemCategories]", {"range": "1103", "text": "1104"}, "Update the dependencies array to be: [fetchUnitsOfMeasure]", {"range": "1105", "text": "1106"}, "Update the dependencies array to be: [fetchItemQualities]", {"range": "1107", "text": "1108"}, "Update the dependencies array to be: [fetchItemSizes]", {"range": "1109", "text": "1110"}, "Update the dependencies array to be: [fetchData]", {"range": "1111", "text": "1112"}, "Update the dependencies array to be: [loadItemMasters, page, rowsPerPage, searchTerm]", {"range": "1113", "text": "1114"}, "Update the dependencies array to be: [page, rowsPerPage, searchTerm, typeFilter, categoryFilter, fetchItemMasters]", {"range": "1115", "text": "1116"}, "Update the dependencies array to be: [fetchBatchItems, page, rowsPerPage, searchTerm, statusFilter]", {"range": "1117", "text": "1118"}, "Update the dependencies array to be: [id, loadItemMaster, loadRelatedData]", {"range": "1119", "text": "1120"}, "Update the dependencies array to be: [loadDropdownOptions]", {"range": "1121", "text": "1122"}, "Update the dependencies array to be: [id, isEdit, loadItemMaster]", {"range": "1123", "text": "1124"}, "Update the dependencies array to be: [id, isEdit, loadDropdowns]", {"range": "1125", "text": "1126"}, "Update the dependencies array to be: [dropdownsLoaded, isEdit, id, loadBatchItem]", {"range": "1127", "text": "1128"}, "Update the dependencies array to be: [loadBatches, page, rowsPerPage, searchTerm]", {"range": "1129", "text": "1130"}, "Update the dependencies array to be: [id, loadBatchItem, loadInventoryItems]", {"range": "1131", "text": "1132"}, "Update the dependencies array to be: [id, isEdit, loadDropdownOptions, loadItemMaster]", {"range": "1133", "text": "1134"}, "Update the dependencies array to be: [id, isEdit, loadDropdownOptions, loadInventoryItem]", {"range": "1135", "text": "1136"}, "removeEscape", {"range": "1137", "text": "1024"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1138", "text": "1139"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [fetchInventoryItems, page, rowsPerPage, searchTerm, statusFilter]", {"range": "1140", "text": "1141"}, "Update the dependencies array to be: [testAllEndpoints]", {"range": "1142", "text": "1143"}, "Update the dependencies array to be: [fetchInventoryItem, id]", {"range": "1144", "text": "1145"}, "Update the dependencies array to be: [formik, formik.values.organization, selectedOrgId]", {"range": "1146", "text": "1147"}, "directive", "", "Update the dependencies array to be: [fetchItems]", {"range": "1148", "text": "1149"}, "Update the dependencies array to be: [fetchSupplier, open, supplierId]", {"range": "1150", "text": "1151"}, "Update the dependencies array to be: [page, rowsPerPage, searchTerm, statusFilter, departmentFilter, fetchForms]", {"range": "1152", "text": "1153"}, "Update the dependencies array to be: [fetchForm, id]", {"range": "1154", "text": "1155"}, "Update the dependencies array to be: [fetchAvailableItems, fetchStoresAndShelves]", {"range": "1156", "text": "1157"}, {"range": "1158", "text": "1024"}, {"range": "1159", "text": "1139"}, {"range": "1160", "text": "1024"}, {"range": "1161", "text": "1139"}, {"range": "1162", "text": "1024"}, {"range": "1163", "text": "1139"}, {"range": "1164", "text": "1024"}, {"range": "1165", "text": "1139"}, {"range": "1166", "text": "1024"}, {"range": "1167", "text": "1139"}, {"range": "1168", "text": "1024"}, {"range": "1169", "text": "1139"}, {"range": "1170", "text": "1024"}, {"range": "1171", "text": "1139"}, {"range": "1172", "text": "1024"}, {"range": "1173", "text": "1139"}, {"range": "1174", "text": "1024"}, {"range": "1175", "text": "1139"}, {"range": "1176", "text": "1024"}, {"range": "1177", "text": "1139"}, {"range": "1178", "text": "1024"}, {"range": "1179", "text": "1139"}, {"range": "1180", "text": "1024"}, {"range": "1181", "text": "1139"}, {"range": "1182", "text": "1024"}, {"range": "1183", "text": "1139"}, {"range": "1184", "text": "1024"}, {"range": "1185", "text": "1139"}, {"range": "1186", "text": "1024"}, {"range": "1187", "text": "1139"}, {"range": "1188", "text": "1024"}, {"range": "1189", "text": "1139"}, {"range": "1190", "text": "1024"}, {"range": "1191", "text": "1139"}, {"range": "1192", "text": "1024"}, {"range": "1193", "text": "1139"}, {"range": "1194", "text": "1024"}, {"range": "1195", "text": "1139"}, {"range": "1196", "text": "1024"}, {"range": "1197", "text": "1139"}, {"range": "1198", "text": "1024"}, {"range": "1199", "text": "1139"}, {"range": "1200", "text": "1024"}, {"range": "1201", "text": "1139"}, {"range": "1202", "text": "1024"}, {"range": "1203", "text": "1139"}, [1902, 1904], "[fetchOrgTypes]", [1929, 1931], "[fetchOrganizations]", [1811, 1813], "[fetchOffices]", [1504, 1508], "[fetchOrganizationDetails, id]", [7436, 7438], "[dashboardCards]", [1347, 1378], "[fetchSupplierTypes, page, rowsPerPage, searchTerm]", [7227, 7229], "[baseCards]", [1402, 1404], "[fetchItemTypes]", [1575, 1577], "[fetchEntryModes]", [1900, 1902], "[fetchClassifications]", [1447, 1449], "[fetchItemBrands]", [1439, 1441], "[fetchItemCategories]", [1448, 1450], "[fetchUnitsOfMeasure]", [1493, 1495], "[fetchItemQualities]", [1410, 1412], "[fetchItemSizes]", [2033, 2035], "[fetchData]", [1688, 1719], "[loadItemMasters, page, rowsPerPage, searchTerm]", [2079, 2138], "[page, rowsPerPage, searchTerm, typeFilter, categoryFilter, fetchItemMasters]", [2237, 2282], "[fetchBatchItems, page, rowsPerPage, searchTerm, statusFilter]", [2593, 2597], "[id, loadItemMaster, loadRelatedData]", [3963, 3965], "[loadDropdownOptions]", [4075, 4087], "[id, isEdit, loadItemMaster]", [3367, 3379], "[id, isEdit, loadDropdowns]", [3587, 3616], "[dropdownsLoaded, isEdit, id, loadBatchItem]", [2003, 2034], "[loadBatches, page, rowsPerPage, searchTerm]", [1638, 1642], "[id, loadBatchItem, loadInventoryItems]", [18848, 18860], "[id, isEdit, loadDropdownOptions, loadItemMaster]", [2957, 2969], "[id, isEdit, loadDropdownOptions, loadInventoryItem]", [9121, 9122], [9121, 9121], "\\", [1883, 1928], "[fetchInventoryItems, page, rowsPerPage, searchTerm, statusFilter]", [2781, 2783], "[testAllEndpoints]", [1336, 1340], "[fetchInventoryItem, id]", [4428, 4471], "[formik, formik.values.organization, selectedOrgId]", [3014, 3016], "[fetchItems]", [1010, 1028], "[fetchSupplier, open, supplierId]", [1750, 1813], "[page, rowsPerPage, searchTerm, statusFilter, departmentFilter, fetchForms]", [1165, 1169], "[fetchForm, id]", [1757, 1759], "[fetchAvailableItems, fetchStoresAndShelves]", [651, 652], [651, 651], [696, 697], [696, 696], [730, 731], [730, 730], [732, 733], [732, 732], [800, 801], [800, 800], [937, 938], [937, 937], [939, 940], [939, 939], [941, 942], [941, 941], [943, 944], [943, 943], [945, 946], [945, 945], [947, 948], [947, 947], [979, 980], [979, 979], [981, 982], [981, 981], [983, 984], [983, 983], [985, 986], [985, 985], [987, 988], [987, 987], [989, 990], [989, 989], [10454, 10455], [10454, 10454], [10456, 10457], [10456, 10456], [10458, 10459], [10458, 10458], [10460, 10461], [10460, 10460], [10462, 10463], [10462, 10462], [10464, 10465], [10464, 10464]]