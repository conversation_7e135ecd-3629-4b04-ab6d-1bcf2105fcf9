import { useState } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  Paper,
  Chip,
  useTheme,
  alpha,
  Breadcrum<PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>
} from '@mui/material';
import {
  Help as HelpIcon,
  MenuBook as DocumentationIcon,
  VideoLibrary as TutorialsIcon,
  Support as SupportIcon,
  QuestionAnswer as FAQIcon,
  Home as HomeIcon,
  ContactSupport as ContactIcon,
  BugReport as BugReportIcon,
  Feedback as FeedbackIcon,
  School as TrainingIcon
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';

const HelpMenu = () => {
  const theme = useTheme();

  // Help management cards data
  const helpCards = [
    {
      id: 'documentation',
      title: 'Documentation',
      description: 'Comprehensive user guides and system documentation',
      icon: <DocumentationIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.primary.main,
      path: '/help/documentation',
      count: '50+',
      adminOnly: false
    },
    {
      id: 'video-tutorials',
      title: 'Video Tutorials',
      description: 'Step-by-step video guides for all features',
      icon: <TutorialsIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.secondary.main,
      path: '/help/tutorials',
      count: '25+',
      adminOnly: false
    },
    {
      id: 'faq',
      title: 'FAQ',
      description: 'Frequently asked questions and answers',
      icon: <FAQIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.info.main,
      path: '/help/faq',
      count: '100+',
      adminOnly: false
    },
    {
      id: 'contact-support',
      title: 'Contact Support',
      description: 'Get help from our technical support team',
      icon: <ContactIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.success.main,
      path: '/help/contact',
      count: '24/7',
      adminOnly: false
    },
    {
      id: 'training',
      title: 'Training Resources',
      description: 'Training materials and certification programs',
      icon: <TrainingIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.warning.main,
      path: '/help/training',
      count: 'New',
      adminOnly: false
    },
    {
      id: 'bug-report',
      title: 'Report Issues',
      description: 'Report bugs and submit feature requests',
      icon: <BugReportIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.error.main,
      path: '/help/report',
      count: 'Open',
      adminOnly: false
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <HelpIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Help & Support
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          Help & Support
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Access documentation, tutorials, and support resources
        </Typography>
      </Box>

      {/* Info Paper */}
      <Paper
        sx={{
          p: 2,
          mb: 4,
          display: 'flex',
          alignItems: 'center',
          bgcolor: alpha(theme.palette.primary.main, 0.1),
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
          borderRadius: 2
        }}
      >
        <SupportIcon color="primary" sx={{ mr: 2 }} />
        <Box>
          <Typography variant="h6" color="primary.main">
            Help & Support Center
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Find answers, learn new features, and get the support you need to maximize your productivity
          </Typography>
        </Box>
      </Paper>

      {/* Quick Help Section */}
      <Alert severity="info" sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          🆘 Need Immediate Help?
        </Typography>
        <Typography variant="body2" paragraph>
          For urgent issues or technical support, contact our support team directly:
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button variant="contained" size="small" disabled>
            📧 <EMAIL>
          </Button>
          <Button variant="outlined" size="small" disabled>
            📞 +1 (555) 123-4567
          </Button>
          <Button variant="outlined" size="small" disabled>
            💬 Live Chat
          </Button>
        </Box>
      </Alert>

      {/* Help Management Cards */}
      <Grid container spacing={3}>
        {helpCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0,0,0,0.1)',
                },
                borderTop: `4px solid ${card.color}`,
                borderRadius: 2,
                position: 'relative',
                overflow: 'visible',
                cursor: 'not-allowed',
                opacity: 0.7
              }}
            >
              {card.count && (
                <Chip
                  label={card.count}
                  color="primary"
                  size="small"
                  sx={{
                    position: 'absolute',
                    top: -10,
                    right: 16,
                    fontWeight: 'bold',
                    boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
                  }}
                />
              )}
              <CardContent sx={{ flexGrow: 1, p: 3 }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: alpha(card.color, 0.1),
                    mb: 2,
                    mx: 'auto'
                  }}
                >
                  {card.icon}
                </Box>
                <Typography variant="h6" component="h2" gutterBottom textAlign="center" fontWeight="bold">
                  {card.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" textAlign="center">
                  {card.description}
                </Typography>
                <Typography variant="caption" color="warning.main" textAlign="center" display="block" sx={{ mt: 1 }}>
                  Coming Soon
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* System Information */}
      <Paper sx={{ p: 3, mt: 4, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          System Information
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="subtitle2" gutterBottom>
              Version
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Asset Management v1.0.0
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="subtitle2" gutterBottom>
              Last Updated
            </Typography>
            <Typography variant="body2" color="text.secondary">
              December 2024
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="subtitle2" gutterBottom>
              Environment
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Development
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="subtitle2" gutterBottom>
              Support Status
            </Typography>
            <Typography variant="body2" color="success.main">
              Active
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* Quick Links */}
      <Paper sx={{ p: 3, mt: 2, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          Quick Links
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={4}>
            <Button variant="outlined" fullWidth disabled startIcon={<DocumentationIcon />}>
              User Manual
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Button variant="outlined" fullWidth disabled startIcon={<TutorialsIcon />}>
              Getting Started
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Button variant="outlined" fullWidth disabled startIcon={<FeedbackIcon />}>
              Send Feedback
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default HelpMenu;
