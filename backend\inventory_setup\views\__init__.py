"""
Inventory Setup Views
"""
from .main_classification import MainClassificationViewSet
from .sub_classification import SubClassificationViewSet
from .entry_mode import EntryModeViewSet
from .item_type import ItemTypeViewSet
from .item_category import ItemCategoryViewSet
from .item_brand import ItemBrandViewSet
from .item_manufacturer import ItemManufacturerViewSet
from .item_quality import ItemQualityViewSet
from .item_shape import ItemShapeViewSet
from .item_size import ItemSizeViewSet
from .unit_of_measure import UnitOfMeasureViewSet
from .item_status import ItemStatusViewSet
from .property_status import PropertyStatusViewSet
from .approval_status import ApprovalStatusViewSet
from .item_tag import ItemTagViewSet

__all__ = [
    'MainClassificationViewSet',
    'SubClassificationViewSet',
    'EntryModeViewSet',
    'ItemTypeViewSet',
    'ItemCategoryViewSet',
    'ItemBrandViewSet',
    'ItemManufacturerViewSet',
    'ItemQualityViewSet',
    'ItemShapeViewSet',
    'ItemSizeViewSet',
    'UnitOfMeasureViewSet',
    'ItemStatusViewSet',
    'PropertyStatusViewSet',
    'ApprovalStatusViewSet',
    'ItemTagViewSet',
]
