"""
Sub Classification Model for Inventory Setup
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
from .base import TimeStampedModel
from .main_classification import MainClassification


class SubClassification(TimeStampedModel):
    """
    Sub Classification model for categorizing inventory items under main classifications.
    Uses a 3-digit auto-generated sequential code system.
    """
    
    main_class = models.ForeignKey(
        MainClassification,
        on_delete=models.CASCADE,
        verbose_name=_('Main Classification'),
        help_text=_('Parent main classification'),
        related_name='sub_classifications'
    )
    
    code = models.CharField(
        max_length=3,
        blank=True,
        verbose_name=_('Sub Classification Code'),
        help_text=_('3-digit sequential code (auto-generated if not provided)'),
        validators=[
            RegexValidator(
                regex='^[0-9]{3}$',
                message=_('Code must be exactly 3 digits')
            )
        ]
    )
    
    name = models.Char<PERSON>ield(
        max_length=100,
        verbose_name=_('Sub Classification Name'),
        help_text=_('Descriptive name for the sub classification')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description'),
        help_text=_('Detailed description of the sub classification')
    )

    color = models.CharField(
        max_length=7,
        default='#9C27B0',
        verbose_name=_('Color'),
        help_text=_('Color code for visual identification (hex format)'),
        validators=[
            RegexValidator(
                regex='^#[0-9A-Fa-f]{6}$',
                message=_('Color must be a valid hex color code (e.g., #FF5722)')
            )
        ]
    )

    class Meta:
        db_table = 'inventory_sub_classification'
        verbose_name = _('Sub Classification')
        verbose_name_plural = _('Sub Classifications')
        unique_together = ('main_class', 'code')
        ordering = ['main_class__code', 'code']
        indexes = [
            models.Index(fields=['main_class', 'code']),
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.main_class.code}-{self.code} - {self.name}"

    def save(self, *args, **kwargs):
        """Override save to auto-generate sequential code if not provided"""
        if not self.code:
            # Get the highest code for this main classification
            highest_code = SubClassification.objects.filter(
                main_class=self.main_class
            ).exclude(pk=self.pk).order_by('-code').first()

            if highest_code and highest_code.code.isdigit():
                # Increment the highest code
                next_code = int(highest_code.code) + 1
                self.code = f"{next_code:03d}"
            else:
                # Start with 001 if no existing codes
                self.code = "001"

        super().save(*args, **kwargs)

    @property
    def full_code(self):
        """Get the full classification code (main-sub)"""
        return f"{self.main_class.code}-{self.code}"
