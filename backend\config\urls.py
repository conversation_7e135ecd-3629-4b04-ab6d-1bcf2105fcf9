"""
URL configuration for config project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.http import JsonResponse
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from organization.views.swagger import openapi_schema_view

@api_view(['GET'])
@permission_classes([AllowAny])
def direct_openapi_view(request):
    """Direct handler for the problematic URL"""
    schema = {
        "openapi": "3.0.0",
        "info": {
            "title": "Asset Management API",
            "version": "1.0.0",
            "description": "API for Asset Management System"
        },
        "paths": {}
    }
    return JsonResponse(schema)

schema_view = get_schema_view(
    openapi.Info(
        title="Asset Management API",
        default_version='v1',
        description="""
        # Asset Management System API

        A comprehensive API for managing organizational assets, inventory, and procurement processes.

        ## Features
        - **Organization Management**: Manage organizations, types, and offices
        - **User Authentication**: JWT-based authentication system
        - **Public Endpoints**: Access to public organization information
        - **Admin Interface**: Full administrative capabilities

        ## Authentication
        Most endpoints require authentication using JWT tokens. Public endpoints are marked accordingly.

        ## Getting Started
        1. Obtain an authentication token via `/api/v1/auth/token/`
        2. Include the token in the Authorization header: `Bearer <token>`
        3. Access protected endpoints with your token

        ## Public Endpoints
        Some endpoints like `/organizations/public_info/` are publicly accessible for login page integration.
        """,
        terms_of_service="https://www.example.com/terms/",
        contact=openapi.Contact(
            name="Asset Management Team",
            email="<EMAIL>",
            url="https://www.assetmanagement.com"
        ),
        license=openapi.License(name="MIT License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
    url=settings.SWAGGER_SETTINGS.get('DEFAULT_API_URL', 'http://localhost:8000'),
)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api-auth/', include('rest_framework.urls')),

    # API Documentation - Swagger/OpenAPI
    path('', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui-alt'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    path('swagger.json/', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    path('swagger.yaml/', schema_view.without_ui(cache_timeout=0), name='schema-yaml'),

    # API endpoints
    path('api/v1/auth/', include('authentication.urls')),
    path('api/v1/', include('organization.urls')),
    path('api/v1/', include('supplier.urls')),
    path('api/v1/inventory-setup/', include('inventory_setup.urls')),
    path('api/v1/', include('items.urls')),
    path('api/v1/model19/', include('model19.urls')),
]

if settings.DEBUG:
    urlpatterns += [
        path('__debug__/', include('debug_toolbar.urls')),
    ]

    # Serve media files during development
    from django.conf.urls.static import static
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
