"""
Item Tag Views
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend

from ..models import ItemTag
from ..serializers import (
    ItemTagSerializer,
    ItemTagListSerializer,
    ItemTagDropdownSerializer,
    ItemTagCreateSerializer
)


class ItemTagViewSet(viewsets.ModelViewSet):
    """ViewSet for managing Item Tags"""
    queryset = ItemTag.objects.all()
    serializer_class = ItemTagSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'tag_type', 'is_system_tag']
    search_fields = ['name', 'slug', 'description']
    ordering_fields = ['name', 'created_at', 'updated_at']
    ordering = ['name']

    def get_serializer_class(self):
        if self.action == 'list':
            return ItemTagListSerializer
        elif self.action == 'dropdown':
            return ItemTagDropdownSerializer
        elif self.action == 'create':
            return ItemTagCreateSerializer
        return ItemTagSerializer

    def get_queryset(self):
        queryset = ItemTag.objects.all()
        if self.action == 'dropdown':
            queryset = queryset.filter(is_active=True)
        return queryset

    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """Get tags grouped by type"""
        tag_type = request.query_params.get('type')
        if tag_type:
            queryset = self.get_queryset().filter(tag_type=tag_type)
        else:
            queryset = self.get_queryset()
        
        serializer = ItemTagListSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def create_predefined(self, request):
        """Create predefined item tags"""
        ItemTag.create_predefined_tags()
        tags = ItemTag.objects.all()
        serializer = ItemTagListSerializer(tags, many=True)
        return Response({
            'message': 'Predefined item tags created successfully',
            'tags': serializer.data
        })

    @action(detail=False, methods=['post'])
    def bulk_update_status(self, request):
        ids = request.data.get('ids', [])
        is_active = request.data.get('is_active')
        
        if not ids:
            return Response({'error': 'No IDs provided'}, status=status.HTTP_400_BAD_REQUEST)
        if is_active is None:
            return Response({'error': 'is_active field is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Don't allow deactivating system tags
        if not is_active:
            system_tags = ItemTag.objects.filter(id__in=ids, is_system_tag=True)
            if system_tags.exists():
                return Response({
                    'error': 'Cannot deactivate system tags'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        updated_count = ItemTag.objects.filter(id__in=ids).update(is_active=is_active)
        return Response({
            'message': f'Updated {updated_count} item tags',
            'updated_count': updated_count
        })

    def retrieve(self, request, *args, **kwargs):
        """Get detailed item tag information"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        """Update item tag with validation"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()

        # Prevent editing system tags
        if instance.is_system_tag and not partial:
            return Response({
                'error': 'System tags cannot be fully updated'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        """Partial update item tag"""
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()

        # Check if this is a system tag
        if instance.is_system_tag:
            return Response({
                'error': 'System tags cannot be deleted'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check usage count (when item model is implemented)
        if instance.usage_count > 0:
            return Response({
                'error': f'Cannot delete tag that is used by {instance.usage_count} items'
            }, status=status.HTTP_400_BAD_REQUEST)

        instance.soft_delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
