"""
Sub Classification Serializers
"""
from rest_framework import serializers
from ..models import SubClassification, MainClassification


class SubClassificationSerializer(serializers.ModelSerializer):
    """Serializer for SubClassification model"""
    
    main_class_name = serializers.CharField(source='main_class.name', read_only=True)
    main_class_code = serializers.CharField(source='main_class.code', read_only=True)
    full_code = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = SubClassification
        fields = [
            'id',
            'main_class',
            'main_class_name',
            'main_class_code',
            'code',
            'name',
            'description',
            'color',
            'full_code',
            'display_name',
            'is_active',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'code']

    def validate_name(self, value):
        """Validate name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
        return value

    def validate_main_class(self, value):
        """Validate main classification is active"""
        if value and not value.is_active:
            raise serializers.ValidationError("Cannot assign to inactive main classification")
        return value


class SubClassificationListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing sub classifications"""

    main_class_name = serializers.CharField(source='main_class.name', read_only=True)
    main_class_code = serializers.CharField(source='main_class.code', read_only=True)
    full_code = serializers.ReadOnlyField()

    class Meta:
        model = SubClassification
        fields = [
            'id',
            'main_class',
            'main_class_name',
            'main_class_code',
            'code',
            'name',
            'description',
            'color',
            'full_code',
            'is_active'
        ]


class SubClassificationDropdownSerializer(serializers.ModelSerializer):
    """Minimal serializer for dropdown/select options"""
    
    label = serializers.SerializerMethodField()
    value = serializers.CharField(source='id')
    main_class_id = serializers.CharField(source='main_class.id')
    
    class Meta:
        model = SubClassification
        fields = ['value', 'label', 'main_class_id', 'full_code']
    
    def get_label(self, obj):
        return f"{obj.full_code} - {obj.name}"


class SubClassificationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating sub classifications with code auto-generation"""
    
    class Meta:
        model = SubClassification
        fields = [
            'main_class',
            'name',
            'description',
            'color',
            'is_active'
        ]

    def validate_main_class(self, value):
        """Validate main classification is active"""
        if value and not value.is_active:
            raise serializers.ValidationError("Cannot assign to inactive main classification")
        return value
