"""
Management command to create sample inventory setup data
"""
from django.core.management.base import BaseCommand
from inventory_setup.models import (
    MainClassification, SubClassification, EntryMode,
    ItemType, ItemCategory, ItemBrand, ItemManufacturer,
    ItemQuality, ItemShape, ItemSize, UnitOfMeasure,
    ItemStatus, PropertyStatus, ApprovalStatus, ItemTag
)


class Command(BaseCommand):
    help = 'Create sample inventory setup data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample inventory setup data...')

        # Create Main Classifications
        main_classes = [
            {'code': 'ELEC', 'name': 'Electronics', 'description': 'Electronic devices and components'},
            {'code': 'FURN', 'name': 'Furniture', 'description': 'Office and home furniture'},
            {'code': 'STAT', 'name': 'Stationery', 'description': 'Office supplies and stationery'},
            {'code': 'TOOL', 'name': 'Tools', 'description': 'Hand tools and equipment'},
        ]

        for mc_data in main_classes:
            mc, created = MainClassification.objects.get_or_create(
                code=mc_data['code'],
                defaults=mc_data
            )
            if created:
                self.stdout.write(f'Created main classification: {mc}')
            else:
                self.stdout.write(f'Main classification already exists: {mc}')

        # Create Sub Classifications
        try:
            elec_main = MainClassification.objects.get(code='ELEC')
            furn_main = MainClassification.objects.get(code='FURN')
            stat_main = MainClassification.objects.get(code='STAT')

            sub_classes = [
                {'main_class': elec_main, 'name': 'Computers', 'description': 'Desktop and laptop computers'},
                {'main_class': elec_main, 'name': 'Printers', 'description': 'Printing devices'},
                {'main_class': elec_main, 'name': 'Monitors', 'description': 'Display monitors'},
                {'main_class': furn_main, 'name': 'Desks', 'description': 'Office desks'},
                {'main_class': furn_main, 'name': 'Chairs', 'description': 'Office chairs'},
                {'main_class': stat_main, 'name': 'Pens', 'description': 'Writing instruments'},
                {'main_class': stat_main, 'name': 'Paper', 'description': 'Paper products'},
            ]

            for sc_data in sub_classes:
                # Check if sub classification already exists
                existing = SubClassification.objects.filter(
                    main_class=sc_data['main_class'],
                    name=sc_data['name']
                ).first()
                
                if not existing:
                    sc = SubClassification.objects.create(**sc_data)
                    self.stdout.write(f'Created sub classification: {sc}')
                else:
                    self.stdout.write(f'Sub classification already exists: {existing}')

        except MainClassification.DoesNotExist as e:
            self.stdout.write(f'Error: {e}')

        # Create Entry Modes
        entry_modes = [
            {'name': 'Purchase', 'description': 'Items purchased from suppliers', 'is_default': True},
            {'name': 'Transfer', 'description': 'Items transferred from other locations'},
            {'name': 'Donation', 'description': 'Items received as donations'},
            {'name': 'Production', 'description': 'Items produced internally', 'requires_approval': True},
            {'name': 'Return', 'description': 'Items returned from other departments'},
        ]

        for em_data in entry_modes:
            em, created = EntryMode.objects.get_or_create(
                name=em_data['name'],
                defaults=em_data
            )
            if created:
                self.stdout.write(f'Created entry mode: {em}')
            else:
                self.stdout.write(f'Entry mode already exists: {em}')

        # Create Item Types
        item_types = [
            {'name': 'Equipment', 'description': 'Durable equipment and machinery'},
            {'name': 'Supplies', 'description': 'Consumable supplies and materials'},
            {'name': 'Materials', 'description': 'Raw materials and components'},
            {'name': 'Tools', 'description': 'Hand tools and instruments'},
        ]

        for it_data in item_types:
            it, created = ItemType.objects.get_or_create(
                name=it_data['name'],
                defaults=it_data
            )
            if created:
                self.stdout.write(f'Created item type: {it}')
            else:
                self.stdout.write(f'Item type already exists: {it}')

        # Create Item Categories
        item_categories = [
            {'name': 'Office Supplies', 'description': 'General office supplies'},
            {'name': 'IT Equipment', 'description': 'Information technology equipment'},
            {'name': 'Furniture', 'description': 'Office and institutional furniture'},
            {'name': 'Cleaning Supplies', 'description': 'Cleaning and maintenance supplies'},
        ]

        for ic_data in item_categories:
            ic, created = ItemCategory.objects.get_or_create(
                name=ic_data['name'],
                defaults=ic_data
            )
            if created:
                self.stdout.write(f'Created item category: {ic}')
            else:
                self.stdout.write(f'Item category already exists: {ic}')

        # Create Item Brands
        item_brands = [
            {'name': 'HP', 'description': 'Hewlett-Packard technology products'},
            {'name': 'Dell', 'description': 'Dell computer systems'},
            {'name': 'Canon', 'description': 'Canon imaging and printing solutions'},
            {'name': 'Microsoft', 'description': 'Microsoft software and hardware'},
        ]

        for ib_data in item_brands:
            ib, created = ItemBrand.objects.get_or_create(
                name=ib_data['name'],
                defaults=ib_data
            )
            if created:
                self.stdout.write(f'Created item brand: {ib}')
            else:
                self.stdout.write(f'Item brand already exists: {ib}')

        # Create Item Manufacturers
        item_manufacturers = [
            {'name': 'Apple Inc.', 'country': 'United States', 'website': 'https://www.apple.com'},
            {'name': 'Samsung Electronics', 'country': 'South Korea', 'website': 'https://www.samsung.com'},
            {'name': 'Toyota Motor Corporation', 'country': 'Japan', 'website': 'https://www.toyota.com'},
            {'name': 'IKEA', 'country': 'Sweden', 'website': 'https://www.ikea.com'},
        ]

        for im_data in item_manufacturers:
            im, created = ItemManufacturer.objects.get_or_create(
                name=im_data['name'],
                defaults=im_data
            )
            if created:
                self.stdout.write(f'Created item manufacturer: {im}')
            else:
                self.stdout.write(f'Item manufacturer already exists: {im}')

        # Create Item Qualities
        item_qualities = [
            {'name': 'Excellent', 'description': 'Excellent condition'},
            {'name': 'Good', 'description': 'Good condition with minor wear'},
            {'name': 'Fair', 'description': 'Fair condition with visible wear'},
            {'name': 'Poor', 'description': 'Poor condition, needs repair'},
            {'name': 'New', 'description': 'Brand new, unused'},
            {'name': 'Refurbished', 'description': 'Professionally refurbished'},
        ]

        for iq_data in item_qualities:
            iq, created = ItemQuality.objects.get_or_create(
                name=iq_data['name'],
                defaults=iq_data
            )
            if created:
                self.stdout.write(f'Created item quality: {iq}')
            else:
                self.stdout.write(f'Item quality already exists: {iq}')

        # Create Item Shapes
        item_shapes = [
            {'name': 'Round', 'description': 'Circular or spherical shape'},
            {'name': 'Square', 'description': 'Square or cubic shape'},
            {'name': 'Rectangular', 'description': 'Rectangular or box shape'},
            {'name': 'Cylindrical', 'description': 'Cylindrical shape'},
            {'name': 'Triangular', 'description': 'Triangular shape'},
        ]

        for is_data in item_shapes:
            ishape, created = ItemShape.objects.get_or_create(
                name=is_data['name'],
                defaults=is_data
            )
            if created:
                self.stdout.write(f'Created item shape: {ishape}')
            else:
                self.stdout.write(f'Item shape already exists: {ishape}')

        # Create Item Sizes
        item_sizes = [
            {'name': 'Small', 'description': 'Small size'},
            {'name': 'Medium', 'description': 'Medium size'},
            {'name': 'Large', 'description': 'Large size'},
            {'name': 'Extra Large', 'description': 'Extra large size'},
            {'name': 'A4', 'description': 'A4 paper size'},
            {'name': 'Letter', 'description': 'Letter paper size'},
        ]

        for isize_data in item_sizes:
            isize, created = ItemSize.objects.get_or_create(
                name=isize_data['name'],
                defaults=isize_data
            )
            if created:
                self.stdout.write(f'Created item size: {isize}')
            else:
                self.stdout.write(f'Item size already exists: {isize}')

        # Create Units of Measure
        units_of_measure = [
            {'name': 'Piece', 'symbol': 'pcs', 'description': 'Individual pieces or units'},
            {'name': 'Kilogram', 'symbol': 'kg', 'description': 'Weight in kilograms'},
            {'name': 'Meter', 'symbol': 'm', 'description': 'Length in meters'},
            {'name': 'Liter', 'symbol': 'L', 'description': 'Volume in liters'},
            {'name': 'Box', 'symbol': 'box', 'description': 'Packaged in boxes'},
            {'name': 'Set', 'symbol': 'set', 'description': 'Complete sets'},
            {'name': 'Pair', 'symbol': 'pair', 'description': 'Pairs of items'},
        ]

        for uom_data in units_of_measure:
            uom, created = UnitOfMeasure.objects.get_or_create(
                name=uom_data['name'],
                defaults=uom_data
            )
            if created:
                self.stdout.write(f'Created unit of measure: {uom}')
            else:
                self.stdout.write(f'Unit of measure already exists: {uom}')

        # Create Item Statuses
        self.stdout.write('Creating item statuses...')
        ItemStatus.create_default_statuses()
        self.stdout.write('Item statuses created successfully')

        # Create Property Statuses
        self.stdout.write('Creating property statuses...')
        PropertyStatus.create_default_statuses()
        self.stdout.write('Property statuses created successfully')

        # Create Approval Statuses
        self.stdout.write('Creating approval statuses...')
        ApprovalStatus.create_default_statuses()
        self.stdout.write('Approval statuses created successfully')

        # Create Item Tags
        self.stdout.write('Creating item tags...')
        ItemTag.create_predefined_tags()
        self.stdout.write('Item tags created successfully')

        self.stdout.write(
            self.style.SUCCESS('Sample inventory setup data creation completed!')
        )
