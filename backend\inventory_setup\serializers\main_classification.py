"""
Main Classification Serializers
"""
from rest_framework import serializers
from ..models import MainClassification


class MainClassificationSerializer(serializers.ModelSerializer):
    """Serializer for MainClassification model"""
    
    sub_classifications_count = serializers.ReadOnlyField()
    total_sub_classifications = serializers.ReadOnlyField()
    
    class Meta:
        model = MainClassification
        fields = [
            'id',
            'code',
            'name',
            'description',
            'color',
            'is_active',
            'sub_classifications_count',
            'total_sub_classifications',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_code(self, value):
        """Validate and format code"""
        if value:
            value = value.upper().strip()
            if len(value) != 4:
                raise serializers.ValidationError("Code must be exactly 4 characters long")
            if not value.isalnum():
                raise serializers.ValidationError("Code must contain only letters and numbers")
        return value

    def validate_name(self, value):
        """Validate name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
        return value


class MainClassificationListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing main classifications"""

    sub_classifications_count = serializers.ReadOnlyField()

    class Meta:
        model = MainClassification
        fields = [
            'id',
            'code',
            'name',
            'description',
            'color',
            'is_active',
            'sub_classifications_count'
        ]


class MainClassificationDropdownSerializer(serializers.ModelSerializer):
    """Minimal serializer for dropdown/select options"""
    
    label = serializers.SerializerMethodField()
    value = serializers.CharField(source='id')
    
    class Meta:
        model = MainClassification
        fields = ['value', 'label', 'code', 'name']
    
    def get_label(self, obj):
        return f"{obj.code} - {obj.name}"
