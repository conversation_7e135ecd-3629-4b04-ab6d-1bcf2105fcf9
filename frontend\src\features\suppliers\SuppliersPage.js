import {
  Container,
  Typography,
  Box,
  Paper,
  Breadcrumbs,
  Link,
  Alert,
  Button,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import {
  Home as HomeIcon,
  Business as BusinessIcon,
  ArrowBack as ArrowBackIcon,
  Construction as ConstructionIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate, useParams } from 'react-router-dom';
import SupplierTypesList from './SupplierTypesList';

const SuppliersPage = () => {
  const navigate = useNavigate();
  const { type } = useParams(); // supplier-types, supplier-categories, suppliers
  
  // Route to specific components based on type
  if (type === 'supplier-types') {
    return <SupplierTypesList />;
  }
  
  const getPageInfo = () => {
    switch (type) {
      case 'supplier-categories':
        return {
          title: 'Supplier Categories',
          description: 'Organize suppliers by product categories',
          breadcrumb: 'Supplier Categories'
        };
      case 'suppliers':
        return {
          title: 'Suppliers',
          description: 'Manage supplier profiles and contact information',
          breadcrumb: 'Suppliers'
        };
      default:
        return {
          title: 'Supplier Management',
          description: 'Supplier management functionality',
          breadcrumb: 'Suppliers'
        };
    }
  };

  const pageInfo = getPageInfo();

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Link
          component={RouterLink}
          to="/suppliers-menu"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Supplier Management
        </Link>
        <Typography color="text.primary">
          {pageInfo.breadcrumb}
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box display="flex" alignItems="center">
          <BusinessIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
          <Typography variant="h4" component="h1" fontWeight="bold">
            {pageInfo.title}
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/suppliers-menu')}
        >
          Back to Menu
        </Button>
      </Box>

      {/* Under Construction Notice */}
      <Alert severity="info" sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          🚧 Under Development
        </Typography>
        <Typography variant="body2">
          {pageInfo.description} functionality is currently under development. 
          This will include comprehensive features for managing supplier relationships.
        </Typography>
      </Alert>

      {/* Placeholder Content */}
      <Paper sx={{ p: 4 }}>
        <Typography variant="h6" gutterBottom>
          {pageInfo.title}
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          This section will provide comprehensive {pageInfo.title.toLowerCase()} management capabilities including:
        </Typography>
        <Box component="ul" sx={{ pl: 3 }}>
          {type === 'supplier-categories' && (
            <>
              <Typography component="li" variant="body2" gutterBottom>
                Product and service category definitions
              </Typography>
              <Typography component="li" variant="body2" gutterBottom>
                Category-based supplier organization
              </Typography>
              <Typography component="li" variant="body2" gutterBottom>
                Category performance analytics
              </Typography>
            </>
          )}
          {type === 'suppliers' && (
            <>
              <Typography component="li" variant="body2" gutterBottom>
                Complete supplier profile management
              </Typography>
              <Typography component="li" variant="body2" gutterBottom>
                Contact information and communication tracking
              </Typography>
              <Typography component="li" variant="body2" gutterBottom>
                Performance ratings and evaluation
              </Typography>
              <Typography component="li" variant="body2" gutterBottom>
                Purchase order history and analytics
              </Typography>
            </>
          )}
        </Box>
      </Paper>

      {/* Development Notice */}
      <Paper sx={{ p: 3, mt: 3, textAlign: 'center', bgcolor: 'grey.50' }}>
        <ConstructionIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {pageInfo.title} Management
        </Typography>
        <Typography variant="body2" color="text.secondary">
          This feature will provide comprehensive {pageInfo.title.toLowerCase()} management including:
        </Typography>
        <Box component="ul" sx={{ textAlign: 'left', mt: 2, maxWidth: 400, mx: 'auto' }}>
          <li>Advanced search and filtering</li>
          <li>Bulk operations and data import</li>
          <li>Performance tracking and analytics</li>
          <li>Integration with purchase orders</li>
          <li>Automated notifications and alerts</li>
        </Box>
      </Paper>
    </Container>
  );
};

export default SuppliersPage;
