/**
 * Item Management Dashboard
 * Professional dashboard-style interface matching Organization and Supplier menus
 */

import React, { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  Paper,
  useTheme,
  alpha,
  Chip,
  CircularProgress,
  Breadcrumbs,
  Link
} from '@mui/material';
import {
  Inventory as InventoryIcon,
  Receipt as ReceiptIcon,
  Category as CategoryIcon,
  QrCode as QrCodeIcon,
  Insights as InsightsIcon,
  Home as HomeIcon,
  Dashboard as DashboardIcon
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import api from '../../utils/axios';

const ItemManagementDashboard = () => {
  const theme = useTheme();
  const { enqueueSnackbar } = useSnackbar();
  
  const [counts, setCounts] = useState({
    serialVouchers: 0,
    serialVoucherCategories: 0,
    loading: true
  });

  useEffect(() => {
    loadCounts();
  }, []);

  const loadCounts = async () => {
    try {
      const [vouchersResponse, categoriesResponse] = await Promise.all([
        api.get('/serial-vouchers/'),
        api.get('/serial-voucher-categories/')
      ]);
      
      setCounts({
        serialVouchers: vouchersResponse.data.results?.length || vouchersResponse.data.length || 0,
        serialVoucherCategories: categoriesResponse.data.results?.length || categoriesResponse.data.length || 0,
        loading: false
      });
    } catch (error) {
      console.error('Error loading counts:', error);
      setCounts(prev => ({ ...prev, loading: false }));
    }
  };

  // Item management cards data
  const itemCards = [
    {
      id: 'serial-vouchers',
      title: 'Serial Vouchers',
      description: 'Manage automated serial number generation for items',
      icon: <ReceiptIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.primary.main,
      path: '/items/serial-vouchers',
      count: counts.loading ? <CircularProgress size={16} /> : counts.serialVouchers,
      adminOnly: false
    },
    {
      id: 'voucher-categories',
      title: 'Voucher Categories',
      description: 'Organize serial vouchers by item categories',
      icon: <CategoryIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.success.main,
      path: '/items/serial-voucher-categories',
      count: counts.loading ? <CircularProgress size={16} /> : counts.serialVoucherCategories,
      adminOnly: false
    },
    {
      id: 'item-catalog',
      title: 'Item Catalog',
      description: 'Comprehensive item database and specifications',
      icon: <InventoryIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.info.main,
      path: '/items/catalog',
      count: 'Soon',
      adminOnly: false
    },
    {
      id: 'barcode-management',
      title: 'Barcode Management',
      description: 'Generate and manage item barcodes and QR codes',
      icon: <QrCodeIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.warning.main,
      path: '/items/barcodes',
      count: 'Soon',
      adminOnly: false
    }
  ];

  // Filter cards based on user role (for now showing all)
  const filteredCards = itemCards.filter(card => !card.adminOnly || true);

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <DashboardIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Item Management
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center' }}>
        <InventoryIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
        <Typography variant="h4" component="h1" gutterBottom>
          Item Management Dashboard
        </Typography>
      </Box>

      {/* Info Paper */}
      <Paper
        sx={{
          p: 2,
          mb: 4,
          display: 'flex',
          alignItems: 'center',
          bgcolor: alpha(theme.palette.primary.main, 0.1),
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
          borderRadius: 2
        }}
      >
        <InsightsIcon color="primary" sx={{ mr: 2 }} />
        <Box>
          <Typography variant="h6" color="primary.main">
            Item Management Center
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Comprehensive item lifecycle management system for cataloging, tracking, serial number generation, and inventory control
          </Typography>
        </Box>
      </Paper>

      {/* Item Management Cards */}
      <Grid container spacing={3}>
        {filteredCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={6} key={index}>
            <Card
              component={RouterLink}
              to={card.path}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0,0,0,0.1)',
                },
                textDecoration: 'none',
                borderTop: `4px solid ${card.color}`,
                borderRadius: 2,
                position: 'relative',
                overflow: 'visible'
              }}
            >
              {card.count && (
                <Chip
                  label={card.count}
                  color="primary"
                  size="small"
                  sx={{
                    position: 'absolute',
                    top: -10,
                    right: 16,
                    fontWeight: 'bold',
                    boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
                  }}
                />
              )}
              <Box
                sx={{
                  p: 2,
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: alpha(card.color, 0.1),
                }}
              >
                <Box
                  sx={{
                    mr: 2,
                    p: 1.5,
                    borderRadius: '50%',
                    bgcolor: alpha(card.color, 0.2),
                    color: card.color,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {card.icon}
                </Box>
                <Typography variant="h6" component="div" color="text.primary">
                  {card.title}
                </Typography>
              </Box>
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  {card.description}
                </Typography>
              </CardContent>
              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  size="small"
                  sx={{
                    color: card.color,
                    '&:hover': {
                      bgcolor: alpha(card.color, 0.1)
                    }
                  }}
                >
                  {card.count === 'Soon' ? 'Coming Soon' : 'Manage'}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Quick Stats */}
      <Paper sx={{ p: 3, mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Quick Statistics
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary.main" fontWeight="bold">
                {counts.loading ? <CircularProgress size={24} /> : counts.serialVouchers}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Serial Vouchers
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main" fontWeight="bold">
                {counts.loading ? <CircularProgress size={24} /> : counts.serialVoucherCategories}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Voucher Categories
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="info.main" fontWeight="bold">
                0
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Items Cataloged
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main" fontWeight="bold">
                0
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Barcodes Generated
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default ItemManagementDashboard;
