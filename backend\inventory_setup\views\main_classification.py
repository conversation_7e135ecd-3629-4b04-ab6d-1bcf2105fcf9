"""
Main Classification Views
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from ..models import MainClassification
from ..serializers import (
    MainClassificationSerializer,
    MainClassificationListSerializer,
    MainClassificationDropdownSerializer
)


class MainClassificationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Main Classifications
    
    Provides CRUD operations for main classifications with filtering,
    searching, and dropdown endpoints.
    """
    queryset = MainClassification.objects.all()
    serializer_class = MainClassificationSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['code', 'name', 'description']
    ordering_fields = ['code', 'name', 'created_at', 'updated_at']
    ordering = ['code']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return MainClassificationListSerializer
        elif self.action == 'dropdown':
            return MainClassificationDropdownSerializer
        return MainClassificationSerializer

    def get_queryset(self):
        """Filter queryset based on action"""
        queryset = MainClassification.objects.all()
        
        if self.action == 'dropdown':
            # Only return active items for dropdown
            queryset = queryset.filter(is_active=True)
        
        return queryset

    @swagger_auto_schema(
        method='get',
        operation_description="Get main classifications for dropdown/select options",
        responses={
            200: openapi.Response(
                description="List of main classifications for dropdown",
                examples={
                    "application/json": [
                        {
                            "value": "uuid-here",
                            "label": "ELEC - Electronics",
                            "code": "ELEC",
                            "name": "Electronics"
                        }
                    ]
                }
            )
        }
    )
    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get main classifications formatted for dropdown/select options"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='post',
        operation_description="Bulk activate/deactivate main classifications",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'ids': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description='List of main classification IDs'
                ),
                'is_active': openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    description='Active status to set'
                )
            },
            required=['ids', 'is_active']
        )
    )
    @action(detail=False, methods=['post'])
    def bulk_update_status(self, request):
        """Bulk update active status of main classifications"""
        ids = request.data.get('ids', [])
        is_active = request.data.get('is_active')
        
        if not ids:
            return Response(
                {'error': 'No IDs provided'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if is_active is None:
            return Response(
                {'error': 'is_active field is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        updated_count = MainClassification.objects.filter(
            id__in=ids
        ).update(is_active=is_active)
        
        return Response({
            'message': f'Updated {updated_count} main classifications',
            'updated_count': updated_count
        })

    def destroy(self, request, *args, **kwargs):
        """Soft delete main classification"""
        instance = self.get_object()
        
        # Check if has active sub-classifications
        if instance.sub_classifications.filter(is_active=True).exists():
            return Response(
                {'error': 'Cannot delete main classification with active sub-classifications'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        instance.soft_delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
