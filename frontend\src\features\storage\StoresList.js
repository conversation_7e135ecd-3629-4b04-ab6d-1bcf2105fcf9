import { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  CircularProgress,
  Breadcrumbs,
  Link,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  Store as StoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Home as HomeIcon,
  Storage as StorageIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { Link as RouterLink } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import api from '../../utils/axios';

const StoresList = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  
  const [stores, setStores] = useState([]);
  const [storeTypes, setStoreTypes] = useState([]);
  const [organizations, setOrganizations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [formDialog, setFormDialog] = useState({ open: false, store: null });
  const [deleteDialog, setDeleteDialog] = useState({ open: false, store: null });
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    store_type: '',
    organization: '',
    location: '',
    manager_name: '',
    phone: '',
    email: '',
    is_active: true
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [storesResponse, storeTypesResponse, organizationsResponse] = await Promise.all([
        api.get('/stores/'),
        api.get('/store-types/'),
        api.get('/organizations/')
      ]);

      setStores(storesResponse.data.results || storesResponse.data);
      setStoreTypes(storeTypesResponse.data.results || storeTypesResponse.data);
      setOrganizations(organizationsResponse.data.results || organizationsResponse.data);
    } catch (error) {
      console.error('Error loading data:', error);
      enqueueSnackbar('Failed to load stores data', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenForm = (store = null) => {
    if (store) {
      setFormData({
        name: store.name,
        code: store.code,
        store_type: store.store_type,
        organization: store.organization,
        location: store.location,
        manager_name: store.manager_name || '',
        phone: store.phone || '',
        email: store.email || '',
        is_active: store.is_active
      });
    } else {
      setFormData({
        name: '',
        code: '',
        store_type: '',
        organization: '',
        location: '',
        manager_name: '',
        phone: '',
        email: '',
        is_active: true
      });
    }
    setFormDialog({ open: true, store });
  };

  const handleCloseForm = () => {
    setFormDialog({ open: false, store: null });
    setFormData({
      name: '',
      code: '',
      store_type: '',
      organization: '',
      location: '',
      manager_name: '',
      phone: '',
      email: '',
      is_active: true
    });
  };

  const handleSubmit = async () => {
    if (!formData.name.trim() || !formData.code.trim() || !formData.store_type || !formData.organization) {
      enqueueSnackbar('Please fill in all required fields', { variant: 'error' });
      return;
    }

    setSaving(true);
    try {
      const submitData = {
        name: formData.name.trim(),
        code: formData.code.trim().toUpperCase(),
        store_type: formData.store_type,
        organization: formData.organization,
        location: formData.location.trim(),
        manager_name: formData.manager_name.trim(),
        phone: formData.phone.trim(),
        email: formData.email.trim(),
        is_active: formData.is_active
      };

      if (formDialog.store) {
        await api.put(`/stores/${formDialog.store.id}/`, submitData);
        enqueueSnackbar('Store updated successfully', { variant: 'success' });
      } else {
        await api.post('/stores/', submitData);
        enqueueSnackbar('Store created successfully', { variant: 'success' });
      }
      
      handleCloseForm();
      loadData();
    } catch (error) {
      console.error('Error saving store:', error);
      if (error.response?.data?.code) {
        enqueueSnackbar('Store code already exists', { variant: 'error' });
      } else {
        enqueueSnackbar('Failed to save store', { variant: 'error' });
      }
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    try {
      await api.delete(`/stores/${deleteDialog.store.id}/`);
      enqueueSnackbar('Store deleted successfully', { variant: 'success' });
      setDeleteDialog({ open: false, store: null });
      loadData();
    } catch (error) {
      console.error('Error deleting store:', error);
      enqueueSnackbar('Failed to delete store', { variant: 'error' });
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 3 }}>
            Loading stores...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Link
          component={RouterLink}
          to="/storage-menu"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <StorageIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Storage Management
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <StoreIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Stores
        </Typography>
      </Breadcrumbs>

      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box display="flex" alignItems="center">
          <StoreIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
          <Typography variant="h4" component="h1" fontWeight="bold">
            Stores
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadData}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenForm()}
          >
            Add Store
          </Button>
        </Box>
      </Box>

      {/* Stores Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Code</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Organization</TableCell>
              <TableCell>Location</TableCell>
              <TableCell>Manager</TableCell>
              <TableCell>Shelves</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {stores.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} align="center" sx={{ py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    No stores found. Create your first store to get started.
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              stores.map((store) => (
                <TableRow key={store.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {store.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={store.code} 
                      size="small" 
                      variant="outlined"
                      sx={{ fontFamily: 'monospace', fontWeight: 'bold' }}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {store.store_type_name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {store.organization_name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {store.location}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {store.manager_name || 'Not assigned'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {store.shelves_count || 0} shelves
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={store.is_active ? 'Active' : 'Inactive'}
                      color={store.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Edit Store">
                        <IconButton
                          size="small"
                          onClick={() => handleOpenForm(store)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Store">
                        <IconButton
                          size="small"
                          onClick={() => setDeleteDialog({ open: true, store })}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Form Dialog */}
      <Dialog open={formDialog.open} onClose={handleCloseForm} maxWidth="md" fullWidth>
        <DialogTitle>
          {formDialog.store ? 'Edit Store' : 'Create Store'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Store Name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Store Code"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                  required
                  helperText="Unique identifier for the store"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Store Type</InputLabel>
                  <Select
                    value={formData.store_type}
                    onChange={(e) => setFormData({ ...formData, store_type: e.target.value })}
                    label="Store Type"
                  >
                    {storeTypes.filter(type => type.is_active).map((type) => (
                      <MenuItem key={type.id} value={type.id}>
                        {type.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Organization</InputLabel>
                  <Select
                    value={formData.organization}
                    onChange={(e) => setFormData({ ...formData, organization: e.target.value })}
                    label="Organization"
                  >
                    {organizations.map((org) => (
                      <MenuItem key={org.id} value={org.id}>
                        {org.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Location"
                  value={formData.location}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  required
                  helperText="Physical address or location description"
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Manager Name"
                  value={formData.manager_name}
                  onChange={(e) => setFormData({ ...formData, manager_name: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Phone"
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_active}
                      onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                    />
                  }
                  label="Active"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseForm} disabled={saving}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} variant="contained" disabled={saving}>
            {saving ? 'Saving...' : (formDialog.store ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, store: null })}>
        <DialogTitle>Delete Store</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the store "{deleteDialog.store?.name} ({deleteDialog.store?.code})"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, store: null })}>
            Cancel
          </Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default StoresList;
