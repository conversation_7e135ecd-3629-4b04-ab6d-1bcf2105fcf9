import { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  CircularProgress,
  Breadcrumbs,
  Link,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  ViewModule as ShelfIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Home as HomeIcon,
  Storage as StorageIcon,
  Refresh as RefreshIcon,
  Store as StoreIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { Link as RouterLink } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import api from '../../utils/axios';

const ShelvesList = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  
  const [shelves, setShelves] = useState([]);
  const [stores, setStores] = useState([]);
  const [loading, setLoading] = useState(true);
  const [formDialog, setFormDialog] = useState({ open: false, shelf: null });
  const [deleteDialog, setDeleteDialog] = useState({ open: false, shelf: null });
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    store: '',
    code: '',
    row: 1,
    column: 1,
    description: '',
    capacity: 1,
    is_active: true
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [shelvesResponse, storesResponse] = await Promise.all([
        api.get('/shelves/'),
        api.get('/stores/')
      ]);
      
      setShelves(shelvesResponse.data.results || shelvesResponse.data);
      setStores(storesResponse.data.results || storesResponse.data);
    } catch (error) {
      console.error('Error loading data:', error);
      enqueueSnackbar('Failed to load shelves data', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenForm = (shelf = null) => {
    if (shelf) {
      setFormData({
        store: shelf.store,
        code: shelf.code,
        row: shelf.row,
        column: shelf.column,
        description: shelf.description || '',
        capacity: shelf.capacity,
        is_active: shelf.is_active
      });
    } else {
      setFormData({
        store: '',
        code: '',
        row: 1,
        column: 1,
        description: '',
        capacity: 1,
        is_active: true
      });
    }
    setFormDialog({ open: true, shelf });
  };

  const handleCloseForm = () => {
    setFormDialog({ open: false, shelf: null });
    setFormData({
      store: '',
      code: '',
      row: 1,
      column: 1,
      description: '',
      capacity: 1,
      is_active: true
    });
  };

  const handleSubmit = async () => {
    if (!formData.store || !formData.code.trim() || formData.row < 1 || formData.column < 1 || formData.capacity < 1) {
      enqueueSnackbar('Please fill in all required fields with valid values', { variant: 'error' });
      return;
    }

    setSaving(true);
    try {
      const submitData = {
        store: formData.store,
        code: formData.code.trim().toUpperCase(),
        row: parseInt(formData.row),
        column: parseInt(formData.column),
        description: formData.description.trim(),
        capacity: parseInt(formData.capacity),
        is_active: formData.is_active
      };

      if (formDialog.shelf) {
        await api.put(`/shelves/${formDialog.shelf.id}/`, submitData);
        enqueueSnackbar('Shelf updated successfully', { variant: 'success' });
      } else {
        await api.post('/shelves/', submitData);
        enqueueSnackbar('Shelf created successfully', { variant: 'success' });
      }
      
      handleCloseForm();
      loadData();
    } catch (error) {
      console.error('Error saving shelf:', error);
      if (error.response?.data) {
        const errorData = error.response.data;
        if (errorData.code) {
          enqueueSnackbar('Shelf code already exists in this store', { variant: 'error' });
        } else if (errorData.row || errorData.column) {
          enqueueSnackbar('A shelf already exists at this position', { variant: 'error' });
        } else {
          enqueueSnackbar('Failed to save shelf', { variant: 'error' });
        }
      } else {
        enqueueSnackbar('Failed to save shelf', { variant: 'error' });
      }
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    try {
      await api.delete(`/shelves/${deleteDialog.shelf.id}/`);
      enqueueSnackbar('Shelf deleted successfully', { variant: 'success' });
      setDeleteDialog({ open: false, shelf: null });
      loadData();
    } catch (error) {
      console.error('Error deleting shelf:', error);
      enqueueSnackbar('Failed to delete shelf', { variant: 'error' });
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 3 }}>
            Loading shelves...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Link
          component={RouterLink}
          to="/storage-menu"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <StorageIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Storage Management
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <ShelfIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Shelves
        </Typography>
      </Breadcrumbs>

      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box display="flex" alignItems="center">
          <ShelfIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
          <Typography variant="h4" component="h1" fontWeight="bold">
            Shelves
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadData}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenForm()}
          >
            Add Shelf
          </Button>
        </Box>
      </Box>

      {/* Shelves Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Store</TableCell>
              <TableCell>Code</TableCell>
              <TableCell>Position</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Capacity</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {shelves.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    No shelves found. Create your first shelf to get started.
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              shelves.map((shelf) => (
                <TableRow key={shelf.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <StoreIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {shelf.store_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {shelf.store_code}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={shelf.code} 
                      size="small" 
                      variant="outlined"
                      sx={{ fontFamily: 'monospace', fontWeight: 'bold' }}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                      {shelf.position_code}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {shelf.description || 'No description'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {shelf.capacity} items
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={shelf.is_active ? 'Active' : 'Inactive'}
                      color={shelf.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Edit Shelf">
                        <IconButton
                          size="small"
                          onClick={() => handleOpenForm(shelf)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Shelf">
                        <IconButton
                          size="small"
                          onClick={() => setDeleteDialog({ open: true, shelf })}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Form Dialog */}
      <Dialog open={formDialog.open} onClose={handleCloseForm} maxWidth="md" fullWidth>
        <DialogTitle>
          {formDialog.shelf ? 'Edit Shelf' : 'Create Shelf'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <InputLabel>Store</InputLabel>
                  <Select
                    value={formData.store}
                    onChange={(e) => setFormData({ ...formData, store: e.target.value })}
                    label="Store"
                  >
                    {stores.filter(store => store.is_active).map((store) => (
                      <MenuItem key={store.id} value={store.id}>
                        {store.name} ({store.code})
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Shelf Code"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                  required
                  helperText="Unique identifier for the shelf"
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Row"
                  type="number"
                  value={formData.row}
                  onChange={(e) => setFormData({ ...formData, row: parseInt(e.target.value) || 1 })}
                  required
                  inputProps={{ min: 1 }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Column"
                  type="number"
                  value={formData.column}
                  onChange={(e) => setFormData({ ...formData, column: parseInt(e.target.value) || 1 })}
                  required
                  inputProps={{ min: 1 }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  multiline
                  rows={2}
                  helperText="Optional description of the shelf"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Capacity"
                  type="number"
                  value={formData.capacity}
                  onChange={(e) => setFormData({ ...formData, capacity: parseInt(e.target.value) || 1 })}
                  required
                  inputProps={{ min: 1 }}
                  helperText="Maximum number of items"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_active}
                      onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                    />
                  }
                  label="Active"
                  sx={{ mt: 2 }}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseForm} disabled={saving}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} variant="contained" disabled={saving}>
            {saving ? 'Saving...' : (formDialog.shelf ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, shelf: null })}>
        <DialogTitle>Delete Shelf</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the shelf "{deleteDialog.shelf?.store_code}-{deleteDialog.shelf?.code}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, shelf: null })}>
            Cancel
          </Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ShelvesList;
