"""
Item Manufacturer Serializers
"""
from rest_framework import serializers
from ..models import ItemManufacturer


class ItemManufacturerSerializer(serializers.ModelSerializer):
    """Serializer for ItemManufacturer model"""
    
    usage_count = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = ItemManufacturer
        fields = [
            'id',
            'name',
            'country',
            'website',
            'description',
            'display_name',
            'is_active',
            'usage_count',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_name(self, value):
        """Validate name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
        return value

    def validate_country(self, value):
        """Validate country"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Country must be at least 2 characters long")
        return value


class ItemManufacturerListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing item manufacturers"""
    
    usage_count = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = ItemManufacturer
        fields = [
            'id',
            'name',
            'country',
            'display_name',
            'is_active',
            'usage_count'
        ]


class ItemManufacturerDropdownSerializer(serializers.ModelSerializer):
    """Minimal serializer for dropdown/select options"""
    
    label = serializers.SerializerMethodField()
    value = serializers.CharField(source='id')
    
    class Meta:
        model = ItemManufacturer
        fields = ['value', 'label', 'country']
    
    def get_label(self, obj):
        return f"{obj.name} ({obj.country})"
