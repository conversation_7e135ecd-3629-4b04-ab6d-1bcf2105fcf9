"""
Inventory Setup Admin Configuration
"""
from django.contrib import admin
from .models import (
    MainClassification, SubClassification, EntryMode,
    ItemType, ItemCategory, ItemBrand, ItemManufacturer,
    ItemQuality, ItemShape, ItemSize, UnitOfMeasure,
    ItemStatus, PropertyStatus, ApprovalStatus, ItemTag
)


@admin.register(MainClassification)
class MainClassificationAdmin(admin.ModelAdmin):
    """Admin configuration for MainClassification"""
    list_display = ['code', 'name', 'usage_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['code', 'name', 'description']
    ordering = ['code']
    readonly_fields = ['created_at', 'updated_at', 'usage_count']

    fieldsets = (
        (None, {
            'fields': ('code', 'name', 'description', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SubClassification)
class SubClassificationAdmin(admin.ModelAdmin):
    """Admin configuration for SubClassification"""
    list_display = ['full_code', 'name', 'main_class', 'is_active', 'created_at']
    list_filter = ['is_active', 'main_class', 'created_at']
    search_fields = ['code', 'name', 'description', 'main_class__name', 'main_class__code']
    ordering = ['main_class__code', 'code']
    readonly_fields = ['code', 'created_at', 'updated_at', 'full_code']

    fieldsets = (
        (None, {
            'fields': ('main_class', 'code', 'name', 'description', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(EntryMode)
class EntryModeAdmin(admin.ModelAdmin):
    """Admin configuration for EntryMode"""
    list_display = ['name', 'code', 'is_default', 'requires_approval', 'allows_negative_stock', 'is_active', 'created_at']
    list_filter = ['is_active', 'is_default', 'requires_approval', 'allows_negative_stock', 'created_at']
    search_fields = ['name', 'description', 'code']
    ordering = ['name']
    readonly_fields = ['code', 'created_at', 'updated_at']

    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'description', 'is_active')
        }),
        ('Settings', {
            'fields': ('is_default', 'requires_approval', 'allows_negative_stock')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


# Simple admin configurations for the new models
@admin.register(ItemType)
class ItemTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'usage_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at', 'usage_count']


@admin.register(ItemCategory)
class ItemCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'usage_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at', 'usage_count']


@admin.register(ItemBrand)
class ItemBrandAdmin(admin.ModelAdmin):
    list_display = ['name', 'usage_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at', 'usage_count']


@admin.register(ItemManufacturer)
class ItemManufacturerAdmin(admin.ModelAdmin):
    list_display = ['name', 'country', 'usage_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'country', 'created_at']
    search_fields = ['name', 'country', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at', 'usage_count', 'display_name']


@admin.register(ItemQuality)
class ItemQualityAdmin(admin.ModelAdmin):
    list_display = ['name', 'usage_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at', 'usage_count']


@admin.register(ItemShape)
class ItemShapeAdmin(admin.ModelAdmin):
    list_display = ['name', 'usage_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at', 'usage_count']


@admin.register(ItemSize)
class ItemSizeAdmin(admin.ModelAdmin):
    list_display = ['name', 'usage_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at', 'usage_count']


@admin.register(UnitOfMeasure)
class UnitOfMeasureAdmin(admin.ModelAdmin):
    list_display = ['name', 'symbol', 'usage_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'symbol', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at', 'usage_count', 'display_name']


# Enhanced admin configurations for status and tag models
@admin.register(ItemStatus)
class ItemStatusAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'status_type', 'color_code', 'is_active', 'is_default', 'allows_checkout']
    list_filter = ['is_active', 'status_type', 'allows_checkout', 'requires_approval', 'is_default']
    search_fields = ['name', 'code', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at', 'usage_count', 'display_name']
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'status_type')
        }),
        ('Display Settings', {
            'fields': ('color_code',)
        }),
        ('Behavior Settings', {
            'fields': ('is_active', 'is_default', 'allows_checkout', 'requires_approval')
        }),
        ('Metadata', {
            'fields': ('usage_count', 'display_name', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PropertyStatus)
class PropertyStatusAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'status_type', 'color_code', 'is_active', 'is_default', 'affects_value']
    list_filter = ['is_active', 'status_type', 'affects_value', 'requires_inspection', 'is_default']
    search_fields = ['name', 'code', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at', 'usage_count', 'display_name']
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'status_type')
        }),
        ('Display Settings', {
            'fields': ('color_code',)
        }),
        ('Behavior Settings', {
            'fields': ('is_active', 'is_default', 'affects_value', 'requires_inspection')
        }),
        ('Metadata', {
            'fields': ('usage_count', 'display_name', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ApprovalStatus)
class ApprovalStatusAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'status_type', 'color_code', 'is_active', 'is_default', 'is_final']
    list_filter = ['is_active', 'status_type', 'is_final', 'allows_modification', 'requires_comment', 'is_default']
    search_fields = ['name', 'code', 'description']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at', 'usage_count', 'display_name']
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'status_type')
        }),
        ('Display Settings', {
            'fields': ('color_code',)
        }),
        ('Behavior Settings', {
            'fields': ('is_active', 'is_default', 'is_final', 'allows_modification', 'requires_comment')
        }),
        ('Metadata', {
            'fields': ('usage_count', 'display_name', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ItemTag)
class ItemTagAdmin(admin.ModelAdmin):
    list_display = ['name', 'tag_type', 'color_code', 'is_active', 'is_system_tag']
    list_filter = ['is_active', 'tag_type', 'is_system_tag']
    search_fields = ['name', 'slug', 'description']
    ordering = ['name']
    readonly_fields = ['slug', 'created_at', 'updated_at', 'usage_count', 'display_name']
    prepopulated_fields = {'slug': ('name',)}
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'tag_type', 'description')
        }),
        ('Display Settings', {
            'fields': ('color_code',)
        }),
        ('Settings', {
            'fields': ('is_active', 'is_system_tag')
        }),
        ('Metadata', {
            'fields': ('usage_count', 'display_name', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
