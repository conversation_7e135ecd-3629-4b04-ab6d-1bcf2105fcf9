"""
Property Status Model for Inventory Setup
"""
import uuid
from django.db import models
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _


class PropertyStatus(models.Model):
    """
    Model for property status tracking (Serviceable, Unserviceable, etc.)
    """
    
    # Status type choices
    STATUS_TYPES = [
        ('serviceable', 'Serviceable'),
        ('unserviceable', 'Unserviceable'),
        ('condemned', 'Condemned'),
        ('pending', 'Pending Review'),
    ]
    
    # Predefined color choices
    COLOR_CHOICES = [
        ('#4CAF50', 'Green - Serviceable/Good'),
        ('#FF9800', 'Orange - Needs Attention'),
        ('#F44336', 'Red - Unserviceable/Critical'),
        ('#9C27B0', 'Purple - Condemned'),
        ('#607D8B', 'Gray - Pending/Unknown'),
        ('#2196F3', 'Blue - Under Review'),
        ('#795548', 'Brown - Archived'),
        ('#E91E63', 'Pink - Special Status'),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text=_("Unique identifier for the property status")
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        null=True,
        blank=True,
        validators=[RegexValidator(
            regex=r'^[A-Z0-9_-]+$',
            message=_("Code must contain only uppercase letters, numbers, underscores, and hyphens")
        )],
        verbose_name=_("Status Code"),
        help_text=_("Unique code for the status (e.g., 'SERVICEABLE', 'UNSERVICEABLE')")
    )
    
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_("Status Name"),
        help_text=_("Human-readable name for the property status")
    )
    
    description = models.TextField(
        blank=True,
        verbose_name=_("Description"),
        help_text=_("Detailed description of what this property status means")
    )
    
    status_type = models.CharField(
        max_length=20,
        choices=STATUS_TYPES,
        default='serviceable',
        verbose_name=_("Status Type"),
        help_text=_("Category of this property status")
    )
    
    color_code = models.CharField(
        max_length=7,
        choices=COLOR_CHOICES,
        default='#4CAF50',
        validators=[RegexValidator(
            regex=r'^#[0-9A-Fa-f]{6}$',
            message=_("Color code must be a valid hex color (e.g., #FF0000)")
        )],
        verbose_name=_("Color Code"),
        help_text=_("Hex color code for displaying this status")
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("Whether this status is available for use")
    )

    is_default = models.BooleanField(
        default=False,
        verbose_name=_("Default Status"),
        help_text=_("Whether this is the default property status")
    )

    affects_value = models.BooleanField(
        default=False,
        verbose_name=_("Affects Value"),
        help_text=_("Whether this status affects the item's value")
    )

    requires_inspection = models.BooleanField(
        default=False,
        verbose_name=_("Requires Inspection"),
        help_text=_("Whether items with this status require regular inspection")
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Created At"),
        help_text=_("Date and time when the record was created")
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_("Updated At"),
        help_text=_("Date and time when the record was last updated")
    )

    class Meta:
        verbose_name = _("Property Status")
        verbose_name_plural = _("Property Statuses")
        db_table = 'inventory_property_status'
        ordering = ['name']
        indexes = [
            models.Index(fields=['code'], name='inv_prop_status_code_idx'),
            models.Index(fields=['is_active'], name='inv_prop_status_active_idx'),
            models.Index(fields=['status_type'], name='inv_prop_status_type_idx'),
        ]

    def __str__(self):
        return f"{self.name}" + (f" ({self.code})" if self.code else "")

    def save(self, *args, **kwargs):
        # Auto-generate code if not provided
        if not self.code and self.name:
            from django.utils.text import slugify
            self.code = slugify(self.name).upper().replace('-', '_')
        
        # Ensure only one default status
        if self.is_default:
            PropertyStatus.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)

    @property
    def usage_count(self):
        """Count of items using this property status"""
        # This will be implemented when item model is created
        return 0

    @property
    def display_name(self):
        """Display name with code if available"""
        return f"{self.name}" + (f" ({self.code})" if self.code else "")

    def clean(self):
        """Validate the model"""
        from django.core.exceptions import ValidationError
        
        if self.code:
            self.code = self.code.upper()
        
        # Validate color code format
        if self.color_code and not self.color_code.startswith('#'):
            raise ValidationError({'color_code': _("Color code must start with #")})

    @classmethod
    def get_default_status(cls):
        """Get the default property status"""
        return cls.objects.filter(is_default=True, is_active=True).first()

    @classmethod
    def create_default_statuses(cls):
        """Create default property statuses if they don't exist"""
        default_statuses = [
            {
                'code': 'SERVICEABLE',
                'name': 'Serviceable',
                'description': 'Property is in good working condition',
                'status_type': 'serviceable',
                'color_code': '#4CAF50',
                'is_default': True,
                'affects_value': False
            },
            {
                'code': 'FAIR',
                'name': 'Fair Condition',
                'description': 'Property is functional but shows wear',
                'status_type': 'serviceable',
                'color_code': '#FF9800',
                'affects_value': True,
                'requires_inspection': True
            },
            {
                'code': 'POOR',
                'name': 'Poor Condition',
                'description': 'Property needs repair or maintenance',
                'status_type': 'unserviceable',
                'color_code': '#F44336',
                'affects_value': True,
                'requires_inspection': True
            },
            {
                'code': 'UNSERVICEABLE',
                'name': 'Unserviceable',
                'description': 'Property cannot be used in current condition',
                'status_type': 'unserviceable',
                'color_code': '#F44336',
                'affects_value': True,
                'requires_inspection': True
            },
            {
                'code': 'CONDEMNED',
                'name': 'Condemned',
                'description': 'Property is condemned and should be disposed',
                'status_type': 'condemned',
                'color_code': '#9C27B0',
                'affects_value': True,
                'requires_inspection': True
            },
            {
                'code': 'PENDING',
                'name': 'Pending Review',
                'description': 'Property status is under review',
                'status_type': 'pending',
                'color_code': '#607D8B',
                'requires_inspection': True
            }
        ]
        
        for status_data in default_statuses:
            cls.objects.get_or_create(
                code=status_data['code'],
                defaults=status_data
            )


