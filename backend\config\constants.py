"""
Application Constants and Configuration
"""
import os
from django.conf import settings

# Default Organization Values
DEFAULT_ORGANIZATION = {
    'name': os.getenv('DEFAULT_ORG_NAME', 'Asset Management System'),
    'shortcode': os.getenv('DEFAULT_ORG_SHORTCODE', 'AMS'),
    'motto': os.getenv('DEFAULT_ORG_MOTTO', 'Professional Inventory Management'),
    'phone': os.getenv('DEFAULT_ORG_PHONE', ''),
    'email': os.getenv('DEFAULT_ORG_EMAIL', ''),
    'website': os.getenv('DEFAULT_ORG_WEBSITE', ''),
    'address': os.getenv('DEFAULT_ORG_ADDRESS', ''),
    'city': os.getenv('DEFAULT_ORG_CITY', ''),
    'country': os.getenv('DEFAULT_ORG_COUNTRY', ''),
}

# Application Configuration
APP_CONFIG = {
    'name': os.getenv('APP_NAME', 'Asset Management System'),
    'version': os.getenv('APP_VERSION', '1.0.0'),
    'environment': os.getenv('DJANGO_ENV', 'development'),
    'debug': settings.DEBUG,
}

# API Configuration
API_CONFIG = {
    'version': 'v1',
    'pagination_size': int(os.getenv('API_PAGINATION_SIZE', '20')),
    'max_pagination_size': int(os.getenv('API_MAX_PAGINATION_SIZE', '100')),
}

# File Upload Configuration
UPLOAD_CONFIG = {
    'max_file_size': int(os.getenv('MAX_FILE_SIZE', '10485760')),  # 10MB
    'allowed_image_types': ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    'allowed_document_types': ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'],
}

# Security Configuration
SECURITY_CONFIG = {
    'password_min_length': int(os.getenv('PASSWORD_MIN_LENGTH', '8')),
    'session_timeout': int(os.getenv('SESSION_TIMEOUT', '3600')),  # 1 hour
    'max_login_attempts': int(os.getenv('MAX_LOGIN_ATTEMPTS', '5')),
}

# Cache Configuration
CACHE_CONFIG = {
    'default_timeout': int(os.getenv('CACHE_TIMEOUT', '300')),  # 5 minutes
    'organization_cache_timeout': int(os.getenv('ORG_CACHE_TIMEOUT', '3600')),  # 1 hour
}

# Error Messages
ERROR_MESSAGES = {
    'INVALID_CREDENTIALS': 'Invalid username or password.',
    'ACCOUNT_DISABLED': 'Your account has been disabled.',
    'PERMISSION_DENIED': 'You do not have permission to perform this action.',
    'RESOURCE_NOT_FOUND': 'The requested resource was not found.',
    'VALIDATION_ERROR': 'Please check your input and try again.',
    'SERVER_ERROR': 'An internal server error occurred.',
    'ORGANIZATION_NOT_FOUND': 'Organization not found.',
    'OFFICE_NOT_FOUND': 'Office not found.',
}

# Success Messages
SUCCESS_MESSAGES = {
    'LOGIN_SUCCESS': 'Successfully logged in.',
    'LOGOUT_SUCCESS': 'Successfully logged out.',
    'DATA_CREATED': 'Data created successfully.',
    'DATA_UPDATED': 'Data updated successfully.',
    'DATA_DELETED': 'Data deleted successfully.',
    'ORGANIZATION_SET_MAIN': 'Organization set as main successfully.',
    'OFFICE_SET_MAIN': 'Office set as main successfully.',
}

# Validation Rules
VALIDATION_RULES = {
    'organization_name_max_length': 200,
    'organization_shortcode_max_length': 10,
    'phone_max_length': 20,
    'email_max_length': 254,
    'url_max_length': 500,
    'address_max_length': 500,
    'city_max_length': 100,
    'country_max_length': 100,
}

# Default User Groups and Permissions
DEFAULT_GROUPS = {
    'Admin': [
        'add_organization', 'change_organization', 'delete_organization', 'view_organization',
        'add_office', 'change_office', 'delete_office', 'view_office',
        'add_organizationtype', 'change_organizationtype', 'delete_organizationtype', 'view_organizationtype',
    ],
    'Manager': [
        'view_organization', 'change_organization',
        'add_office', 'change_office', 'view_office',
        'view_organizationtype',
    ],
    'User': [
        'view_organization',
        'view_office',
        'view_organizationtype',
    ],
}

# Logging Configuration
LOGGING_CONFIG = {
    'log_level': os.getenv('LOG_LEVEL', 'INFO'),
    'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'max_log_file_size': int(os.getenv('MAX_LOG_FILE_SIZE', '10485760')),  # 10MB
    'backup_count': int(os.getenv('LOG_BACKUP_COUNT', '5')),
}
