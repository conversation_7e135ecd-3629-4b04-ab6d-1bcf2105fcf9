"""
Item Size Model for Inventory Setup
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from .base import TimeStampedModel


class ItemSize(TimeStampedModel):
    """
    Item Size model for defining sizes of inventory items.
    Examples: Small, Medium, Large, XL, XXL, A4, Letter, etc.
    """
    
    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Size Name'),
        help_text=_('Name of the size')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description'),
        help_text=_('Detailed description of the size')
    )

    class Meta:
        db_table = 'inventory_item_size'
        verbose_name = _('Item Size')
        verbose_name_plural = _('Item Sizes')
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.name

    def clean(self):
        """Custom validation"""
        super().clean()
        if self.name:
            self.name = self.name.strip()

    def save(self, *args, **kwargs):
        """Override save to clean data"""
        if self.name:
            self.name = self.name.strip()
        super().save(*args, **kwargs)

    @property
    def usage_count(self):
        """Get count of how many items use this size"""
        # This would be implemented when item models are created
        # For now, return 0
        return 0
