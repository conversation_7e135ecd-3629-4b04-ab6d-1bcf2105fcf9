"""
Item Status Views
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from ..models import ItemStatus
from ..serializers import (
    ItemStatusSerializer,
    ItemStatusListSerializer,
    ItemStatusDropdownSerializer,
    ItemStatusCreateSerializer
)


class ItemStatusViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Item Statuses
    
    Provides CRUD operations for item statuses with filtering,
    searching, and dropdown endpoints.
    """
    queryset = ItemStatus.objects.all()
    serializer_class = ItemStatusSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'status_type', 'allows_checkout', 'requires_approval']
    search_fields = ['code', 'name', 'description']
    ordering_fields = ['name', 'code', 'created_at', 'updated_at']
    ordering = ['name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ItemStatusListSerializer
        elif self.action == 'dropdown':
            return ItemStatusDropdownSerializer
        elif self.action == 'create':
            return ItemStatusCreateSerializer
        return ItemStatusSerializer

    def get_queryset(self):
        """Filter queryset based on action"""
        queryset = ItemStatus.objects.all()
        
        if self.action == 'dropdown':
            # Only return active statuses for dropdown
            queryset = queryset.filter(is_active=True)
        
        return queryset

    @swagger_auto_schema(
        method='get',
        operation_description="Get item statuses for dropdown/select options",
        responses={
            200: openapi.Response(
                description="List of item statuses for dropdown",
                examples={
                    "application/json": [
                        {
                            "value": "uuid-here",
                            "label": "Available (AVAILABLE)",
                            "color_code": "#4CAF50",
                            "icon": "CheckCircle"
                        }
                    ]
                }
            )
        }
    )
    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get item statuses formatted for dropdown/select options"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='post',
        operation_description="Set a status as the default status",
        responses={200: openapi.Response(description="Default status set successfully")}
    )
    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """Set this status as the default status"""
        item_status = self.get_object()
        
        # Remove default from all other statuses
        ItemStatus.objects.filter(is_default=True).update(is_default=False)
        
        # Set this status as default
        item_status.is_default = True
        item_status.save()
        
        return Response({
            'message': f'"{item_status.name}" is now the default status',
            'default_status': ItemStatusSerializer(item_status).data
        })

    @swagger_auto_schema(
        method='post',
        operation_description="Create default item statuses",
        responses={200: openapi.Response(description="Default statuses created successfully")}
    )
    @action(detail=False, methods=['post'])
    def create_defaults(self, request):
        """Create default item statuses"""
        ItemStatus.create_default_statuses()
        
        statuses = ItemStatus.objects.all()
        serializer = ItemStatusListSerializer(statuses, many=True)
        
        return Response({
            'message': 'Default item statuses created successfully',
            'statuses': serializer.data
        })

    @swagger_auto_schema(
        method='post',
        operation_description="Bulk activate/deactivate item statuses",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'ids': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description='List of item status IDs'
                ),
                'is_active': openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    description='Active status to set'
                )
            },
            required=['ids', 'is_active']
        )
    )
    @action(detail=False, methods=['post'])
    def bulk_update_status(self, request):
        """Bulk update active status of item statuses"""
        ids = request.data.get('ids', [])
        is_active = request.data.get('is_active')
        
        if not ids:
            return Response(
                {'error': 'No IDs provided'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if is_active is None:
            return Response(
                {'error': 'is_active field is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        updated_count = ItemStatus.objects.filter(
            id__in=ids
        ).update(is_active=is_active)
        
        return Response({
            'message': f'Updated {updated_count} item statuses',
            'updated_count': updated_count
        })

    def retrieve(self, request, *args, **kwargs):
        """Get detailed item status information"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        """Update item status with validation"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        """Partial update item status"""
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        """Soft delete item status"""
        instance = self.get_object()

        # Check if this is the default status
        if instance.is_default:
            return Response(
                {'error': 'Cannot delete the default status'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check usage count (when item model is implemented)
        if instance.usage_count > 0:
            return Response(
                {'error': f'Cannot delete status that is used by {instance.usage_count} items'},
                status=status.HTTP_400_BAD_REQUEST
            )

        instance.soft_delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
