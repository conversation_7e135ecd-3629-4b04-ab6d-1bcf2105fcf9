"""
Item Size Serializers
"""
from rest_framework import serializers
from ..models import ItemSize


class ItemSizeSerializer(serializers.ModelSerializer):
    """Serializer for ItemSize model"""
    
    usage_count = serializers.ReadOnlyField()
    
    class Meta:
        model = ItemSize
        fields = [
            'id',
            'name',
            'description',
            'is_active',
            'usage_count',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_name(self, value):
        """Validate name"""
        if value:
            value = value.strip()
            if len(value) < 2:
                raise serializers.ValidationError("Name must be at least 2 characters long")
        return value


class ItemSizeListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing item sizes"""
    
    usage_count = serializers.ReadOnlyField()
    
    class Meta:
        model = ItemSize
        fields = [
            'id',
            'name',
            'is_active',
            'usage_count'
        ]


class ItemSizeDropdownSerializer(serializers.ModelSerializer):
    """Minimal serializer for dropdown/select options"""
    
    label = serializers.CharField(source='name')
    value = serializers.CharField(source='id')
    
    class Meta:
        model = ItemSize
        fields = ['value', 'label']
