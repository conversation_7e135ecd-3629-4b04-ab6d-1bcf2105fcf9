"""
Property Status Views
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend

from ..models import PropertyStatus
from ..serializers import (
    PropertyStatusSerializer,
    PropertyStatusListSerializer,
    PropertyStatusDropdownSerializer,
    PropertyStatusCreateSerializer
)


class PropertyStatusViewSet(viewsets.ModelViewSet):
    """ViewSet for managing Property Statuses"""
    queryset = PropertyStatus.objects.all()
    serializer_class = PropertyStatusSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'status_type', 'affects_value', 'requires_inspection']
    search_fields = ['code', 'name', 'description']
    ordering_fields = ['name', 'code', 'created_at', 'updated_at']
    ordering = ['name']

    def get_serializer_class(self):
        if self.action == 'list':
            return PropertyStatusListSerializer
        elif self.action == 'dropdown':
            return PropertyStatusDropdownSerializer
        elif self.action == 'create':
            return PropertyStatusCreateSerializer
        return PropertyStatusSerializer

    def get_queryset(self):
        queryset = PropertyStatus.objects.all()
        if self.action == 'dropdown':
            queryset = queryset.filter(is_active=True)
        return queryset

    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        property_status = self.get_object()
        PropertyStatus.objects.filter(is_default=True).update(is_default=False)
        property_status.is_default = True
        property_status.save()
        return Response({
            'message': f'"{property_status.name}" is now the default property status',
            'default_status': PropertyStatusSerializer(property_status).data
        })

    @action(detail=False, methods=['post'])
    def create_defaults(self, request):
        PropertyStatus.create_default_statuses()
        statuses = PropertyStatus.objects.all()
        serializer = PropertyStatusListSerializer(statuses, many=True)
        return Response({
            'message': 'Default property statuses created successfully',
            'statuses': serializer.data
        })

    @action(detail=False, methods=['post'])
    def bulk_update_status(self, request):
        ids = request.data.get('ids', [])
        is_active = request.data.get('is_active')
        
        if not ids:
            return Response({'error': 'No IDs provided'}, status=status.HTTP_400_BAD_REQUEST)
        if is_active is None:
            return Response({'error': 'is_active field is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        updated_count = PropertyStatus.objects.filter(id__in=ids).update(is_active=is_active)
        return Response({
            'message': f'Updated {updated_count} property statuses',
            'updated_count': updated_count
        })

    def retrieve(self, request, *args, **kwargs):
        """Get detailed property status information"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        """Update property status with validation"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        """Partial update property status"""
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()

        # Check if this is the default status
        if instance.is_default:
            return Response(
                {'error': 'Cannot delete the default status'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check usage count (when item model is implemented)
        if instance.usage_count > 0:
            return Response(
                {'error': f'Cannot delete status that is used by {instance.usage_count} items'},
                status=status.HTTP_400_BAD_REQUEST
            )

        instance.soft_delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
