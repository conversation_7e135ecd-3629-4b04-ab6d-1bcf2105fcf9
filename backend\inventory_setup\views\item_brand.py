"""
Item Brand Views
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend

from ..models import ItemBrand
from ..serializers import (
    ItemBrandSerializer,
    ItemBrandListSerializer,
    ItemBrandDropdownSerializer
)


class ItemBrandViewSet(viewsets.ModelViewSet):
    """ViewSet for managing Item Brands"""
    queryset = ItemBrand.objects.all()
    serializer_class = ItemBrandSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at', 'updated_at']
    ordering = ['name']

    def get_serializer_class(self):
        if self.action == 'list':
            return ItemBrandListSerializer
        elif self.action == 'dropdown':
            return ItemBrandDropdownSerializer
        return ItemBrandSerializer

    def get_queryset(self):
        queryset = ItemBrand.objects.all()
        if self.action == 'dropdown':
            queryset = queryset.filter(is_active=True)
        return queryset

    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def bulk_update_status(self, request):
        ids = request.data.get('ids', [])
        is_active = request.data.get('is_active')
        
        if not ids:
            return Response({'error': 'No IDs provided'}, status=status.HTTP_400_BAD_REQUEST)
        if is_active is None:
            return Response({'error': 'is_active field is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        updated_count = ItemBrand.objects.filter(id__in=ids).update(is_active=is_active)
        return Response({
            'message': f'Updated {updated_count} item brands',
            'updated_count': updated_count
        })

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.soft_delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
