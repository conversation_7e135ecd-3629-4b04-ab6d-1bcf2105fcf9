import { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Breadcrumbs,
  Link,
  Alert,
  Button,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import {
  Home as HomeIcon,
  Storage as StorageIcon,
  ArrowBack as ArrowBackIcon,
  Construction as ConstructionIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate, useParams } from 'react-router-dom';
import StoreTypesList from './StoreTypesList';
import StoresList from './StoresList';

const StoragePage = () => {
  const navigate = useNavigate();
  const { type } = useParams(); // store-types, stores, shelves, layout

  // Route to specific components based on type
  if (type === 'store-types') {
    return <StoreTypesList />;
  }

  if (type === 'stores') {
    return <StoresList />;
  }

  const getPageInfo = () => {
    switch (type) {
      case 'shelves':
        return {
          title: 'Shelves',
          description: 'Organize and manage storage shelves within stores',
          breadcrumb: 'Shelves'
        };
      case 'layout':
        return {
          title: 'Storage Layout',
          description: 'Visualize and manage storage facility layouts',
          breadcrumb: 'Storage Layout'
        };
      default:
        return {
          title: 'Storage Management',
          description: 'Storage management functionality',
          breadcrumb: 'Storage'
        };
    }
  };

  const pageInfo = getPageInfo();

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Link
          component={RouterLink}
          to="/storage-menu"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <StorageIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Storage Management
        </Link>
        <Typography color="text.primary">
          {pageInfo.breadcrumb}
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/storage-menu')}
          sx={{ mb: 2 }}
        >
          Back to Storage Management
        </Button>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          {pageInfo.title}
        </Typography>
        <Typography variant="h6" color="text.secondary">
          {pageInfo.description}
        </Typography>
      </Box>

      {/* Under Construction Notice */}
      <Alert severity="info" sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          🚧 Under Development
        </Typography>
        <Typography variant="body2">
          This {pageInfo.title.toLowerCase()} management feature is currently under development. 
          It will include comprehensive functionality for managing {pageInfo.title.toLowerCase()} 
          with full CRUD operations, search, filtering, and reporting capabilities.
        </Typography>
      </Alert>

      {/* Placeholder Content */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <ConstructionIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              {pageInfo.title} Management
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              This page will provide comprehensive management capabilities for {pageInfo.title.toLowerCase()}.
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Features coming soon:
            </Typography>
            <Box component="ul" sx={{ textAlign: 'left', mt: 2, maxWidth: 400, mx: 'auto' }}>
              <li>Create and edit {pageInfo.title.toLowerCase()}</li>
              <li>Advanced search and filtering</li>
              <li>Bulk operations</li>
              <li>Import/Export functionality</li>
              <li>Detailed reporting</li>
              <li>Real-time updates</li>
            </Box>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button variant="outlined" disabled>
                  Add New {pageInfo.title.slice(0, -1)}
                </Button>
                <Button variant="outlined" disabled>
                  Import Data
                </Button>
                <Button variant="outlined" disabled>
                  Export Report
                </Button>
                <Button variant="outlined" disabled>
                  View Analytics
                </Button>
              </Box>
            </CardContent>
          </Card>
          
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Statistics
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Detailed statistics and metrics will be displayed here once the feature is implemented.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default StoragePage;
