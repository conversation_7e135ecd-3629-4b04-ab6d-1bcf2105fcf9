from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver

class UserProfile(models.Model):
    """
    Extended user profile with additional information
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')

    # User's assigned office
    assigned_office = models.ForeignKey(
        'organization.Office',
        on_delete=models.SET_NULL,
        related_name='assigned_users',
        null=True,
        blank=True
    )

    # Additional user information
    phone_number = models.CharField(max_length=20, blank=True)
    position = models.CharField(max_length=100, blank=True)

    def __str__(self):
        return f"{self.user.username}'s Profile"

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """Create a UserProfile for every new User"""
    if created:
        # Check if a profile already exists for this user
        if not hasattr(instance, 'profile') or instance.profile is None:
            UserProfile.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """Save the UserProfile when the User is saved"""
    try:
        # Check if the profile exists before saving
        if hasattr(instance, 'profile') and instance.profile is not None:
            instance.profile.save()
    except Exception as e:
        print(f"Error saving user profile: {e}")
