# Generated by Django 5.2.1 on 2025-06-09 04:23

import django.core.validators
import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ApprovalStatus',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for the approval status', primary_key=True, serialize=False)),
                ('code', models.CharField(blank=True, help_text="Unique code for the status (e.g., 'PENDING', 'APPROVED', 'REJECTED')", max_length=20, null=True, unique=True, validators=[django.core.validators.RegexValidator(message='Code must contain only uppercase letters, numbers, underscores, and hyphens', regex='^[A-Z0-9_-]+$')], verbose_name='Status Code')),
                ('name', models.Char<PERSON>ield(help_text='Human-readable name for the approval status', max_length=100, unique=True, verbose_name='Status Name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of what this approval status means', verbose_name='Description')),
                ('status_type', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled'), ('on_hold', 'On Hold')], default='pending', help_text='Category of this approval status', max_length=20, verbose_name='Status Type')),
                ('color_code', models.CharField(choices=[('#4CAF50', 'Green - Approved/Success'), ('#F44336', 'Red - Rejected/Failed'), ('#FF9800', 'Orange - Pending/Warning'), ('#2196F3', 'Blue - In Review'), ('#9C27B0', 'Purple - On Hold'), ('#607D8B', 'Gray - Cancelled/Inactive'), ('#795548', 'Brown - Archived'), ('#E91E63', 'Pink - Special Status')], default='#FF9800', help_text='Hex color code for displaying this status', max_length=7, validators=[django.core.validators.RegexValidator(message='Color code must be a valid hex color (e.g., #FF0000)', regex='^#[0-9A-Fa-f]{6}$')], verbose_name='Color Code')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this status is available for use', verbose_name='Active')),
                ('is_default', models.BooleanField(default=False, help_text='Whether this is the default approval status', verbose_name='Default Status')),
                ('is_final', models.BooleanField(default=False, help_text='Whether this status represents a final decision', verbose_name='Final Status')),
                ('allows_modification', models.BooleanField(default=True, help_text='Whether items with this status can be modified', verbose_name='Allows Modification')),
                ('requires_comment', models.BooleanField(default=False, help_text='Whether setting this status requires a comment', verbose_name='Requires Comment')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Approval Status',
                'verbose_name_plural': 'Approval Statuses',
                'db_table': 'inventory_approval_status',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['code'], name='inv_appr_status_code_idx'), models.Index(fields=['is_active'], name='inv_appr_status_active_idx'), models.Index(fields=['status_type'], name='inv_appr_status_type_idx')],
            },
        ),
        migrations.CreateModel(
            name='EntryMode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this record should be treated as active', verbose_name='Active')),
                ('name', models.CharField(help_text='Name of the inventory entry mode', max_length=50, unique=True, verbose_name='Entry Mode Name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the entry mode', null=True, verbose_name='Description')),
                ('code', models.CharField(blank=True, help_text='Short code for the entry mode (auto-generated if not provided)', max_length=10, unique=True, verbose_name='Entry Mode Code')),
                ('is_default', models.BooleanField(default=False, help_text='Designates if this is the default entry mode', verbose_name='Default Entry Mode')),
                ('requires_approval', models.BooleanField(default=False, help_text='Designates if entries using this mode require approval', verbose_name='Requires Approval')),
                ('allows_negative_stock', models.BooleanField(default=False, help_text='Designates if this entry mode allows negative stock levels', verbose_name='Allows Negative Stock')),
            ],
            options={
                'verbose_name': 'Entry Mode',
                'verbose_name_plural': 'Entry Modes',
                'db_table': 'inventory_entry_mode',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='inventory_e_name_30c08b_idx'), models.Index(fields=['code'], name='inventory_e_code_d99f37_idx'), models.Index(fields=['is_active'], name='inventory_e_is_acti_dad746_idx'), models.Index(fields=['is_default'], name='inventory_e_is_defa_2c8a9a_idx')],
            },
        ),
        migrations.CreateModel(
            name='ItemBrand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this record should be treated as active', verbose_name='Active')),
                ('name', models.CharField(help_text='Name of the brand', max_length=100, unique=True, verbose_name='Brand Name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the brand', null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Item Brand',
                'verbose_name_plural': 'Item Brands',
                'db_table': 'inventory_item_brand',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='inventory_i_name_7999bf_idx'), models.Index(fields=['is_active'], name='inventory_i_is_acti_58952f_idx')],
            },
        ),
        migrations.CreateModel(
            name='ItemCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this record should be treated as active', verbose_name='Active')),
                ('name', models.CharField(help_text='Name of the item category', max_length=100, unique=True, verbose_name='Category Name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the item category', null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Item Category',
                'verbose_name_plural': 'Item Categories',
                'db_table': 'inventory_item_category',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='inventory_i_name_6f8df7_idx'), models.Index(fields=['is_active'], name='inventory_i_is_acti_7de472_idx')],
            },
        ),
        migrations.CreateModel(
            name='ItemManufacturer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this record should be treated as active', verbose_name='Active')),
                ('name', models.CharField(help_text='Name of the manufacturer', max_length=255, unique=True, verbose_name='Manufacturer Name')),
                ('country', models.CharField(help_text='Country where the manufacturer is based', max_length=100, verbose_name='Country')),
                ('website', models.URLField(blank=True, help_text='Official website of the manufacturer', null=True, validators=[django.core.validators.URLValidator()], verbose_name='Website')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the manufacturer', null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Item Manufacturer',
                'verbose_name_plural': 'Item Manufacturers',
                'db_table': 'inventory_item_manufacturer',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='inventory_i_name_1d8cda_idx'), models.Index(fields=['country'], name='inventory_i_country_2f95be_idx'), models.Index(fields=['is_active'], name='inventory_i_is_acti_92960f_idx')],
            },
        ),
        migrations.CreateModel(
            name='ItemQuality',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this record should be treated as active', verbose_name='Active')),
                ('name', models.CharField(help_text='Name of the quality level', max_length=50, unique=True, verbose_name='Quality Name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the quality level', null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Item Quality',
                'verbose_name_plural': 'Item Qualities',
                'db_table': 'inventory_item_quality',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='inventory_i_name_497883_idx'), models.Index(fields=['is_active'], name='inventory_i_is_acti_bdb4a7_idx')],
            },
        ),
        migrations.CreateModel(
            name='ItemShape',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this record should be treated as active', verbose_name='Active')),
                ('name', models.CharField(help_text='Name of the shape', max_length=50, unique=True, verbose_name='Shape Name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the shape', null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Item Shape',
                'verbose_name_plural': 'Item Shapes',
                'db_table': 'inventory_item_shape',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='inventory_i_name_3b0fe4_idx'), models.Index(fields=['is_active'], name='inventory_i_is_acti_fa63a0_idx')],
            },
        ),
        migrations.CreateModel(
            name='ItemSize',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this record should be treated as active', verbose_name='Active')),
                ('name', models.CharField(help_text='Name of the size', max_length=50, unique=True, verbose_name='Size Name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the size', null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Item Size',
                'verbose_name_plural': 'Item Sizes',
                'db_table': 'inventory_item_size',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='inventory_i_name_c53992_idx'), models.Index(fields=['is_active'], name='inventory_i_is_acti_b90b28_idx')],
            },
        ),
        migrations.CreateModel(
            name='ItemStatus',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for the item status', primary_key=True, serialize=False)),
                ('code', models.CharField(help_text="Unique code for the status (e.g., 'AVAILABLE', 'IN_USE', 'REPAIR')", max_length=20, unique=True, validators=[django.core.validators.RegexValidator(message='Code must contain only uppercase letters, numbers, underscores, and hyphens', regex='^[A-Z0-9_-]+$')], verbose_name='Status Code')),
                ('name', models.CharField(help_text='Human-readable name for the status', max_length=100, unique=True, verbose_name='Status Name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of what this status means', verbose_name='Description')),
                ('status_type', models.CharField(choices=[('operational', 'Operational'), ('maintenance', 'Maintenance'), ('disposal', 'Disposal'), ('administrative', 'Administrative')], default='operational', help_text='Category of this status', max_length=20, verbose_name='Status Type')),
                ('color_code', models.CharField(choices=[('#4CAF50', 'Green - Available/Good'), ('#2196F3', 'Blue - In Use/Active'), ('#FF9800', 'Orange - Warning/Maintenance'), ('#F44336', 'Red - Critical/Unavailable'), ('#9C27B0', 'Purple - Special Status'), ('#607D8B', 'Gray - Inactive/Retired'), ('#795548', 'Brown - Storage/Archive'), ('#E91E63', 'Pink - Reserved/Allocated')], default='#4CAF50', help_text='Hex color code for displaying this status', max_length=7, validators=[django.core.validators.RegexValidator(message='Color code must be a valid hex color (e.g., #FF0000)', regex='^#[0-9A-Fa-f]{6}$')], verbose_name='Color Code')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this status is available for use', verbose_name='Active')),
                ('is_default', models.BooleanField(default=False, help_text='Whether this is the default status for new items', verbose_name='Default Status')),
                ('allows_checkout', models.BooleanField(default=True, help_text='Whether items with this status can be checked out', verbose_name='Allows Checkout')),
                ('requires_approval', models.BooleanField(default=False, help_text='Whether changing to this status requires approval', verbose_name='Requires Approval')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Item Status',
                'verbose_name_plural': 'Item Statuses',
                'db_table': 'inventory_item_status',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['code'], name='inv_item_status_code_idx'), models.Index(fields=['is_active'], name='inv_item_status_active_idx'), models.Index(fields=['status_type'], name='inv_item_status_type_idx')],
            },
        ),
        migrations.CreateModel(
            name='ItemTag',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for the item tag', primary_key=True, serialize=False)),
                ('name', models.CharField(help_text="Name of the tag (e.g., 'Sensitive', 'Very Small', 'Building Material')", max_length=100, unique=True, verbose_name='Tag Name')),
                ('slug', models.SlugField(help_text='URL-friendly version of the tag name', max_length=100, unique=True, verbose_name='Slug')),
                ('tag_type', models.CharField(choices=[('sensitivity', 'Sensitivity Level'), ('size', 'Size Category'), ('material', 'Material Type'), ('disposal', 'Disposal Method'), ('handling', 'Handling Requirements'), ('storage', 'Storage Requirements'), ('security', 'Security Level'), ('environmental', 'Environmental Impact'), ('maintenance', 'Maintenance Category'), ('quality', 'Quality Indicator'), ('location', 'Location Specific'), ('other', 'Other')], default='other', help_text='Category of this tag', max_length=20, verbose_name='Tag Type')),
                ('description', models.TextField(blank=True, help_text='Detailed description of what this tag represents', verbose_name='Description')),
                ('color_code', models.CharField(choices=[('#F44336', 'Red - Critical/High Priority'), ('#FF9800', 'Orange - Warning/Medium Priority'), ('#FFEB3B', 'Yellow - Caution/Low Priority'), ('#4CAF50', 'Green - Safe/Good'), ('#2196F3', 'Blue - Information/Standard'), ('#9C27B0', 'Purple - Special/Premium'), ('#E91E63', 'Pink - Attention/Featured'), ('#795548', 'Brown - Material/Physical'), ('#607D8B', 'Gray - Neutral/Inactive'), ('#424242', 'Black - Critical/Restricted')], default='#2196F3', help_text='Hex color code for displaying this tag', max_length=7, validators=[django.core.validators.RegexValidator(message='Color code must be a valid hex color (e.g., #FF0000)', regex='^#[0-9A-Fa-f]{6}$')], verbose_name='Color Code')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this tag is available for use', verbose_name='Active')),
                ('is_system_tag', models.BooleanField(default=False, help_text='Whether this is a system-defined tag that cannot be deleted', verbose_name='System Tag')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Item Tag',
                'verbose_name_plural': 'Item Tags',
                'db_table': 'inventory_item_tag',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['slug'], name='inv_item_tag_slug_idx'), models.Index(fields=['tag_type'], name='inv_item_tag_type_idx'), models.Index(fields=['is_active'], name='inv_item_tag_active_idx')],
            },
        ),
        migrations.CreateModel(
            name='ItemType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this record should be treated as active', verbose_name='Active')),
                ('name', models.CharField(help_text='Name of the item type', max_length=100, unique=True, verbose_name='Type Name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the item type', null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Item Type',
                'verbose_name_plural': 'Item Types',
                'db_table': 'inventory_item_type',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='inventory_i_name_e8c487_idx'), models.Index(fields=['is_active'], name='inventory_i_is_acti_b515a8_idx')],
            },
        ),
        migrations.CreateModel(
            name='MainClassification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this record should be treated as active', verbose_name='Active')),
                ('code', models.CharField(help_text='4-character unique code for the main classification', max_length=4, unique=True, validators=[django.core.validators.RegexValidator(message='Code must be exactly 4 characters (letters and numbers only)', regex='^[A-Z0-9]{4}$')], verbose_name='Classification Code')),
                ('name', models.CharField(help_text='Descriptive name for the main classification', max_length=100, verbose_name='Classification Name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the main classification', null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Main Classification',
                'verbose_name_plural': 'Main Classifications',
                'db_table': 'inventory_main_classification',
                'ordering': ['code'],
                'indexes': [models.Index(fields=['code'], name='inventory_m_code_d2edb0_idx'), models.Index(fields=['name'], name='inventory_m_name_c0b25a_idx'), models.Index(fields=['is_active'], name='inventory_m_is_acti_99b691_idx')],
            },
        ),
        migrations.CreateModel(
            name='PropertyStatus',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for the property status', primary_key=True, serialize=False)),
                ('code', models.CharField(blank=True, help_text="Unique code for the status (e.g., 'SERVICEABLE', 'UNSERVICEABLE')", max_length=20, null=True, unique=True, validators=[django.core.validators.RegexValidator(message='Code must contain only uppercase letters, numbers, underscores, and hyphens', regex='^[A-Z0-9_-]+$')], verbose_name='Status Code')),
                ('name', models.CharField(help_text='Human-readable name for the property status', max_length=100, unique=True, verbose_name='Status Name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of what this property status means', verbose_name='Description')),
                ('status_type', models.CharField(choices=[('serviceable', 'Serviceable'), ('unserviceable', 'Unserviceable'), ('condemned', 'Condemned'), ('pending', 'Pending Review')], default='serviceable', help_text='Category of this property status', max_length=20, verbose_name='Status Type')),
                ('color_code', models.CharField(choices=[('#4CAF50', 'Green - Serviceable/Good'), ('#FF9800', 'Orange - Needs Attention'), ('#F44336', 'Red - Unserviceable/Critical'), ('#9C27B0', 'Purple - Condemned'), ('#607D8B', 'Gray - Pending/Unknown'), ('#2196F3', 'Blue - Under Review'), ('#795548', 'Brown - Archived'), ('#E91E63', 'Pink - Special Status')], default='#4CAF50', help_text='Hex color code for displaying this status', max_length=7, validators=[django.core.validators.RegexValidator(message='Color code must be a valid hex color (e.g., #FF0000)', regex='^#[0-9A-Fa-f]{6}$')], verbose_name='Color Code')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this status is available for use', verbose_name='Active')),
                ('is_default', models.BooleanField(default=False, help_text='Whether this is the default property status', verbose_name='Default Status')),
                ('affects_value', models.BooleanField(default=False, help_text="Whether this status affects the item's value", verbose_name='Affects Value')),
                ('requires_inspection', models.BooleanField(default=False, help_text='Whether items with this status require regular inspection', verbose_name='Requires Inspection')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Property Status',
                'verbose_name_plural': 'Property Statuses',
                'db_table': 'inventory_property_status',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['code'], name='inv_prop_status_code_idx'), models.Index(fields=['is_active'], name='inv_prop_status_active_idx'), models.Index(fields=['status_type'], name='inv_prop_status_type_idx')],
            },
        ),
        migrations.CreateModel(
            name='UnitOfMeasure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this record should be treated as active', verbose_name='Active')),
                ('name', models.CharField(help_text='Full name of the unit of measure', max_length=50, unique=True, verbose_name='Unit Name')),
                ('symbol', models.CharField(help_text='Short symbol for the unit (e.g., kg, m, L)', max_length=10, unique=True, verbose_name='Symbol')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the unit of measure', null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name': 'Unit of Measure',
                'verbose_name_plural': 'Units of Measure',
                'db_table': 'inventory_unit_of_measure',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='inventory_u_name_7ae1c5_idx'), models.Index(fields=['symbol'], name='inventory_u_symbol_bec20d_idx'), models.Index(fields=['is_active'], name='inventory_u_is_acti_082615_idx')],
            },
        ),
        migrations.CreateModel(
            name='SubClassification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created', verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated', verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this record should be treated as active', verbose_name='Active')),
                ('code', models.CharField(blank=True, help_text='3-digit sequential code (auto-generated if not provided)', max_length=3, validators=[django.core.validators.RegexValidator(message='Code must be exactly 3 digits', regex='^[0-9]{3}$')], verbose_name='Sub Classification Code')),
                ('name', models.CharField(help_text='Descriptive name for the sub classification', max_length=100, verbose_name='Sub Classification Name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the sub classification', null=True, verbose_name='Description')),
                ('main_class', models.ForeignKey(help_text='Parent main classification', on_delete=django.db.models.deletion.CASCADE, related_name='sub_classifications', to='inventory_setup.mainclassification', verbose_name='Main Classification')),
            ],
            options={
                'verbose_name': 'Sub Classification',
                'verbose_name_plural': 'Sub Classifications',
                'db_table': 'inventory_sub_classification',
                'ordering': ['main_class__code', 'code'],
                'indexes': [models.Index(fields=['main_class', 'code'], name='inventory_s_main_cl_fd5496_idx'), models.Index(fields=['name'], name='inventory_s_name_1c5fb5_idx'), models.Index(fields=['is_active'], name='inventory_s_is_acti_57ed95_idx')],
                'unique_together': {('main_class', 'code')},
            },
        ),
    ]
