"""
Item Quality Model for Inventory Setup
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from .base import TimeStampedModel


class ItemQuality(TimeStampedModel):
    """
    Item Quality model for defining quality levels of inventory items.
    Examples: Excellent, Good, Fair, Poor, New, Used, Refurbished, etc.
    """
    
    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Quality Name'),
        help_text=_('Name of the quality level')
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description'),
        help_text=_('Detailed description of the quality level')
    )

    class Meta:
        db_table = 'inventory_item_quality'
        verbose_name = _('Item Quality')
        verbose_name_plural = _('Item Qualities')
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.name

    def clean(self):
        """Custom validation"""
        super().clean()
        if self.name:
            self.name = self.name.strip()

    def save(self, *args, **kwargs):
        """Override save to clean data"""
        if self.name:
            self.name = self.name.strip()
        super().save(*args, **kwargs)

    @property
    def usage_count(self):
        """Get count of how many items use this quality level"""
        # This would be implemented when item models are created
        # For now, return 0
        return 0
