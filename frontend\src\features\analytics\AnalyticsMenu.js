import { useState } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  Paper,
  Chip,
  useTheme,
  alpha,
  Breadcrumbs,
  Link,
  Alert
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  Assessment as ReportsIcon,
  TrendingUp as TrendsIcon,
  Dashboard as DashboardIcon,
  Insights as InsightsIcon,
  Home as HomeIcon,
  BarChart as ChartIcon,
  <PERSON>Chart as PieChartIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';

const AnalyticsMenu = () => {
  const theme = useTheme();

  // Analytics management cards data
  const analyticsCards = [
    {
      id: 'inventory-reports',
      title: 'Inventory Reports',
      description: 'Generate comprehensive inventory and stock reports',
      icon: <ReportsIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.primary.main,
      path: '/analytics/inventory-reports',
      count: '12+',
      adminOnly: false
    },
    {
      id: 'financial-analytics',
      title: 'Financial Analytics',
      description: 'Analyze costs, valuations, and financial metrics',
      icon: <TrendsIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.secondary.main,
      path: '/analytics/financial',
      count: '8+',
      adminOnly: false
    },
    {
      id: 'performance-dashboard',
      title: 'Performance Dashboard',
      description: 'Real-time performance metrics and KPIs',
      icon: <DashboardIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.info.main,
      path: '/analytics/performance',
      count: 'Live',
      adminOnly: false
    },
    {
      id: 'trend-analysis',
      title: 'Trend Analysis',
      description: 'Historical trends and predictive analytics',
      icon: <TimelineIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.success.main,
      path: '/analytics/trends',
      count: 'New',
      adminOnly: false
    },
    {
      id: 'custom-reports',
      title: 'Custom Reports',
      description: 'Build and schedule custom reports',
      icon: <ChartIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.warning.main,
      path: '/analytics/custom-reports',
      count: '5+',
      adminOnly: false
    },
    {
      id: 'data-visualization',
      title: 'Data Visualization',
      description: 'Interactive charts and visual analytics',
      icon: <PieChartIcon sx={{ fontSize: 40 }} />,
      color: theme.palette.error.main,
      path: '/analytics/visualization',
      count: 'Beta',
      adminOnly: false
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <AnalyticsIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Analytics & Reports
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          Analytics & Reports
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Generate comprehensive reports and analytics across all modules
        </Typography>
      </Box>

      {/* Info Paper */}
      <Paper
        sx={{
          p: 2,
          mb: 4,
          display: 'flex',
          alignItems: 'center',
          bgcolor: alpha(theme.palette.error.main, 0.1),
          border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`,
          borderRadius: 2
        }}
      >
        <InsightsIcon color="error" sx={{ mr: 2 }} />
        <Box>
          <Typography variant="h6" color="error.main">
            Analytics & Reporting Center
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Access comprehensive analytics, generate reports, and gain insights from your inventory data
          </Typography>
        </Box>
      </Paper>

      {/* Under Development Notice */}
      <Alert severity="info" sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          🚧 Analytics Platform Under Development
        </Typography>
        <Typography variant="body2">
          Our comprehensive analytics and reporting platform is currently under development. 
          It will provide powerful insights, customizable reports, and real-time dashboards 
          for all aspects of your inventory management system.
        </Typography>
      </Alert>

      {/* Analytics Management Cards */}
      <Grid container spacing={3}>
        {analyticsCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0,0,0,0.1)',
                },
                borderTop: `4px solid ${card.color}`,
                borderRadius: 2,
                position: 'relative',
                overflow: 'visible',
                cursor: 'not-allowed',
                opacity: 0.7
              }}
            >
              {card.count && (
                <Chip
                  label={card.count}
                  color="primary"
                  size="small"
                  sx={{
                    position: 'absolute',
                    top: -10,
                    right: 16,
                    fontWeight: 'bold',
                    boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
                  }}
                />
              )}
              <CardContent sx={{ flexGrow: 1, p: 3 }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: alpha(card.color, 0.1),
                    mb: 2,
                    mx: 'auto'
                  }}
                >
                  {card.icon}
                </Box>
                <Typography variant="h6" component="h2" gutterBottom textAlign="center" fontWeight="bold">
                  {card.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" textAlign="center">
                  {card.description}
                </Typography>
                <Typography variant="caption" color="warning.main" textAlign="center" display="block" sx={{ mt: 1 }}>
                  Coming Soon
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Features Preview */}
      <Paper sx={{ p: 3, mt: 4, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          Upcoming Features
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="subtitle2" gutterBottom>
              📊 Real-time Dashboards
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Live KPI monitoring and alerts
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="subtitle2" gutterBottom>
              📈 Predictive Analytics
            </Typography>
            <Typography variant="body2" color="text.secondary">
              AI-powered forecasting and insights
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="subtitle2" gutterBottom>
              📋 Custom Reports
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Drag-and-drop report builder
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="subtitle2" gutterBottom>
              🔄 Automated Scheduling
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Scheduled report generation and delivery
            </Typography>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default AnalyticsMenu;
