from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters import rest_framework as filters
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.db.models import Count, Avg, Q
from django.db import IntegrityError
from .models import SupplierType, SupplierCategory, Supplier
from .serializers import (
    SupplierTypeSerializer,
    SupplierTypeDropdownSerializer,
    SupplierCategorySerializer,
    SupplierCategoryDropdownSerializer,
    SupplierSerializer,
    SupplierListSerializer,
    SupplierDropdownSerializer,
    SupplierCreateSerializer,
    SupplierStatsSerializer,
)


class SupplierTypeFilter(filters.FilterSet):
    """Filter for SupplierType"""
    name = filters.CharFilter(lookup_expr='icontains')
    code = filters.CharFilter(lookup_expr='icontains')
    is_active = filters.BooleanFilter()

    class Meta:
        model = SupplierType
        fields = ['name', 'code', 'is_active']


class SupplierTypeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing supplier types
    """
    queryset = SupplierType.objects.all()
    serializer_class = SupplierTypeSerializer
    permission_classes = []  # Temporarily allow unauthenticated access for testing
    filterset_class = SupplierTypeFilter
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'created_at']
    ordering = ['name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action in ['dropdown', 'active_dropdown']:
            return SupplierTypeDropdownSerializer
        return SupplierTypeSerializer

    @swagger_auto_schema(
        method='get',
        operation_description="Get active supplier types for dropdown",
        responses={200: SupplierTypeDropdownSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get active supplier types for dropdown/select options"""
        queryset = self.get_queryset().filter(is_active=True)
        serializer = SupplierTypeDropdownSerializer(queryset, many=True)
        return Response(serializer.data)


class SupplierCategoryFilter(filters.FilterSet):
    """Filter for SupplierCategory"""
    name = filters.CharFilter(lookup_expr='icontains')
    code = filters.CharFilter(lookup_expr='icontains')
    is_active = filters.BooleanFilter()

    class Meta:
        model = SupplierCategory
        fields = ['name', 'code', 'is_active']


class SupplierCategoryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing supplier categories
    """
    queryset = SupplierCategory.objects.all()
    serializer_class = SupplierCategorySerializer
    permission_classes = []  # Temporarily allow unauthenticated access for testing
    filterset_class = SupplierCategoryFilter
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'created_at']
    ordering = ['name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action in ['dropdown', 'active_dropdown']:
            return SupplierCategoryDropdownSerializer
        return SupplierCategorySerializer

    @swagger_auto_schema(
        method='get',
        operation_description="Get active categories for dropdown",
        responses={200: SupplierCategoryDropdownSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get active categories for dropdown/select options"""
        queryset = self.get_queryset().filter(is_active=True)
        serializer = SupplierCategoryDropdownSerializer(queryset, many=True)
        return Response(serializer.data)


class SupplierFilter(filters.FilterSet):
    """Filter for Supplier"""
    supplier_code = filters.CharFilter(lookup_expr='icontains')
    company_name = filters.CharFilter(lookup_expr='icontains')
    # supplier_type = filters.ChoiceFilter(choices=Supplier.SUPPLIER_TYPES)  # Removed - now using ManyToMany
    category = filters.ModelChoiceFilter(queryset=SupplierCategory.objects.all())
    contact_person = filters.CharFilter(lookup_expr='icontains')
    email = filters.CharFilter(lookup_expr='icontains')
    country = filters.CharFilter()
    city = filters.CharFilter(lookup_expr='icontains')
    is_active = filters.BooleanFilter()
    is_preferred = filters.BooleanFilter()
    preferred_currency = filters.ChoiceFilter(choices=Supplier.CURRENCIES)
    payment_terms_min = filters.NumberFilter(field_name='payment_terms', lookup_expr='gte')
    payment_terms_max = filters.NumberFilter(field_name='payment_terms', lookup_expr='lte')
    quality_rating_min = filters.NumberFilter(field_name='quality_rating', lookup_expr='gte')
    quality_rating_max = filters.NumberFilter(field_name='quality_rating', lookup_expr='lte')

    class Meta:
        model = Supplier
        fields = [
            'supplier_code', 'company_name',
            'contact_person', 'email', 'country', 'city', 'is_active', 'is_preferred',
            'preferred_currency'
        ]


class SupplierViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing suppliers
    """
    queryset = Supplier.objects.prefetch_related('supplier_types', 'categories').all()
    serializer_class = SupplierSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_class = SupplierFilter
    search_fields = [
        'supplier_code', 'company_name', 'contact_person', 'email',
        'tin_number', 'city', 'address', 'notes'
    ]
    ordering_fields = [
        'supplier_code', 'company_name', 'contact_person',
        'country', 'city', 'payment_terms', 'quality_rating', 'delivery_rating',
        'service_rating', 'created_at', 'updated_at'
    ]
    ordering = ['company_name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return SupplierListSerializer
        elif self.action in ['dropdown', 'active_dropdown', 'preferred_dropdown']:
            return SupplierDropdownSerializer
        elif self.action == 'stats':
            return SupplierStatsSerializer
        return SupplierSerializer

    def get_queryset(self):
        """Filter queryset based on action"""
        queryset = Supplier.objects.prefetch_related('supplier_types', 'categories').all()

        # For dropdown actions, only return active suppliers
        if self.action in ['dropdown', 'active_dropdown']:
            queryset = queryset.filter(is_active=True)
        elif self.action == 'preferred_dropdown':
            queryset = queryset.filter(is_active=True, is_preferred=True)

        return queryset

    def destroy(self, request, *args, **kwargs):
        """Custom destroy method with better error handling"""
        try:
            instance = self.get_object()
            company_name = instance.company_name
            self.perform_destroy(instance)
            return Response({
                'message': f'Supplier "{company_name}" deleted successfully'
            }, status=status.HTTP_200_OK)
        except IntegrityError as e:
            return Response({
                'error': 'Cannot delete supplier',
                'detail': 'This supplier is referenced by other records and cannot be deleted. Consider deactivating it instead.',
                'suggestion': 'Use the deactivate option to disable this supplier while preserving historical data.'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'error': 'Failed to delete supplier',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        method='get',
        operation_description="Get active suppliers for dropdown",
        responses={200: SupplierDropdownSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get active suppliers for dropdown/select options"""
        queryset = self.get_queryset()
        serializer = SupplierDropdownSerializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        method='get',
        operation_description="Get preferred suppliers for dropdown",
        responses={200: SupplierDropdownSerializer(many=True)}
    )
    @action(detail=False, methods=['get'])
    def preferred_dropdown(self, request):
        """Get preferred suppliers for dropdown/select options"""
        queryset = self.get_queryset()
        serializer = SupplierDropdownSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a supplier"""
        supplier = self.get_object()
        supplier.activate()
        return Response({'message': f'Supplier "{supplier.company_name}" activated successfully'})

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate a supplier"""
        supplier = self.get_object()
        supplier.deactivate()
        return Response({'message': f'Supplier "{supplier.company_name}" deactivated successfully'})

    @action(detail=True, methods=['post'])
    def toggle_preferred(self, request, pk=None):
        """Toggle preferred status of a supplier"""
        supplier = self.get_object()
        supplier.is_preferred = not supplier.is_preferred
        supplier.save()
        status_text = "preferred" if supplier.is_preferred else "not preferred"
        return Response({'message': f'Supplier "{supplier.company_name}" marked as {status_text}'})

    @swagger_auto_schema(
        method='get',
        operation_description="Get supplier statistics",
        responses={
            200: openapi.Response(
                description="Supplier statistics",
                examples={
                    "application/json": {
                        "total_suppliers": 50,
                        "active_suppliers": 45,
                        "preferred_suppliers": 12,
                        "suppliers_by_type": {
                            "MANUFACTURER": 15,
                            "DISTRIBUTOR": 20,
                            "WHOLESALER": 10
                        }
                    }
                }
            )
        }
    )
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get comprehensive supplier statistics"""
        suppliers = Supplier.objects.all()

        # Basic counts
        total_suppliers = suppliers.count()
        active_suppliers = suppliers.filter(is_active=True).count()
        preferred_suppliers = suppliers.filter(is_active=True, is_preferred=True).count()

        # Suppliers by type (now using ManyToMany)
        suppliers_by_type = dict(
            suppliers.filter(supplier_types__isnull=False)
            .values_list('supplier_types__name')
            .annotate(count=Count('supplier_types'))
        )

        # Suppliers by country
        suppliers_by_country = dict(
            suppliers.values_list('country').annotate(count=Count('country'))[:10]
        )

        # Average ratings
        ratings = suppliers.aggregate(
            avg_quality=Avg('quality_rating'),
            avg_delivery=Avg('delivery_rating'),
            avg_service=Avg('service_rating')
        )

        # Suppliers by category (now using ManyToMany)
        suppliers_by_category = dict(
            suppliers.filter(categories__isnull=False)
            .values_list('categories__name')
            .annotate(count=Count('categories'))
        )

        statistics = {
            'total_suppliers': total_suppliers,
            'active_suppliers': active_suppliers,
            'inactive_suppliers': total_suppliers - active_suppliers,
            'preferred_suppliers': preferred_suppliers,
            'suppliers_by_type': suppliers_by_type,
            'suppliers_by_country': suppliers_by_country,
            'suppliers_by_category': suppliers_by_category,
            'average_ratings': {
                'quality': round(ratings['avg_quality'] or 0, 2),
                'delivery': round(ratings['avg_delivery'] or 0, 2),
                'service': round(ratings['avg_service'] or 0, 2),
            }
        }

        return Response(statistics)
