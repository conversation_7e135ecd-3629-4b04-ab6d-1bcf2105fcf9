"""
Item Manufacturer Model for Inventory Setup
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import URLValidator
from .base import TimeStampedModel


class ItemManufacturer(TimeStampedModel):
    """
    Item Manufacturer model for tracking manufacturers of inventory items.
    Examples: Apple Inc., Samsung Electronics, Toyota Motor Corporation, etc.
    """
    
    name = models.CharField(
        max_length=255,
        unique=True,
        verbose_name=_('Manufacturer Name'),
        help_text=_('Name of the manufacturer')
    )
    
    country = models.CharField(
        max_length=100,
        verbose_name=_('Country'),
        help_text=_('Country where the manufacturer is based')
    )
    
    website = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('Website'),
        help_text=_('Official website of the manufacturer'),
        validators=[URLValidator()]
    )
    
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description'),
        help_text=_('Detailed description of the manufacturer')
    )

    class Meta:
        db_table = 'inventory_item_manufacturer'
        verbose_name = _('Item Manufacturer')
        verbose_name_plural = _('Item Manufacturers')
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['country']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.name

    def clean(self):
        """Custom validation"""
        super().clean()
        if self.name:
            self.name = self.name.strip()
        if self.country:
            self.country = self.country.strip()

    def save(self, *args, **kwargs):
        """Override save to clean data"""
        if self.name:
            self.name = self.name.strip()
        if self.country:
            self.country = self.country.strip()
        super().save(*args, **kwargs)

    @property
    def usage_count(self):
        """Get count of how many items use this manufacturer"""
        # This would be implemented when item models are created
        # For now, return 0
        return 0

    @property
    def display_name(self):
        """Get display name with country"""
        return f"{self.name} ({self.country})"
