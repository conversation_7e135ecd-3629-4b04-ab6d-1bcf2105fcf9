"""
Item Status Model for Inventory Setup
"""
import uuid
from django.db import models
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _


class ItemStatus(models.Model):
    """
    Model for item status tracking (Available, In Use, Under Repair, etc.)
    """
    
    # Status type choices
    STATUS_TYPES = [
        ('operational', 'Operational'),
        ('maintenance', 'Maintenance'),
        ('disposal', 'Disposal'),
        ('administrative', 'Administrative'),
    ]
    
    # Predefined color choices
    COLOR_CHOICES = [
        ('#4CAF50', 'Green - Available/Good'),
        ('#2196F3', 'Blue - In Use/Active'),
        ('#FF9800', 'Orange - Warning/Maintenance'),
        ('#F44336', 'Red - Critical/Unavailable'),
        ('#9C27B0', 'Purple - Special Status'),
        ('#607D8B', 'Gray - Inactive/Retired'),
        ('#795548', 'Brown - Storage/Archive'),
        ('#E91E63', 'Pink - Reserved/Allocated'),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text=_("Unique identifier for the item status")
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        validators=[RegexValidator(
            regex=r'^[A-Z0-9_-]+$',
            message=_("Code must contain only uppercase letters, numbers, underscores, and hyphens")
        )],
        verbose_name=_("Status Code"),
        help_text=_("Unique code for the status (e.g., 'AVAILABLE', 'IN_USE', 'REPAIR')")
    )
    
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_("Status Name"),
        help_text=_("Human-readable name for the status")
    )
    
    description = models.TextField(
        blank=True,
        verbose_name=_("Description"),
        help_text=_("Detailed description of what this status means")
    )
    
    status_type = models.CharField(
        max_length=20,
        choices=STATUS_TYPES,
        default='operational',
        verbose_name=_("Status Type"),
        help_text=_("Category of this status")
    )
    
    color_code = models.CharField(
        max_length=7,
        choices=COLOR_CHOICES,
        default='#4CAF50',
        validators=[RegexValidator(
            regex=r'^#[0-9A-Fa-f]{6}$',
            message=_("Color code must be a valid hex color (e.g., #FF0000)")
        )],
        verbose_name=_("Color Code"),
        help_text=_("Hex color code for displaying this status")
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("Whether this status is available for use")
    )

    is_default = models.BooleanField(
        default=False,
        verbose_name=_("Default Status"),
        help_text=_("Whether this is the default status for new items")
    )

    allows_checkout = models.BooleanField(
        default=True,
        verbose_name=_("Allows Checkout"),
        help_text=_("Whether items with this status can be checked out")
    )

    requires_approval = models.BooleanField(
        default=False,
        verbose_name=_("Requires Approval"),
        help_text=_("Whether changing to this status requires approval")
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Created At"),
        help_text=_("Date and time when the record was created")
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_("Updated At"),
        help_text=_("Date and time when the record was last updated")
    )

    class Meta:
        verbose_name = _("Item Status")
        verbose_name_plural = _("Item Statuses")
        db_table = 'inventory_item_status'
        ordering = ['name']
        indexes = [
            models.Index(fields=['code'], name='inv_item_status_code_idx'),
            models.Index(fields=['is_active'], name='inv_item_status_active_idx'),
            models.Index(fields=['status_type'], name='inv_item_status_type_idx'),
        ]

    def __str__(self):
        return f"{self.name} ({self.code})"

    def save(self, *args, **kwargs):
        # Ensure only one default status
        if self.is_default:
            ItemStatus.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)

    @property
    def usage_count(self):
        """Count of items using this status"""
        # This will be implemented when item model is created
        return 0

    @property
    def display_name(self):
        """Display name with code"""
        return f"{self.name} ({self.code})"

    def clean(self):
        """Validate the model"""
        from django.core.exceptions import ValidationError
        
        if self.code:
            self.code = self.code.upper()
        
        # Validate color code format
        if self.color_code and not self.color_code.startswith('#'):
            raise ValidationError({'color_code': _("Color code must start with #")})

    @classmethod
    def get_default_status(cls):
        """Get the default status"""
        return cls.objects.filter(is_default=True, is_active=True).first()

    @classmethod
    def create_default_statuses(cls):
        """Create default statuses if they don't exist"""
        default_statuses = [
            {
                'code': 'AVAILABLE',
                'name': 'Available',
                'description': 'Item is available for use or checkout',
                'status_type': 'operational',
                'color_code': '#4CAF50',
                'is_default': True,
                'allows_checkout': True
            },
            {
                'code': 'IN_USE',
                'name': 'In Use',
                'description': 'Item is currently being used',
                'status_type': 'operational',
                'color_code': '#2196F3',
                'allows_checkout': False
            },
            {
                'code': 'MAINTENANCE',
                'name': 'Under Maintenance',
                'description': 'Item is undergoing maintenance or repair',
                'status_type': 'maintenance',
                'color_code': '#FF9800',
                'allows_checkout': False
            },
            {
                'code': 'REPAIR',
                'name': 'Under Repair',
                'description': 'Item is being repaired',
                'status_type': 'maintenance',
                'color_code': '#F44336',
                'allows_checkout': False,
                'requires_approval': True
            },
            {
                'code': 'LOST',
                'name': 'Lost',
                'description': 'Item has been lost or misplaced',
                'status_type': 'administrative',
                'color_code': '#9C27B0',
                'allows_checkout': False,
                'requires_approval': True
            },
            {
                'code': 'DISPOSED',
                'name': 'Disposed',
                'description': 'Item has been disposed of',
                'status_type': 'disposal',
                'color_code': '#607D8B',
                'allows_checkout': False,
                'requires_approval': True
            }
        ]
        
        for status_data in default_statuses:
            cls.objects.get_or_create(
                code=status_data['code'],
                defaults=status_data
            )


