"""
Item Tag Model for Inventory Setup
"""
import uuid
from django.db import models
from django.utils.text import slugify
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _


class ItemTag(models.Model):
    """
    Model for item tags used to categorize items by special characteristics
    like sensitive, very small, building materials, disposal methods, etc.
    """

    TAG_TYPES = [
        ('sensitivity', 'Sensitivity Level'),
        ('size', 'Size Category'),
        ('material', 'Material Type'),
        ('disposal', 'Disposal Method'),
        ('handling', 'Handling Requirements'),
        ('storage', 'Storage Requirements'),
        ('security', 'Security Level'),
        ('environmental', 'Environmental Impact'),
        ('maintenance', 'Maintenance Category'),
        ('quality', 'Quality Indicator'),
        ('location', 'Location Specific'),
        ('other', 'Other'),
    ]

    TAG_COLORS = [
        ('#F44336', 'Red - Critical/High Priority'),
        ('#FF9800', 'Orange - Warning/Medium Priority'),
        ('#FFEB3B', 'Yellow - Caution/Low Priority'),
        ('#4CAF50', 'Green - Safe/Good'),
        ('#2196F3', 'Blue - Information/Standard'),
        ('#9C27B0', 'Purple - Special/Premium'),
        ('#E91E63', 'Pink - Attention/Featured'),
        ('#795548', 'Brown - Material/Physical'),
        ('#607D8B', 'Gray - Neutral/Inactive'),
        ('#424242', 'Black - Critical/Restricted'),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text=_("Unique identifier for the item tag")
    )

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_("Tag Name"),
        help_text=_("Name of the tag (e.g., 'Sensitive', 'Very Small', 'Building Material')")
    )

    slug = models.SlugField(
        max_length=100,
        unique=True,
        verbose_name=_("Slug"),
        help_text=_("URL-friendly version of the tag name")
    )

    tag_type = models.CharField(
        max_length=20,
        choices=TAG_TYPES,
        default='other',
        verbose_name=_("Tag Type"),
        help_text=_("Category of this tag")
    )

    description = models.TextField(
        blank=True,
        verbose_name=_("Description"),
        help_text=_("Detailed description of what this tag represents")
    )

    color_code = models.CharField(
        max_length=7,
        choices=TAG_COLORS,
        default='#2196F3',
        validators=[RegexValidator(
            regex=r'^#[0-9A-Fa-f]{6}$',
            message=_("Color code must be a valid hex color (e.g., #FF0000)")
        )],
        verbose_name=_("Color Code"),
        help_text=_("Hex color code for displaying this tag")
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("Whether this tag is available for use")
    )

    is_system_tag = models.BooleanField(
        default=False,
        verbose_name=_("System Tag"),
        help_text=_("Whether this is a system-defined tag that cannot be deleted")
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Created At"),
        help_text=_("Date and time when the record was created")
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_("Updated At"),
        help_text=_("Date and time when the record was last updated")
    )

    class Meta:
        verbose_name = _("Item Tag")
        verbose_name_plural = _("Item Tags")
        db_table = 'inventory_item_tag'
        ordering = ['name']
        indexes = [
            models.Index(fields=['slug'], name='inv_item_tag_slug_idx'),
            models.Index(fields=['tag_type'], name='inv_item_tag_type_idx'),
            models.Index(fields=['is_active'], name='inv_item_tag_active_idx'),
        ]

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    @property
    def usage_count(self):
        """Count of items using this tag"""
        # This will be implemented when item model is created
        return 0

    @property
    def display_name(self):
        """Display name for the tag"""
        return self.name

    def clean(self):
        """Validate the model"""
        from django.core.exceptions import ValidationError
        
        # Validate color code format
        if self.color_code and not self.color_code.startswith('#'):
            raise ValidationError({'color_code': _("Color code must start with #")})

    @classmethod
    def create_predefined_tags(cls):
        """Create predefined tags if they don't exist"""
        predefined_tags = [
            {
                'name': 'Sensitive',
                'slug': 'sensitive',
                'tag_type': 'sensitivity',
                'description': 'Items that require special handling due to sensitivity',
                'color_code': '#F44336',
                'is_system_tag': True
            },
            {
                'name': 'Very Small',
                'slug': 'very-small',
                'tag_type': 'size',
                'description': 'Items that are very small and easy to lose',
                'color_code': '#FF9800',
                'is_system_tag': True
            },
            {
                'name': 'Building Material',
                'slug': 'building-material',
                'tag_type': 'material',
                'description': 'Construction and building materials',
                'color_code': '#795548',
                'is_system_tag': True
            },
            {
                'name': 'Dispose by Fire',
                'slug': 'dispose-by-fire',
                'tag_type': 'disposal',
                'description': 'Items that must be disposed of by burning',
                'color_code': '#F44336',
                'is_system_tag': True
            },
            {
                'name': 'Fragile',
                'slug': 'fragile',
                'tag_type': 'handling',
                'description': 'Items that require careful handling',
                'color_code': '#FFEB3B',
                'is_system_tag': True
            },
            {
                'name': 'Hazardous',
                'slug': 'hazardous',
                'tag_type': 'environmental',
                'description': 'Items that pose environmental or health risks',
                'color_code': '#F44336',
                'is_system_tag': True
            },
            {
                'name': 'Cold Storage',
                'slug': 'cold-storage',
                'tag_type': 'storage',
                'description': 'Items requiring refrigerated storage',
                'color_code': '#2196F3',
                'is_system_tag': True
            },
            {
                'name': 'High Value',
                'slug': 'high-value',
                'tag_type': 'security',
                'description': 'High-value items requiring extra security',
                'color_code': '#9C27B0',
                'is_system_tag': True
            },
            {
                'name': 'Requires Calibration',
                'slug': 'requires-calibration',
                'tag_type': 'maintenance',
                'description': 'Items that require regular calibration',
                'color_code': '#FF9800',
                'is_system_tag': True
            },
            {
                'name': 'Consumable',
                'slug': 'consumable',
                'tag_type': 'other',
                'description': 'Items that are consumed during use',
                'color_code': '#4CAF50',
                'is_system_tag': True
            }
        ]
        
        for tag_data in predefined_tags:
            cls.objects.get_or_create(
                slug=tag_data['slug'],
                defaults=tag_data
            )


